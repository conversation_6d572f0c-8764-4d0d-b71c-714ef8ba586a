package android.taobao.windvane.extra.uc.WVUCCoreProvider$1;
import tb.yt4;
import android.taobao.windvane.extra.uc.WVUCCoreProvider;
import android.taobao.windvane.extra.uc.WVUCCoreProvider$WVUCCoreProviderCallback;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.au4;
import com.android.alibaba.ip.runtime.IpChange;

public class WVUCCoreProvider$1 extends yt4	// class@000238 from classes.dex
{
    public final WVUCCoreProvider this$0;
    public final WVUCCoreProvider$WVUCCoreProviderCallback val$callback;
    public static IpChange $ipChange;

    public void WVUCCoreProvider$1(WVUCCoreProvider p0,WVUCCoreProvider$WVUCCoreProviderCallback p1){
       this.this$0 = p0;
       this.val$callback = p1;
       super();
    }
    public static Object ipc$super(WVUCCoreProvider$1 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/WVUCCoreProvider$1");
    }
    public void onUCCoreInitFailed(au4 p0){
       WVUCCoreProvider$1 tval$callbac;
       IpChange $ipChange = WVUCCoreProvider$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("764065fe", objArray);
          return;
       }else if((tval$callbac = this.val$callback) != null){
          tval$callbac.onUCCoreFailed(p0);
       }
       return;
    }
    public void onUCCorePrepared(){
       WVUCCoreProvider$1 tval$callbac;
       IpChange $ipChange = WVUCCoreProvider$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("6e1aa650", objArray);
          return;
       }else if((tval$callbac = this.val$callback) != null){
          tval$callbac.onUCCorePrepared();
       }
       return;
    }
}
