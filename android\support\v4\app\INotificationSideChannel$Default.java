package android.support.v4.app.INotificationSideChannel$Default;
import android.support.v4.app.INotificationSideChannel;
import java.lang.Object;
import android.os.IBinder;
import java.lang.String;
import android.app.Notification;

public class INotificationSideChannel$Default implements INotificationSideChannel	// class@000127 from classes.dex
{

    public void INotificationSideChannel$Default(){
       super();
    }
    public IBinder asBinder(){
       return null;
    }
    public void cancel(String p0,int p1,String p2){
    }
    public void cancelAll(String p0){
    }
    public void notify(String p0,int p1,String p2,Notification p3){
    }
}
