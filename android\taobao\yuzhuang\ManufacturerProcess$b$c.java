package android.taobao.yuzhuang.ManufacturerProcess$b$c;
import tb.t2o;
import java.lang.Object;
import java.io.File;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.yuzhuang.ManufacturerProcess$b$c$a;
import java.io.FilenameFilter;

public class ManufacturerProcess$b$c	// class@000320 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x30c0002a);
       t2o.a(0x30c00029);
    }
    public void ManufacturerProcess$b$c(){
       super();
    }
    public File a(File p0){
       File[] uFileArray;
       int i = 0;
       IpChange $ipChange = ManufacturerProcess$b$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("d956d48d", objArray);
       }else if(p0.exists() && p0.isDirectory()){
          if ((uFileArray = p0.listFiles()) == null) {
             return null;
          }else {
             int len = uFileArray.length;
             while (i < len) {
                object oobject = uFileArray[i];
                File uFile = (oobject.isDirectory())? this.a(oobject): this.b(oobject, new ManufacturerProcess$b$c$a(this));
                if (uFile != null) {
                   return uFile;
                }
                i = i + 1;
             }
          }
       }
       return null;
    }
    public File b(File p0,FilenameFilter p1){
       IpChange $ipChange = ManufacturerProcess$b$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("b2bf0b54", objArray);
       }else if(p1.accept(p0, p0.getName())){
          return p0;
       }else {
          return null;
       }
    }
}
