package androidx.activity.compose.BackHandlerKt$BackHandler$2$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.OnBackPressedDispatcher;
import androidx.lifecycle.LifecycleOwner;
import androidx.activity.compose.BackHandlerKt$BackHandler$backCallback$1$1;
import java.lang.Object;
import tb.hi20;
import tb.gi20;
import androidx.activity.OnBackPressedCallback;
import androidx.activity.compose.BackHandlerKt$BackHandler$2$1$invoke$$inlined$onDispose$1;

public final class BackHandlerKt$BackHandler$2$1 extends Lambda implements g1a	// class@00047e from classes.dex
{
    public final BackHandlerKt$BackHandler$backCallback$1$1 $backCallback;
    public final OnBackPressedDispatcher $backDispatcher;
    public final LifecycleOwner $lifecycleOwner;

    public void BackHandlerKt$BackHandler$2$1(OnBackPressedDispatcher p0,LifecycleOwner p1,BackHandlerKt$BackHandler$backCallback$1$1 p2){
       this.$backDispatcher = p0;
       this.$lifecycleOwner = p1;
       this.$backCallback = p2;
       super(1);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final gi20 invoke(hi20 p0){
       this.$backDispatcher.addCallback(this.$lifecycleOwner, this.$backCallback);
       return new BackHandlerKt$BackHandler$2$1$invoke$$inlined$onDispose$1(this.$backCallback);
    }
}
