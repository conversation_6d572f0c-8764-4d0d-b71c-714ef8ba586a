package android.taobao.windvane.extra.uc.APIContextHelper$2;
import tb.s2d;
import java.lang.Object;
import com.alibaba.ability.result.ExecuteResult;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public final class APIContextHelper$2 implements s2d	// class@0001f8 from classes.dex
{
    public static IpChange $ipChange;

    public void APIContextHelper$2(){
       super();
    }
    public void onCallback(ExecuteResult p0){
       IpChange $ipChange = APIContextHelper$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f183ed74", objArray);
       }
       return;
    }
}
