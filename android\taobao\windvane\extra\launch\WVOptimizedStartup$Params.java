package android.taobao.windvane.extra.launch.WVOptimizedStartup$Params;
import tb.t2o;
import android.app.Application;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class WVOptimizedStartup$Params	// class@0001b8 from classes.dex
{
    private final Application application;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000ee);
    }
    public void WVOptimizedStartup$Params(Application p0){
       super();
       this.application = p0;
    }
    public static Application access$100(WVOptimizedStartup$Params p0){
       IpChange $ipChange = WVOptimizedStartup$Params.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.application;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("40fe6a5a", objArray);
    }
}
