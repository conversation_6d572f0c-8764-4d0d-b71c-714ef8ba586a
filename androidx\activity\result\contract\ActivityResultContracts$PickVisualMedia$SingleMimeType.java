package androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$SingleMimeType;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VisualMediaType;
import java.lang.String;
import java.lang.Object;
import tb.ckf;

public final class ActivityResultContracts$PickVisualMedia$SingleMimeType implements ActivityResultContracts$PickVisualMedia$VisualMediaType	// class@0004d4 from classes.dex
{
    private final String mimeType;

    public void ActivityResultContracts$PickVisualMedia$SingleMimeType(String p0){
       ckf.g(p0, "mimeType");
       super();
       this.mimeType = p0;
    }
    public final String getMimeType(){
       return this.mimeType;
    }
}
