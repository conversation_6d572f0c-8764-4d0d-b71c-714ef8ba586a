package android.support.v4.app.INotificationSideChannel$Stub;
import android.support.v4.app.INotificationSideChannel;
import android.os.Binder;
import android.os.IInterface;
import java.lang.String;
import android.os.IBinder;
import android.support.v4.app.INotificationSideChannel$Stub$Proxy;
import android.os.Parcel;
import android.app.Notification;
import android.os.Parcelable$Creator;
import java.lang.Object;
import android.support.v4.app.INotificationSideChannel$_Parcel;

public abstract class INotificationSideChannel$Stub extends Binder implements INotificationSideChannel	// class@000129 from classes.dex
{
    static final int TRANSACTION_cancel = 2;
    static final int TRANSACTION_cancelAll = 3;
    static final int TRANSACTION_notify = 1;

    public void INotificationSideChannel$Stub(){
       super();
       this.attachInterface(this, INotificationSideChannel.DESCRIPTOR);
    }
    public static INotificationSideChannel asInterface(IBinder p0){
       IInterface iInterface;
       if (p0 == null) {
          return null;
       }
       if ((iInterface = p0.queryLocalInterface(INotificationSideChannel.DESCRIPTOR)) != null && iInterface instanceof INotificationSideChannel) {
          return iInterface;
       }
       return new INotificationSideChannel$Stub$Proxy(p0);
    }
    public IBinder asBinder(){
       return this;
    }
    public boolean onTransact(int p0,Parcel p1,Parcel p2,int p3){
       String dESCRIPTOR = INotificationSideChannel.DESCRIPTOR;
       if (p0 >= 1 && p0 <= 0xffffff) {
          p1.enforceInterface(dESCRIPTOR);
       }
       if (p0 == 0x5f4e5446) {
          p2.writeString(dESCRIPTOR);
          return 1;
       }else if(p0 != 1){
          if (p0 != 2) {
             if (p0 != 3) {
                return super.onTransact(p0, p1, p2, p3);
             }else {
                this.cancelAll(p1.readString());
             }
          }else {
             this.cancel(p1.readString(), p1.readInt(), p1.readString());
          }
       }else {
          this.notify(p1.readString(), p1.readInt(), p1.readString(), INotificationSideChannel$_Parcel.access$000(p1, Notification.CREATOR));
       }
       return 1;
    }
}
