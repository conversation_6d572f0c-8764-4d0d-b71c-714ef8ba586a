package android.taobao.windvane.extra.uc.interfaces.EventHandler;
import java.lang.String;
import java.lang.Object;
import java.util.Map;
import android.taobao.windvane.extra.uc.interfaces.IRequest;

public interface abstract EventHandler	// class@000271 from classes.dex
{

    void data(byte[] p0,int p1);
    void endData();
    void error(int p0,String p1);
    int getResourceType();
    void headers(Object p0);
    void headers(Map p0);
    boolean isSynchronous();
    void setRequest(IRequest p0);
    void setResourceType(int p0);
    void status(int p0,int p1,int p2,String p3);
}
