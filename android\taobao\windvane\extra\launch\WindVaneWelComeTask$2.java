package android.taobao.windvane.extra.launch.WindVaneWelComeTask$2;
import java.lang.Runnable;
import android.taobao.windvane.extra.launch.WindVaneWelComeTask;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.config.EnvEnum;
import android.taobao.windvane.WindVaneSDK;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.v7t;

public class WindVaneWelComeTask$2 implements Runnable	// class@0001c4 from classes.dex
{
    public final WindVaneWelComeTask this$0;
    public static IpChange $ipChange;

    public void WindVaneWelComeTask$2(WindVaneWelComeTask p0){
       this.this$0 = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = WindVaneWelComeTask$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          WindVaneSDK.setEnvMode(EnvEnum.ONLINE);
          return;
       }
    }
}
