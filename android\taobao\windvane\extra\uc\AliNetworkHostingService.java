package android.taobao.windvane.extra.uc.AliNetworkHostingService;
import com.uc.webview.export.extension.INetworkHostingService;
import tb.t2o;
import android.content.Context;
import android.taobao.windvane.extra.uc.AliNetworkDecider;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.Object;
import java.lang.String;
import java.lang.Number;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.lang.Boolean;
import com.uc.webview.export.WebView;
import com.uc.webview.export.extension.INetworkHostingService$ITransaction;
import android.taobao.windvane.extra.uc.AliNetworkHostingService$NetworkTransaction;

public class AliNetworkHostingService extends INetworkHostingService	// class@000209 from classes.dex
{
    private final AliNetworkDecider mAliDecider;
    private final Context mContext;
    public static IpChange $ipChange;
    private static final int NETWORK_TYPE_TAOBAO_NET;
    private static final String NETWORK_VERSION;
    public static final int NET_ERROR_FALLBACK;
    private static final String TAG;

    static {
       t2o.a(0x3d800136);
    }
    public void AliNetworkHostingService(Context p0){
       super();
       this.mContext = p0;
       this.mAliDecider = new AliNetworkDecider();
    }
    public static int access$000(int p0){
       IpChange $ipChange = AliNetworkHostingService.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return AliNetworkHostingService.convertToOldRequestType(p0);
       }
       Object[] objArray = new Object[]{new Integer(p0)};
       return $ipChange.ipc$dispatch("ddeb5a5f", objArray).intValue();
    }
    public static Context access$100(AliNetworkHostingService p0){
       IpChange $ipChange = AliNetworkHostingService.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mContext;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("dcdf8878", objArray);
    }
    private static int convertToOldRequestType(int p0){
       IpChange $ipChange = AliNetworkHostingService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Integer(p0)};
          return $ipChange.ipc$dispatch("3281905f", objArray).intValue();
       }else {
          int i = 3;
          int i1 = 2;
          if (p0 == i1) {
             return i;
          }
          if (p0 == i) {
             return 4;
          }
          i = 6;
          if (p0 == 4) {
             return i;
          }
          if (p0 != i) {
             i1 = 13;
             if (p0 != 12) {
                if (p0 != i1) {
                   return p0;
                }else {
                   return 14;
                }
             }
          }
          return i1;
       }
    }
    public static Object ipc$super(AliNetworkHostingService p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/AliNetworkHostingService");
    }
    private boolean shouldUseTaobaoNetwork(String p0){
       int i = 1;
       int i1 = 2;
       IpChange $ipChange = AliNetworkHostingService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("16ab0441", objArray).booleanValue();
       }else if(i1 == this.mAliDecider.chooseNetwork(p0)){
          i = false;
       }
       return i;
    }
    public INetworkHostingService$ITransaction createTransaction(int p0,String p1,WebView p2){
       AliNetworkHostingService$NetworkTransaction networkTrans;
       IpChange $ipChange = AliNetworkHostingService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1,p2};
          return $ipChange.ipc$dispatch("af3ec3f0", objArray);
       }else if(this.shouldUseTaobaoNetwork(p1)){
          networkTrans = new AliNetworkHostingService$NetworkTransaction(this, p2, p0, p1);
       }else {
          networkTrans = null;
       }
       return networkTrans;
    }
    public int type(){
       IpChange $ipChange = AliNetworkHostingService.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return 2;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("d79de6b4", objArray).intValue();
    }
    public String version(){
       IpChange $ipChange = AliNetworkHostingService.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "*******";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("deb50921", objArray);
    }
}
