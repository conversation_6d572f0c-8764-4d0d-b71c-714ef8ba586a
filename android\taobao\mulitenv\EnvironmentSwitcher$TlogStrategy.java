package android.taobao.mulitenv.EnvironmentSwitcher$TlogStrategy;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class EnvironmentSwitcher$TlogStrategy extends Enum	// class@000142 from classes.dex
{
    private static final EnvironmentSwitcher$TlogStrategy[] $VALUES;
    public static IpChange $ipChange;
    public static final EnvironmentSwitcher$TlogStrategy DISABLE_DEGRADE;
    public static final EnvironmentSwitcher$TlogStrategy ENABLE_DEGRADE;

    static {
       EnvironmentSwitcher$TlogStrategy tlogStrategy = new EnvironmentSwitcher$TlogStrategy("ENABLE_DEGRADE", 0);
       EnvironmentSwitcher$TlogStrategy.ENABLE_DEGRADE = tlogStrategy;
       EnvironmentSwitcher$TlogStrategy tlogStrategy1 = new EnvironmentSwitcher$TlogStrategy("DISABLE_DEGRADE", 1);
       EnvironmentSwitcher$TlogStrategy.DISABLE_DEGRADE = tlogStrategy1;
       EnvironmentSwitcher$TlogStrategy[] tlogStrategy2 = new EnvironmentSwitcher$TlogStrategy[]{tlogStrategy,tlogStrategy1};
       EnvironmentSwitcher$TlogStrategy.$VALUES = tlogStrategy2;
    }
    private void EnvironmentSwitcher$TlogStrategy(String p0,int p1){
       super(p0, p1);
    }
    public static Object ipc$super(EnvironmentSwitcher$TlogStrategy p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/mulitenv/EnvironmentSwitcher$TlogStrategy");
    }
    public static EnvironmentSwitcher$TlogStrategy valueOf(String p0){
       IpChange $ipChange = EnvironmentSwitcher$TlogStrategy.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(EnvironmentSwitcher$TlogStrategy.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("5531ebc9", objArray);
    }
    public static EnvironmentSwitcher$TlogStrategy[] values(){
       IpChange $ipChange = EnvironmentSwitcher$TlogStrategy.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return EnvironmentSwitcher$TlogStrategy.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("4344f4fa", objArray);
    }
}
