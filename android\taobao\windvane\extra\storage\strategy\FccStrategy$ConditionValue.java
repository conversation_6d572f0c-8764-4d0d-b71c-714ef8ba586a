package android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValue;
import tb.t2o;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValueType;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValueRange;

public class FccStrategy$ConditionValue	// class@0001f2 from classes.dex
{
    public Float valueNumber;
    public FccStrategy$ConditionValueRange valueRange;
    public String valueString;
    public FccStrategy$ConditionValueType valueType;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800128);
    }
    public void FccStrategy$ConditionValue(FccStrategy$ConditionValueType p0){
       super();
       this.valueType = p0;
    }
    public boolean checkValid(){
       FccStrategy$ConditionValue tvalueType;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = FccStrategy$ConditionValue.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("2b6d1a5f", objArray).booleanValue();
       }else if((tvalueType = this.valueType) == FccStrategy$ConditionValueType.STRING){
          return (TextUtils.isEmpty(this.valueString) ^ i1);
       }else if(tvalueType == FccStrategy$ConditionValueType.NUMBER){
          if (this.valueNumber != null) {
             i = true;
          }
          return i;
       }else if(tvalueType == FccStrategy$ConditionValueType.RANGE && ((tvalueType = this.valueRange) != null && tvalueType.checkValid())){
          i = true;
       }
       return i;
    }
}
