package android.taobao.mulitenv.EnvironmentSwitcher$EnvType;
import java.lang.Enum;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.Number;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.lang.Class;

public final class EnvironmentSwitcher$EnvType extends Enum	// class@00013e from classes.dex
{
    private final int value;
    private static final EnvironmentSwitcher$EnvType[] $VALUES;
    public static IpChange $ipChange;
    public static final EnvironmentSwitcher$EnvType OnLINE;
    public static final EnvironmentSwitcher$EnvType PRE;
    public static final EnvironmentSwitcher$EnvType TEST;
    public static final EnvironmentSwitcher$EnvType TEST_2;

    static {
       EnvironmentSwitcher$EnvType uEnvType = new EnvironmentSwitcher$EnvType("OnLINE", 0, 0);
       EnvironmentSwitcher$EnvType.OnLINE = uEnvType;
       EnvironmentSwitcher$EnvType uEnvType1 = new EnvironmentSwitcher$EnvType("PRE", 1, 1);
       EnvironmentSwitcher$EnvType.PRE = uEnvType1;
       EnvironmentSwitcher$EnvType uEnvType2 = new EnvironmentSwitcher$EnvType("TEST", 2, 2);
       EnvironmentSwitcher$EnvType.TEST = uEnvType2;
       EnvironmentSwitcher$EnvType uEnvType3 = new EnvironmentSwitcher$EnvType("TEST_2", 3, 3);
       EnvironmentSwitcher$EnvType.TEST_2 = uEnvType3;
       EnvironmentSwitcher$EnvType[] uEnvTypeArra = new EnvironmentSwitcher$EnvType[]{uEnvType,uEnvType1,uEnvType2,uEnvType3};
       EnvironmentSwitcher$EnvType.$VALUES = uEnvTypeArra;
    }
    private void EnvironmentSwitcher$EnvType(String p0,int p1,int p2){
       super(p0, p1);
       this.value = p2;
    }
    public static int access$000(EnvironmentSwitcher$EnvType p0){
       IpChange $ipChange = EnvironmentSwitcher$EnvType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.value;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("a55f8516", objArray).intValue();
    }
    public static Object ipc$super(EnvironmentSwitcher$EnvType p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/mulitenv/EnvironmentSwitcher$EnvType");
    }
    public static EnvironmentSwitcher$EnvType valueOf(String p0){
       IpChange $ipChange = EnvironmentSwitcher$EnvType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(EnvironmentSwitcher$EnvType.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("c5bf7fa3", objArray);
    }
    public static EnvironmentSwitcher$EnvType[] values(){
       IpChange $ipChange = EnvironmentSwitcher$EnvType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return EnvironmentSwitcher$EnvType.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("8265bbd2", objArray);
    }
    public int getValue(){
       IpChange $ipChange = EnvironmentSwitcher$EnvType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.value;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("d1b766b5", objArray).intValue();
    }
}
