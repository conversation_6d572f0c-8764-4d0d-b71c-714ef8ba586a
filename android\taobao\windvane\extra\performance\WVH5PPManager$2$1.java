package android.taobao.windvane.extra.performance.WVH5PPManager$2$1;
import tb.esd;
import android.taobao.windvane.extra.performance.WVH5PPManager$2;
import com.alibaba.fastjson.JSONObject;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Long;

public class WVH5PPManager$2$1 implements esd	// class@0001cc from classes.dex
{
    public final WVH5PPManager$2 this$1;
    public final JSONObject val$maxDurationUploadInfo;
    public static IpChange $ipChange;

    public void WVH5PPManager$2$1(WVH5PPManager$2 p0,JSONObject p1){
       this.this$1 = p0;
       this.val$maxDurationUploadInfo = p1;
       super();
    }
    public void recordProperty(String p0,Object p1){
       IpChange $ipChange = WVH5PPManager$2$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4faa9773", objArray);
          return;
       }else {
          this.val$maxDurationUploadInfo.put(p0, p1);
          return;
       }
    }
    public void recordStage(String p0,long p1){
       IpChange $ipChange = WVH5PPManager$2$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("6a661a86", objArray);
          return;
       }else {
          this.val$maxDurationUploadInfo.put(p0, Long.valueOf(p1));
          return;
       }
    }
}
