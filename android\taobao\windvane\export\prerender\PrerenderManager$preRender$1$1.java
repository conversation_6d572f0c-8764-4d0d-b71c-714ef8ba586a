package android.taobao.windvane.export.prerender.PrerenderManager$preRender$1$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import android.taobao.windvane.export.prerender.PrerenderManager$preRender$1;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.wum;
import tb.xhv;
import com.android.alibaba.ip.runtime.IpChange;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;
import java.lang.Boolean;
import android.taobao.windvane.export.prerender.PrerenderManager$preRender$1$1$1;
import tb.d1a;
import com.taobao.themis.kernel.utils.CommonExtKt;

public final class PrerenderManager$preRender$1$1 extends Lambda implements g1a	// class@000179 from classes.dex
{
    public final PrerenderManager$preRender$1 this$0;
    public static IpChange $ipChange;

    public void PrerenderManager$preRender$1$1(PrerenderManager$preRender$1 p0){
       this.this$0 = p0;
       super(1);
    }
    public static Object ipc$super(PrerenderManager$preRender$1$1 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/export/prerender/PrerenderManager$preRender$1$1");
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(wum p0){
       IpChange $ipChange = PrerenderManager$preRender$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("aa982eb0", objArray);
          return;
       }else if(p0 == null){
          lcn.f(RVLLevel.Error, "Themis/Performance/Prerender", "failed to create prerender webview");
          this.this$0.$callback.invoke(Boolean.FALSE);
          return;
       }else {
          CommonExtKt.o(new PrerenderManager$preRender$1$1$1(this, p0));
          return;
       }
    }
}
