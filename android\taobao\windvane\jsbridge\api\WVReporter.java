package android.taobao.windvane.jsbridge.api.WVReporter;
import tb.kpw;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import org.json.JSONObject;
import android.taobao.windvane.webview.IWVWebView;
import java.util.Iterator;
import tb.csw;
import tb.trw;
import java.lang.Throwable;
import tb.hqw;
import tb.vpw;
import tb.wpw;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;

public class WVReporter extends kpw	// class@0002e5 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800240);
    }
    public void WVReporter(){
       super();
    }
    public static Object ipc$super(WVReporter p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/jsbridge/api/WVReporter");
    }
    public boolean execute(String p0,String p1,WVCallBackContext p2){
       IpChange $ipChange = WVReporter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("bcd41fd1", objArray).booleanValue();
       }else if("reportError".equals(p0)){
          this.reportError(p2, p1);
       }else if("reportDomLoad".equals(p0)){
          this.reportDomLoad(p2, p1);
       }else {
          return 0;
       }
       return 1;
    }
    public synchronized void reportDomLoad(WVCallBackContext p0,String p1){
       IpChange $ipChange = WVReporter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("e4145382", objArray);
          return;
       }else {
          try{
             JSONObject jSONObject = new JSONObject(p1);
             p1 = p0.getWebview().getUrl();
             long l = jSONObject.optLong("time", 0);
             long l1 = jSONObject.optLong("firstByte", 0);
             Iterator iterator = jSONObject.keys();
             while (iterator.hasNext()) {
                String str = iterator.next();
                if (!str.startsWith("self_")) {
                   continue ;
                }else {
                   long l2 = jSONObject.optLong(str);
                   if (trw.getPerformanceMonitor() != null) {
                      trw.getPerformanceMonitor().didPageOccurSelfDefinedEvent(p1, str.substring(5), l2);
                   }
                }
             }
             if (trw.getPerformanceMonitor() != null) {
                trw.getPerformanceMonitor().didPageDomLoadAtTime(p1, l);
                trw.getPerformanceMonitor().didPageReceiveFirstByteAtTime(p1, l1);
             }
             p0.success();
             return;
          }catch(org.json.JSONException e12){
             e12.printStackTrace();
             return;
          }
       }
    }
    public synchronized void reportError(WVCallBackContext p0,String p1){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = WVReporter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("51534342", objArray);
          return;
       }else {
          try{
             JSONObject jSONObject = new JSONObject(p1);
             String url = p0.getWebview().getUrl();
             if (trw.getErrorMonitor() != null) {
                trw.getErrorMonitor().didOccurJSError(url, jSONObject.optString("msg"), jSONObject.optString("file"), jSONObject.optString("line"));
             }
             if (this.mWebView == null) {
                i = 0;
             }
             if ((i & vpw.commonConfig.c1)) {
                lcn.a(RVLLevel.Error, "JS/Log").j("log").m(p0.getPid()).a("content", p1).f();
             }
             p0.success();
             return;
          }catch(org.json.JSONException e9){
             e9.printStackTrace();
             return;
          }
       }
    }
}
