package android.taobao.windvane.extra.performance.action.AddPropertyIfAbsentVisitor;
import android.taobao.windvane.extra.performance.action.AddPropertyVisitor;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.cce;
import com.android.alibaba.ip.runtime.IpChange;

public class AddPropertyIfAbsentVisitor extends AddPropertyVisitor	// class@0001d1 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800106);
    }
    public void AddPropertyIfAbsentVisitor(String p0,Object p1){
       super(p0, p1);
    }
    public static Object ipc$super(AddPropertyIfAbsentVisitor p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/performance/action/AddPropertyIfAbsentVisitor");
    }
    public void accept(cce p0){
       IpChange $ipChange = AddPropertyIfAbsentVisitor.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("987066e1", objArray);
          return;
       }else {
          p0.onPropertyIfAbsent(this.name, this.value);
          return;
       }
    }
}
