package android.taobao.yuzhuang.ManufacturerProcess$Config;
import java.lang.Enum;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import android.taobao.yuzhuang.ManufacturerProcess;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.content.Context;
import android.os.Build$VERSION;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.io.File;
import java.lang.Class;
import java.lang.Boolean;

public final class ManufacturerProcess$Config extends Enum	// class@00031b from classes.dex
{
    private String apkConfig;
    private String channelConfig;
    private String manufacturer;
    private static final ManufacturerProcess$Config[] $VALUES;
    public static IpChange $ipChange;
    public static final ManufacturerProcess$Config BLEPHONE;
    public static final ManufacturerProcess$Config HONOR;
    private static final String HONOR_CHANNEL_KEY;
    public static final ManufacturerProcess$Config HUAWEI;
    private static final String HUAWEI_CHANNEL_KEY;
    public static final ManufacturerProcess$Config ONEPLUS;
    public static final ManufacturerProcess$Config OPPO;
    private static final String OPPO_CHANNEL_KEY;
    private static final String OPPO_CHANNEL_KEY_2;
    public static final ManufacturerProcess$Config REALME;
    public static final ManufacturerProcess$Config SMARTISAN;
    public static final ManufacturerProcess$Config SUGAR;
    public static final ManufacturerProcess$Config VIVO;
    public static final ManufacturerProcess$Config XIAOMI;
    public static final ManufacturerProcess$Config ZTE;

    static {
       ManufacturerProcess$Config v6 = new ManufacturerProcess$Config("OPPO", 0, "oppo", "", "/data/etc/appchannel/sjtbconfig.xml");
       ManufacturerProcess$Config.OPPO = v6;
       v6 = new ManufacturerProcess$Config("ONEPLUS", 1, "oneplus", "", "/data/etc/appchannel/sjtbconfig.xml");
       ManufacturerProcess$Config.ONEPLUS = v6;
       ManufacturerProcess$Config v1 = new ManufacturerProcess$Config("REALME", 2, "realme", "", "/data/etc/appchannel/sjtbconfig.xml");
       ManufacturerProcess$Config.REALME = v1;
       ManufacturerProcess$Config v2 = new ManufacturerProcess$Config("ZTE", 3, "zte", "/system", "/system/etc/sjtbconfig.xml");
       ManufacturerProcess$Config.ZTE = v2;
       v1 = new ManufacturerProcess$Config("VIVO", 4, "vivo", "/system/vivo-apps", "/system/etc/sjtbconfig.xml");
       ManufacturerProcess$Config.VIVO = v3;
       v2 = new ManufacturerProcess$Config("SMARTISAN", 5, "smartisan", "/system/media/app", "/system/etc/sjtbconfig.xml");
       ManufacturerProcess$Config.SMARTISAN = v4;
       v1 = new ManufacturerProcess$Config("XIAOMI", 6, "xiaomi", "", "");
       ManufacturerProcess$Config.XIAOMI = v5;
       v2 = new ManufacturerProcess$Config("BLEPHONE", 7, "blephone", "", "/system/etc/sjtbconfig.ini");
       ManufacturerProcess$Config.BLEPHONE = v1;
       v2 = new ManufacturerProcess$Config("HUAWEI", 8, "huawei", "", "");
       ManufacturerProcess$Config.HUAWEI = v2;
       ManufacturerProcess$Config v8 = new ManufacturerProcess$Config("HONOR", 9, "honor", "", "");
       ManufacturerProcess$Config.HONOR = v8;
       ManufacturerProcess$Config v9 = new ManufacturerProcess$Config("SUGAR", 10, "sugar", "", "/system/etc/sjtbconfig.channel.ini");
       ManufacturerProcess$Config.SUGAR = v9;
       ManufacturerProcess$Config[] uConfigArray = new ManufacturerProcess$Config[11];
       uConfigArray[0] = v6;
       uConfigArray[1] = v6;
       uConfigArray[2] = v1;
       uConfigArray[3] = v2;
       uConfigArray[4] = v3;
       uConfigArray[5] = v4;
       uConfigArray[6] = v5;
       uConfigArray[7] = v1;
       uConfigArray[8] = v2;
       uConfigArray[9] = v8;
       uConfigArray[10] = v9;
       ManufacturerProcess$Config.$VALUES = uConfigArray;
    }
    private void ManufacturerProcess$Config(String p0,int p1,String p2,String p3,String p4){
       super(p0, p1);
       this.manufacturer = p2;
       this.apkConfig = p3;
       this.channelConfig = p4;
    }
    public static String[] getConfig(String p0){
       String[] stringArray;
       ManufacturerProcess$Config hUAWEI;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = ManufacturerProcess$Config.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("8993fa58", objArray);
       }else {
          ManufacturerProcess$Config[] uConfigArray = ManufacturerProcess$Config.values();
          int len = uConfigArray.length;
          int i2 = 0;
          while (true) {
             if (i2 < len) {
                object oobject = uConfigArray[i2];
                if (oobject.manufacturer.equals(p0.toLowerCase())) {
                   stringArray = new String[]{oobject.apkConfig,oobject.channelConfig};
                label_003a :
                   if (stringArray == null && ManufacturerProcess.isHuaweiPhone()) {
                      stringArray = new String[]{hUAWEI.apkConfig,hUAWEI.channelConfig};
                      hUAWEI = ManufacturerProcess$Config.HUAWEI;
                      break ;
                   }
                   break ;
                }else {
                   i2 = i2 + i1;
                }
             }else {
                stringArray = null;
                goto label_003a ;
             }
          }
          return stringArray;
       }
    }
    public static Object ipc$super(ManufacturerProcess$Config p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/yuzhuang/ManufacturerProcess$Config");
    }
    public static String transformSpecificChannelConfig(String p0,Context p1,String p2){
       IpChange $ipChange = ManufacturerProcess$Config.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2};
          return $ipChange.ipc$dispatch("52db34a6", objArray);
       }else if(ManufacturerProcess$Config.XIAOMI.isManufacturerMatched(p0)){
          return ManufacturerProcess.access$000(p1.getPackageName());
       }else if(ManufacturerProcess.isHuaweiPhone()){
          return ManufacturerProcess.access$100("ro.channelId.taobao");
       }else if(ManufacturerProcess$Config.HONOR.isManufacturerMatched(p0)){
          return ManufacturerProcess.access$100("ro.channelId.taobao");
       }else if(!ManufacturerProcess$Config.OPPO.isManufacturerMatched(p0) && (!ManufacturerProcess$Config.ONEPLUS.isManufacturerMatched(p0) && (!ManufacturerProcess$Config.REALME.isManufacturerMatched(p0) || Build$VERSION.SDK_INT < 30))){
          p0 = ManufacturerProcess.access$100("ro.preinstall.path");
          if (!TextUtils.isEmpty(p0)) {
             return new File(p0, "sjtbconfig.xml").getAbsolutePath();
          }
       }
       return p2;
    }
    public static ManufacturerProcess$Config valueOf(String p0){
       IpChange $ipChange = ManufacturerProcess$Config.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(ManufacturerProcess$Config.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("4ead39c5", objArray);
    }
    public static ManufacturerProcess$Config[] values(){
       IpChange $ipChange = ManufacturerProcess$Config.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return ManufacturerProcess$Config.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("65473bb6", objArray);
    }
    public boolean isManufacturerMatched(String p0){
       int i = 0;
       IpChange $ipChange = ManufacturerProcess$Config.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("df52c45e", objArray).booleanValue();
       }else if(p0 != null && p0.length()){
          return p0.equalsIgnoreCase(this.manufacturer);
       }else {
          return i;
       }
    }
}
