package android.taobao.mulitenv.EnvironmentSwitcher$SpdyStrategy;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class EnvironmentSwitcher$SpdyStrategy extends Enum	// class@000141 from classes.dex
{
    private static final EnvironmentSwitcher$SpdyStrategy[] $VALUES;
    public static IpChange $ipChange;
    public static final EnvironmentSwitcher$SpdyStrategy DISABLE_DEGRADE;
    public static final EnvironmentSwitcher$SpdyStrategy ENABLE_DEGRADE;

    static {
       EnvironmentSwitcher$SpdyStrategy spdyStrategy = new EnvironmentSwitcher$SpdyStrategy("ENABLE_DEGRADE", 0);
       EnvironmentSwitcher$SpdyStrategy.ENABLE_DEGRADE = spdyStrategy;
       EnvironmentSwitcher$SpdyStrategy spdyStrategy1 = new EnvironmentSwitcher$SpdyStrategy("DISABLE_DEGRADE", 1);
       EnvironmentSwitcher$SpdyStrategy.DISABLE_DEGRADE = spdyStrategy1;
       EnvironmentSwitcher$SpdyStrategy[] spdyStrategy2 = new EnvironmentSwitcher$SpdyStrategy[]{spdyStrategy,spdyStrategy1};
       EnvironmentSwitcher$SpdyStrategy.$VALUES = spdyStrategy2;
    }
    private void EnvironmentSwitcher$SpdyStrategy(String p0,int p1){
       super(p0, p1);
    }
    public static Object ipc$super(EnvironmentSwitcher$SpdyStrategy p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/mulitenv/EnvironmentSwitcher$SpdyStrategy");
    }
    public static EnvironmentSwitcher$SpdyStrategy valueOf(String p0){
       IpChange $ipChange = EnvironmentSwitcher$SpdyStrategy.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(EnvironmentSwitcher$SpdyStrategy.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("4ccbc0a7", objArray);
    }
    public static EnvironmentSwitcher$SpdyStrategy[] values(){
       IpChange $ipChange = EnvironmentSwitcher$SpdyStrategy.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return EnvironmentSwitcher$SpdyStrategy.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("3adec9d8", objArray);
    }
}
