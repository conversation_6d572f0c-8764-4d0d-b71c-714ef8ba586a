package android.taobao.windvane.extra.performance2.WVWPData;
import tb.t2o;
import tb.og8;
import android.taobao.windvane.webview.IWVWebView;
import java.lang.Object;
import java.lang.ref.WeakReference;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import java.lang.ref.Reference;
import android.taobao.windvane.extra.performance2.IPerformance;
import java.lang.Throwable;
import tb.x74;
import tb.v7t;
import java.lang.StringBuilder;
import java.lang.Integer;

public class WVWPData	// class@0001e2 from classes.dex
{
    private int blockHtml;
    private int blockJs;
    private String historyStatus;
    public String htmlError;
    public String initGpuType;
    public String initRenderType;
    public String jsError;
    public String jsErrorCode;
    public String pageCurrentStatus;
    public String probableReason;
    public String progress;
    public String realGpuType;
    public String realRenderType;
    public String stack;
    public String t2;
    public long timeLoadurl;
    public boolean ucBkpg;
    public WeakReference webviewWeakRef;
    public static IpChange $ipChange;
    private static final boolean DEBUG;
    public static final String REQUEST_HTML;
    public static final String REQUEST_JS;
    public static final String RESPONSE_HTML;
    public static final String RESPONSE_JS;
    private static final String TAG;

    static {
       t2o.a(0x3d800117);
       WVWPData.DEBUG = og8.b();
    }
    public void WVWPData(IWVWebView p0){
       super();
       this.pageCurrentStatus = "";
       this.t2 = "0";
       this.realRenderType = "0";
       this.realGpuType = "0";
       this.initRenderType = "0";
       this.initGpuType = "0";
       this.progress = "0";
       this.probableReason = "";
       this.htmlError = "";
       this.jsError = "";
       this.stack = "";
       this.jsErrorCode = "";
       this.blockHtml = 0;
       this.blockJs = 0;
       this.historyStatus = "";
       this.webviewWeakRef = new WeakReference(p0);
    }
    private boolean isPreInit(){
       WVWPData twebviewWeak;
       int i = 0;
       IpChange $ipChange = WVWPData.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("2b7f41d4", objArray).booleanValue();
       }else if((twebviewWeak = this.webviewWeakRef) == null){
          return i;
       }else {
          IWVWebView iWVWebView = twebviewWeak.get();
          if (iWVWebView instanceof IPerformance) {
             return iWVWebView.isPreInit();
          }
          return i;
       }
    }
    public void addProbableReason(String p0){
       IpChange $ipChange = WVWPData.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("ab69ffe5", objArray);
          return;
       }else if(!this.isPreInit()){
          this.probableReason = this.probableReason+p0+";";
       }else {
          v7t.d("WVWPData", "isPreInit, abort addProbableReason "+p0);
       }
       return;
    }
    public void setHtmlError(int p0,String p1){
       IpChange $ipChange = WVWPData.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("442264eb", objArray);
          return;
       }else {
          String str = String.valueOf(p0);
          if (!this.isPreInit()) {
             this.htmlError = str;
             v7t.d("WVWPData", "setHtmlError "+str+" ,url:"+p1);
          }else {
             v7t.d("WVWPData", "isPreInit, abort setHtmlError "+str+" ,url:"+p1);
          }
          return;
       }
    }
    public void setJsErrorCode(int p0,String p1){
       IpChange $ipChange = WVWPData.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("78345fdc", objArray);
          return;
       }else {
          String str = String.valueOf(p0);
          if (!this.isPreInit()) {
             this.jsErrorCode = str;
             v7t.d("WVWPData", "jsErrorCode "+str+" ,url:"+p1);
          }else {
             v7t.d("WVWPData", "isPreInit, abort set jsErrorCode "+str+" ,url:"+p1);
          }
          return;
       }
    }
    public void setPageCurrentStatus(String p0){
       int i = 1;
       IpChange $ipChange = WVWPData.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("86e9ef4b", objArray);
          return;
       }else if(WVWPData.DEBUG){
          this.historyStatus = this.historyStatus+p0+";";
       }
       if ("responseHtml".equals(p0)) {
          this.blockHtml = this.blockHtml - i;
       }
       if ("responseJs".equals(p0)) {
          this.blockJs = this.blockJs - i;
       }
       if ("requestHtml".equals(p0)) {
          this.blockHtml = this.blockHtml + i;
          this.pageCurrentStatus = p0;
       }
       if ("requestJs".equals(p0)) {
          this.blockJs = this.blockJs + i;
          this.pageCurrentStatus = p0;
       }
       if (this.blockHtml <= null && this.blockJs <= null) {
          this.pageCurrentStatus = p0;
       }
       return;
    }
    public String toString(){
       IpChange $ipChange = WVWPData.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("8126d80d", objArray);
       }else {
          StringBuilder str = "WVWPData{ucBkpg="+this.ucBkpg+",pageCurrentStatus="+this.pageCurrentStatus+",probableReason="+this.probableReason+",htmlError="+this.htmlError+",jsErrorCode="+this.jsErrorCode+",jsError="+this.jsError+",stack="+this.stack+",t2="+this.t2+",realRenderType="+this.realRenderType+",initRenderType="+this.initRenderType+",realGpuType="+this.realGpuType+",initGpuType="+this.initGpuType+",progress="+this.progress;
          if (WVWPData.DEBUG) {
             str = str+",historyStatus="+this.historyStatus;
          }
          return str+"}";
       }
    }
}
