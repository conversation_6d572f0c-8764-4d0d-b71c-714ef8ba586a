package android.taobao.windvane.extra.uc.WVUCWebChromeClient$2;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.WVUCWebChromeClient;
import android.webkit.ValueCallback;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.v7t;

public class WVUCWebChromeClient$2 implements Runnable	// class@000244 from classes.dex
{
    public final WVUCWebChromeClient this$0;
    public final ValueCallback val$callback;
    public static IpChange $ipChange;

    public void WVUCWebChromeClient$2(WVUCWebChromeClient p0,ValueCallback p1){
       this.this$0 = p0;
       this.val$callback = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = WVUCWebChromeClient$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          v7t.a("WVUCWebChromeClient", " openFileChooser permission granted");
          WVUCWebChromeClient.access$001(this.this$0, this.val$callback);
          return;
       }
    }
}
