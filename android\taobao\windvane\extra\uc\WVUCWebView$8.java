package android.taobao.windvane.extra.uc.WVUCWebView$8;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class WVUCWebView$8 implements Runnable	// class@00025b from classes.dex
{
    public final WVUCWebView this$0;
    public static IpChange $ipChange;

    public void WVUCWebView$8(WVUCWebView p0){
       this.this$0 = p0;
       super();
    }
    public void run(){
       int i = 1;
       IpChange $ipChange = WVUCWebView$8.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          WVUCWebView.access$1001(this.this$0);
          WVUCWebView.access$1102(this.this$0, i);
          return;
       }
    }
}
