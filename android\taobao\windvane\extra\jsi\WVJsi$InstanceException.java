package android.taobao.windvane.extra.jsi.WVJsi$InstanceException;
import java.lang.RuntimeException;
import tb.t2o;
import java.lang.String;
import android.taobao.windvane.extra.jsi.WVJsi$1;
import java.lang.Throwable;

public class WVJsi$InstanceException extends RuntimeException	// class@0001af from classes.dex
{

    static {
       t2o.a(0x3d8000e5);
    }
    private void WVJsi$InstanceException(String p0){
       super(p0);
    }
    public void WVJsi$InstanceException(String p0,WVJsi$1 p1){
       super(p0);
    }
    private void WVJsi$InstanceException(String p0,Throwable p1){
       super(p0, p1);
    }
    public void WVJsi$InstanceException(String p0,Throwable p1,WVJsi$1 p2){
       super(p0, p1);
    }
    public void WVJsi$InstanceException(Throwable p0){
       super(p0);
    }
}
