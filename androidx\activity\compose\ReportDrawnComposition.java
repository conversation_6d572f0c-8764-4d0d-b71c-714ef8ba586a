package androidx.activity.compose.ReportDrawnComposition;
import tb.d1a;
import androidx.activity.FullyDrawnReporter;
import java.lang.Object;
import androidx.compose.runtime.snapshots.SnapshotStateObserver;
import androidx.activity.compose.ReportDrawnComposition$snapshotStateObserver$1;
import tb.g1a;
import androidx.activity.compose.ReportDrawnComposition$checkReporter$1;
import kotlin.jvm.internal.Ref$BooleanRef;
import androidx.activity.compose.ReportDrawnComposition$observeReporter$1;
import tb.xhv;

public final class ReportDrawnComposition implements d1a	// class@000496 from classes.dex
{
    private final g1a checkReporter;
    private final FullyDrawnReporter fullyDrawnReporter;
    private final d1a predicate;
    private final SnapshotStateObserver snapshotStateObserver;

    public void ReportDrawnComposition(FullyDrawnReporter p0,d1a p1){
       super();
       this.fullyDrawnReporter = p0;
       this.predicate = p1;
       SnapshotStateObserver snapshotStat = new SnapshotStateObserver(ReportDrawnComposition$snapshotStateObserver$1.INSTANCE);
       snapshotStat.s();
       this.snapshotStateObserver = snapshotStat;
       this.checkReporter = new ReportDrawnComposition$checkReporter$1(this);
       p0.addOnReportDrawnListener(this);
       if (!p0.isFullyDrawnReported()) {
          p0.addReporter();
          this.observeReporter(p1);
       }
       return;
    }
    public static final void access$observeReporter(ReportDrawnComposition p0,d1a p1){
       p0.observeReporter(p1);
    }
    private final void observeReporter(d1a p0){
       Ref$BooleanRef uBooleanRef = new Ref$BooleanRef();
       this.snapshotStateObserver.o(p0, this.checkReporter, new ReportDrawnComposition$observeReporter$1(uBooleanRef, p0));
       if (uBooleanRef.element != null) {
          this.removeReporter();
       }
       return;
    }
    public Object invoke(){
       this.invoke();
       return xhv.INSTANCE;
    }
    public void invoke(){
       this.snapshotStateObserver.j();
       this.snapshotStateObserver.t();
    }
    public final void removeReporter(){
       this.snapshotStateObserver.k(this.predicate);
       if (!this.fullyDrawnReporter.isFullyDrawnReported()) {
          this.fullyDrawnReporter.removeReporter();
       }
       this.invoke();
       return;
    }
}
