package androidx.activity.contextaware.ContextAware;
import androidx.activity.contextaware.OnContextAvailableListener;
import android.content.Context;

public interface abstract ContextAware	// class@0004a2 from classes.dex
{

    void addOnContextAvailableListener(OnContextAvailableListener p0);
    Context peekAvailableContext();
    void removeOnContextAvailableListener(OnContextAvailableListener p0);
}
