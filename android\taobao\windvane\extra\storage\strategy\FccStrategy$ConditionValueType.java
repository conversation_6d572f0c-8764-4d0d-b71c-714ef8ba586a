package android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValueType;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class FccStrategy$ConditionValueType extends Enum	// class@0001f4 from classes.dex
{
    private static final FccStrategy$ConditionValueType[] $VALUES;
    public static IpChange $ipChange;
    public static final FccStrategy$ConditionValueType NUMBER;
    public static final FccStrategy$ConditionValueType RANGE;
    public static final FccStrategy$ConditionValueType STRING;

    static {
       FccStrategy$ConditionValueType uConditionVa = new FccStrategy$ConditionValueType("STRING", 0);
       FccStrategy$ConditionValueType.STRING = uConditionVa;
       FccStrategy$ConditionValueType uConditionVa1 = new FccStrategy$ConditionValueType("RANGE", 1);
       FccStrategy$ConditionValueType.RANGE = uConditionVa1;
       FccStrategy$ConditionValueType uConditionVa2 = new FccStrategy$ConditionValueType("NUMBER", 2);
       FccStrategy$ConditionValueType.NUMBER = uConditionVa2;
       FccStrategy$ConditionValueType[] uConditionVa3 = new FccStrategy$ConditionValueType[]{uConditionVa,uConditionVa1,uConditionVa2};
       FccStrategy$ConditionValueType.$VALUES = uConditionVa3;
    }
    private void FccStrategy$ConditionValueType(String p0,int p1){
       super(p0, p1);
    }
    public static Object ipc$super(FccStrategy$ConditionValueType p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/storage/strategy/FccStrategy$ConditionValueType");
    }
    public static FccStrategy$ConditionValueType valueOf(String p0){
       IpChange $ipChange = FccStrategy$ConditionValueType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(FccStrategy$ConditionValueType.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("5af1b90a", objArray);
    }
    public static FccStrategy$ConditionValueType[] values(){
       IpChange $ipChange = FccStrategy$ConditionValueType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return FccStrategy$ConditionValueType.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("79e70879", objArray);
    }
}
