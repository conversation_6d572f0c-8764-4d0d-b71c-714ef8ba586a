package android.taobao.windvane.export.network.RequestCache;
import android.taobao.windvane.export.network.RequestCallback;
import tb.t2o;
import android.taobao.windvane.export.network.Request;
import java.util.ArrayList;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;
import java.util.Iterator;
import android.taobao.windvane.export.network.RequestCache$a;
import java.lang.Integer;
import java.util.Map;
import android.taobao.windvane.export.network.b;

public class RequestCache extends RequestCallback	// class@00016c from classes.dex
{
    public boolean a;
    public final List b;
    public boolean c;
    public RequestCallback d;
    public final Request e;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d80008f);
    }
    public void RequestCache(Request p0){
       super();
       this.a = false;
       this.b = new ArrayList();
       this.c = false;
       this.e = p0;
    }
    public static Object ipc$super(RequestCache p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/export/network/RequestCache");
    }
    public final void a(){
       IpChange $ipChange = RequestCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("d0f7d54b", objArray);
          return;
       }else {
          this.b.clear();
          return;
       }
    }
    public boolean b(RequestCallback p0){
       RequestCache$a uoa;
       int i2;
       object oobject;
       object oobject1;
       int i = 1;
       int i1 = 2;
       IpChange $ipChange = RequestCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("1728205a", objArray).booleanValue();
       }else {
          _monitor_enter(this);
          if (this.c != null) {
             lcn.f(RVLLevel.Error, "Themis/Performance/RequestPrefetch", "RequestCache has been consumed.");
             _monitor_exit(this);
             return 0;
          }else {
             this.c = i;
             this.d = p0;
             Iterator iterator = this.b.iterator();
             while (iterator.hasNext()) {
                if ((uoa = iterator.next()) == null) {
                   continue ;
                }else {
                   RequestCache$a a = uoa.a;
                   Map map = null;
                   if (a != null && 4 != a) {
                      if (i == a) {
                         this.onReceiveData(uoa.b[0]);
                      }else if(i1 == a){
                         i2 = uoa.b[0].intValue();
                         if ((oobject1 = uoa.b[i]) != null) {
                            map = oobject1;
                         }
                         this.onError(i2, map);
                      }else if(3 == a){
                         this.onFinish();
                      }else if(5 == a && ((a = uoa.b) != null && a.length >= i)){
                         i2 = a[0].intValue();
                         uoa = uoa.b;
                         if (uoa.length > i && (oobject1 = uoa[i]) != null) {
                            map = oobject1;
                         }
                         this.onCustomCallback(i2, map);
                      }
                   }else {
                      i2 = uoa.b[0].intValue();
                      if ((oobject = uoa.b[i]) != null) {
                         map = oobject;
                      }
                      if (uoa.a == null) {
                         this.onResponse(i2, map);
                      }else {
                         this.onNetworkResponse(i2, map);
                      }
                   }
                }
             }
             _monitor_exit(this);
             return i;
          }
       }
    }
    public Request c(){
       IpChange $ipChange = RequestCache.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.e;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("a4d10a0e", objArray);
    }
    public boolean d(){
       IpChange $ipChange = RequestCache.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("16fe733a", objArray).booleanValue();
    }
    public void e(boolean p0){
       IpChange $ipChange = RequestCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("1906c91c", objArray);
          return;
       }else {
          this.a = p0;
          return;
       }
    }
    public void onCustomCallback(int p0,Object[] p1){
       RequestCache td;
       int i = 2;
       int i1 = 3;
       IpChange $ipChange = RequestCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[1] = new Integer(p0);
          objArray[i] = p1;
          $ipChange.ipc$dispatch("c344bdea", objArray);
          return;
       }else {
          _monitor_enter(this);
          if (this.c != null) {
             if (p0 == i1) {
                this.c = false;
                b.l(this);
                this.a();
                _monitor_exit(this);
                return;
             }else if((td = this.d) != null){
                td.onCustomCallback(p0, p1);
             }
          }else if(p0 == i1){
             this.a();
             _monitor_exit(this);
             return;
          }else {
             Object[] objArray1 = new Object[i];
             objArray1[0] = Integer.valueOf(p0);
             objArray1[1] = p1;
             this.b.add(new RequestCache$a(5, objArray1));
          }
          _monitor_exit(this);
          return;
       }
    }
    public void onError(int p0,String p1){
       RequestCache td;
       int i = 1;
       IpChange $ipChange = RequestCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("7a671c9d", objArray);
          return;
       }else {
          _monitor_enter(this);
          if (this.c != null) {
             if ((td = this.d) != null) {
                td.onError(p0, p1);
             }
             this.a();
          }else {
             Object[] objArray1 = new Object[]{Integer.valueOf(p0),p1};
             this.b.add(new RequestCache$a(2, objArray1));
          }
          _monitor_exit(this);
          return;
       }
    }
    public void onFinish(){
       RequestCache td;
       int i = 0;
       IpChange $ipChange = RequestCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("badeed9", objArray);
          return;
       }else {
          _monitor_enter(this);
          if (this.c != null) {
             if ((td = this.d) != null) {
                td.onFinish();
             }
             this.a();
          }else {
             Object[] objArray1 = new Object[i];
             this.b.add(new RequestCache$a(3, objArray1));
          }
          _monitor_exit(this);
          return;
       }
    }
    public void onNetworkResponse(int p0,Map p1){
       RequestCache td;
       int i = 2;
       IpChange $ipChange = RequestCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("92d71559", objArray);
          return;
       }else {
          _monitor_enter(this);
          if (this.c != null) {
             if ((td = this.d) != null) {
                td.onNetworkResponse(p0, p1);
             }
          }else {
             Object[] objArray1 = new Object[i];
             objArray1[0] = Integer.valueOf(p0);
             objArray1[1] = p1;
             this.b.add(new RequestCache$a(4, objArray1));
          }
          _monitor_exit(this);
          return;
       }
    }
    public void onReceiveData(byte[] p0){
       Object[] objArray;
       RequestCache td;
       int i = 0;
       IpChange $ipChange = RequestCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f24c16dc", objArray);
          return;
       }else {
          _monitor_enter(this);
          if (this.c != null) {
             if ((td = this.d) != null) {
                td.onReceiveData(p0);
             }
          }else {
             objArray = new Object[]{p0};
             this.b.add(new RequestCache$a(1, objArray));
          }
          _monitor_exit(this);
          return;
       }
    }
    public void onResponse(int p0,Map p1){
       RequestCache td;
       int i = 2;
       IpChange $ipChange = RequestCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("bb214fe9", objArray);
          return;
       }else {
          _monitor_enter(this);
          if (this.c != null) {
             if ((td = this.d) != null) {
                td.onResponse(p0, p1);
             }
          }else {
             Object[] objArray1 = new Object[i];
             objArray1[0] = Integer.valueOf(p0);
             objArray1[1] = p1;
             this.b.add(new RequestCache$a(0, objArray1));
          }
          _monitor_exit(this);
          return;
       }
    }
}
