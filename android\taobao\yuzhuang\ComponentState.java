package android.taobao.yuzhuang.ComponentState;
import java.io.Serializable;
import tb.t2o;
import java.lang.Object;
import android.content.Context;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import tb.xc4;
import android.app.Application;
import java.util.HashMap;

public class ComponentState implements Serializable	// class@00031a from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x30c00021);
    }
    public void ComponentState(){
       super();
    }
    public static boolean hasRecovered(Context p0){
       IpChange $ipChange = ComponentState.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return xc4.a(p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("412bca3c", objArray).booleanValue();
    }
    public void init(Application p0,HashMap p1){
       IpChange $ipChange = ComponentState.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("dddb138b", objArray);
          return;
       }else if(!ComponentState.hasRecovered(p0)){
          xc4.e(p0, p1);
          xc4.d(p0);
       }
       return;
    }
}
