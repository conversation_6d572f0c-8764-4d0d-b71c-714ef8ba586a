package android.taobao.windvane.extra.performance2.WVUserPPManager;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;

public class WVUserPPManager	// class@0001e1 from classes.dex
{
    private long FP;
    private long TTI;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800116);
    }
    public void WVUserPPManager(){
       super();
    }
    public String jsCodeForUserPP(){
       IpChange $ipChange = WVUserPPManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "javascript:\(function\(\){var observer=new PerformanceObserver\(function\(list,obj\){for\(var entry of list.getEntries\(\)\){if\(entry.entryType==\'paint\'&&entry.name==\'first-paint\'\){console.log\(\'hybrid://WVPerformance:FP/receiveFPTime?{\"time\":\'+entry.startTime+\'}\'\)}if\(entry.entryType==\'longtask\'\){console.log\(\'hybrid://WVPerformance:TTI/receiveTTITime?{\"time\":\'+\(entry.startTime+entry.duration\)+\'}\'\)}}}\);observer.observe\({entryTypes:[\'longtask\',\'paint\']}\)}\)\(\)";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("dae6debb", objArray);
    }
}
