package android.taobao.windvane.extra.jsi.WVJsi$InstanceResult;
import tb.t2o;
import com.alibaba.jsi.standard.JSEngine;
import java.lang.Exception;
import java.lang.Object;
import android.taobao.windvane.extra.jsi.WVJsi$1;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;

public class WVJsi$InstanceResult	// class@0001b0 from classes.dex
{
    private final Exception exception;
    private final JSEngine jsEngine;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000e6);
    }
    private void WVJsi$InstanceResult(JSEngine p0,Exception p1){
       super();
       this.jsEngine = p0;
       this.exception = p1;
    }
    public void WVJsi$InstanceResult(JSEngine p0,Exception p1,WVJsi$1 p2){
       super(p0, p1);
    }
    public Exception getException(){
       IpChange $ipChange = WVJsi$InstanceResult.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.exception;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9ad20156", objArray);
    }
    public JSEngine getJsEngine(){
       IpChange $ipChange = WVJsi$InstanceResult.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.jsEngine;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8d6c6b4c", objArray);
    }
    public boolean isSuccess(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = WVJsi$InstanceResult.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("6049a784", objArray).booleanValue();
       }else if(this.jsEngine != null){
          i = true;
       }
       return i;
    }
}
