package android.taobao.windvane.extra.core.WVCore;
import tb.t2o;
import java.lang.Object;
import java.util.concurrent.atomic.AtomicBoolean;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import tb.vpw;
import tb.wpw;
import java.util.List;
import java.util.Arrays;
import android.os.Build;
import android.os.Build$VERSION;
import java.io.File;
import android.content.Context;
import tb.vc9;
import java.lang.StringBuilder;
import tb.sb9;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Long;
import java.lang.System;
import java.lang.Throwable;
import tb.v7t;
import tb.x74;
import android.taobao.windvane.extra.uc.UCCoreStartup;
import com.uc.webview.export.extension.U4Engine$Extractor;
import com.uc.webview.export.extension.U4Engine;
import java.lang.Number;
import tb.yaa;
import android.taobao.windvane.extra.uc.remotefetch.WVUCRemoteFetcher;
import android.taobao.windvane.extra.uc.WVUCWebView;
import tb.zt4;
import android.app.Application;
import android.taobao.windvane.extra.uc.WVCoreSettings;
import tb.yt4;
import com.uc.webview.export.WebView;
import java.lang.Integer;
import java.nio.ByteBuffer;

public class WVCore	// class@00018e from classes.dex
{
    private boolean isUCSDKSupport;
    private final AtomicBoolean isUcStartInit;
    private int usedGpuMulti;
    private int usedWebMulti;
    public static IpChange $ipChange;
    public static final String TAG;
    private static WVCore instance;

    static {
       t2o.a(0x3d8000c3);
    }
    public void WVCore(){
       super();
       this.usedWebMulti = 0;
       this.usedGpuMulti = 0;
       this.isUcStartInit = new AtomicBoolean(0);
       this.isUCSDKSupport = false;
    }
    private boolean checkRemoteIsolateOpen(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("c71c2a52", objArray).booleanValue();
       }else {
          wpw commonConfig = vpw.commonConfig;
          List list = Arrays.asList(commonConfig.w);
          List list1 = Arrays.asList(commonConfig.u);
          if (!Arrays.asList(commonConfig.v).contains(Build.BRAND) && (!list.contains(Build.MODEL) && !list1.contains(Build$VERSION.RELEASE))) {
             i = true;
          }
          return i;
       }
    }
    public static WVCore getInstance(){
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("66f31fd8", objArray);
       }else if(WVCore.instance == null){
          WVCore wVCore = WVCore.class;
          _monitor_enter(wVCore);
          if (WVCore.instance == null) {
             WVCore.instance = new WVCore();
          }
          _monitor_exit(wVCore);
       }
       return WVCore.instance;
    }
    private String getUcSoPath(String p0){
       File[] uFileArray;
       int i = 0;
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("33ec4c4a", objArray);
       }else if((uFileArray = new File(p0).listFiles()) != null){
          int len = uFileArray.length;
          while (i < len) {
             object oobject = uFileArray[i];
             if (oobject.isDirectory()) {
                String ucSoPath = this.getUcSoPath(oobject.getPath());
                if (ucSoPath.endsWith("libwebviewuc.so")) {
                   return ucSoPath;
                }
             }else if(oobject.getName().endsWith("libwebviewuc.so")){
                return oobject.getPath();
             }
             i = i + 1;
          }
       }
       return "";
    }
    public boolean checkIsolateIfOpen(Context p0){
       byte[] uobyteArray;
       boolean b1;
       int i = 1;
       int i1 = 2;
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("7b5da3fb", objArray).booleanValue();
       }else {
          boolean b = this.checkRemoteIsolateOpen();
          if ((uobyteArray = sb9.f(vc9.e(p0, "UCPolicy").getPath()+File.separator+"isolate")) != null) {
             try{
                String str = new String(uobyteArray, "utf-8");
                if (!TextUtils.isEmpty(str)) {
                   String[] stringArray = str.split(",");
                   if (stringArray.length == i1) {
                      if (((System.currentTimeMillis() - Long.valueOf(stringArray[i]).longValue()) - (long)vpw.commonConfig.x) > 0) {
                         this.updateIsolatePolicy(p0, i);
                      }
                      b1 = false;
                   label_0082 :
                      v7t.d("WVCore", "isolate policy: remote=["+b+"], local=["+b1+"]");
                      if (!b || !b1) {
                         i = false;
                      }
                      return i;
                   }
                }
             }catch(java.io.UnsupportedEncodingException e9){
                e9.printStackTrace();
             }
          }
          b1 = true;
          goto label_0082 ;
       }
    }
    public boolean extractWebCoreLibrary(Context p0){
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("761e46bc", objArray).booleanValue();
       }else if(!x74.l(p0)){
          return 0;
       }else {
          U4Engine.createExtractor().setContext(p0.getApplicationContext()).setCompressedFile(new File(UCCoreStartup.ucCore7ZFilePath(p0))).setASync(0).start();
          return 1;
       }
    }
    public int getUsedGpuMulti(){
       IpChange $ipChange = WVCore.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.usedGpuMulti;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("801599ba", objArray).intValue();
    }
    public int getUsedWebMulti(){
       IpChange $ipChange = WVCore.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.usedWebMulti;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("73e8dc12", objArray).intValue();
    }
    public String getV8SoPath(){
       File runningDir;
       String str;
       String str1;
       int i = 0;
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("1259f560", objArray);
       }else {
          Application n = yaa.n;
          if ((runningDir = U4Engine.getRunningDir(n, i)) == null) {
             if (this.innerSo(n)) {
                str = UCCoreStartup.ucCore7ZFilePath(n);
                if (!TextUtils.isEmpty(str)) {
                   runningDir = U4Engine.getExtractDir(n, new File(str));
                }
                str1 = "inner";
             }else if(!TextUtils.isEmpty(WVUCRemoteFetcher.fetchUCRemoteLocal())){
                str = WVUCRemoteFetcher.fetchUCRemoteLocal();
                if (!TextUtils.isEmpty(str)) {
                   runningDir = U4Engine.getExtractDir(n, new File(str));
                }
                str1 = "remoteso";
             }else {
                runningDir = U4Engine.getExtractDirByUrl(n, WVUCWebView.UC_CORE_URL);
                str1 = "download";
             }
          }else {
             str1 = "running";
          }
          String ucSoPath = (runningDir != null)? this.getUcSoPath(runningDir.getAbsolutePath()): "";
          v7t.i("WVCore", "get v8 path by "+str1+", path=["+ucSoPath+"]");
          return ucSoPath;
       }
    }
    public void initUCCore(Context p0,zt4 p1){
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("8e9b91b7", objArray);
          return;
       }else {
          this.initUCCore(p0, null, null, p1);
          return;
       }
    }
    public void initUCCore(Context p0,String[] p1,String p2,zt4 p3){
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          $ipChange.ipc$dispatch("79928836", objArray);
          return;
       }else if(p0 instanceof Application){
          yaa.n = p0;
       }else {
          yaa.n = p0.getApplicationContext();
       }
       if (p3 != null) {
          WVCoreSettings.getInstance().setCoreEventCallback(p3);
       }
       if (p1 != null) {
          yaa.f().q(p1);
       }
       WVUCWebView.initUCCore();
       return;
    }
    public void initUCCore2(Context p0,yt4 p1){
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("ad91342f", objArray);
          return;
       }else if(p0 instanceof Application){
          yaa.n = p0;
       }else {
          yaa.n = p0.getApplicationContext();
       }
       if (WVCore.getInstance().isUCSupport()) {
          if (p1 != null) {
             p1.onUCCorePrepared();
          }
          return;
       }else {
          WVCoreSettings.getInstance().setCoreEventCallback2(p1);
          WVUCWebView.initUCCore(p1);
          return;
       }
    }
    public boolean innerSo(Context p0){
       int i = 1;
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("dd581b13", objArray).booleanValue();
       }else if(p0 == null){
          return 0;
       }else {
          File uFile = new File(UCCoreStartup.ucCore7ZFilePath(p0));
          if (vpw.commonConfig.r != null || !uFile.exists()) {
             i = false;
          }
          return i;
       }
    }
    public boolean isUCInner(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("ff9ac189", objArray).booleanValue();
       }else if(vpw.commonConfig.r == null){
          i = true;
       }
       return i;
    }
    public boolean isUCStartInit(){
       IpChange $ipChange = WVCore.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.isUcStartInit.get();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("a4397dc5", objArray).booleanValue();
    }
    public boolean isUCSupport(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("7b9ec482", objArray).booleanValue();
       }else if(this.isUCSDKSupport != null && WebView.getCoreType() == 3){
          i = true;
       }
       return i;
    }
    public void setUCStartInit(boolean p0){
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("84e32c6b", objArray);
          return;
       }else {
          this.isUcStartInit.set(p0);
          return;
       }
    }
    public void setUCSupport(boolean p0){
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("81d2b4e", objArray);
          return;
       }else {
          this.isUCSDKSupport = p0;
          return;
       }
    }
    public WVCore setUsedGpuMulti(int p0){
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          return $ipChange.ipc$dispatch("f2ed27d6", objArray);
       }else {
          this.usedGpuMulti = p0;
          return this;
       }
    }
    public WVCore setUsedWebMulti(int p0){
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          return $ipChange.ipc$dispatch("eda8de7e", objArray);
       }else {
          this.usedWebMulti = p0;
          return this;
       }
    }
    public void updateIsolatePolicy(Context p0,boolean p1){
       String str = "false,";
       IpChange $ipChange = WVCore.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("bd8d4247", objArray);
          return;
       }else {
          File uFile = vc9.e(p0, "UCPolicy");
          try{
             String str1 = uFile.getPath()+File.separator+"isolate";
             String str2 = (p1)? "": str+System.currentTimeMillis();
             sb9.h(str1, ByteBuffer.wrap(str2.getBytes()));
          }catch(android.taobao.windvane.file.NotEnoughSpace e4){
             e4.printStackTrace();
          }
          return;
       }
    }
}
