package androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$3$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.OnBackPressedDispatcher;
import androidx.lifecycle.LifecycleOwner;
import androidx.activity.compose.PredictiveBackHandlerCallback;
import java.lang.Object;
import tb.hi20;
import tb.gi20;
import androidx.activity.OnBackPressedCallback;
import androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$3$1$invoke$$inlined$onDispose$1;

public final class PredictiveBackHandlerKt$PredictiveBackHandler$3$1 extends Lambda implements g1a	// class@000490 from classes.dex
{
    public final PredictiveBackHandlerCallback $backCallBack;
    public final OnBackPressedDispatcher $backDispatcher;
    public final LifecycleOwner $lifecycleOwner;

    public void PredictiveBackHandlerKt$PredictiveBackHandler$3$1(OnBackPressedDispatcher p0,LifecycleOwner p1,PredictiveBackHandlerCallback p2){
       this.$backDispatcher = p0;
       this.$lifecycleOwner = p1;
       this.$backCallBack = p2;
       super(1);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final gi20 invoke(hi20 p0){
       this.$backDispatcher.addCallback(this.$lifecycleOwner, this.$backCallBack);
       return new PredictiveBackHandlerKt$PredictiveBackHandler$3$1$invoke$$inlined$onDispose$1(this.$backCallBack);
    }
}
