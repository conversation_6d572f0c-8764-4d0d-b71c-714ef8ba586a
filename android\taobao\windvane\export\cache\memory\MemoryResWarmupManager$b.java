package android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$b;
import java.lang.Runnable;
import android.taobao.windvane.export.cache.memory.model.ResourceItemModel;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$d;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$b$a;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager;

public final class MemoryResWarmupManager$b implements Runnable	// class@00015b from classes.dex
{
    public final ResourceItemModel a;
    public final MemoryResWarmupManager$d b;
    public static IpChange $ipChange;

    public void MemoryResWarmupManager$b(ResourceItemModel p0,MemoryResWarmupManager$d p1){
       this.a = p0;
       this.b = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = MemoryResWarmupManager$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          MemoryResWarmupManager.c(this.a, new MemoryResWarmupManager$b$a(this));
          return;
       }
    }
}
