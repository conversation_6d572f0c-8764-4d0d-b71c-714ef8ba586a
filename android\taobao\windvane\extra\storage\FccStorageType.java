package android.taobao.windvane.extra.storage.FccStorageType;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class FccStorageType extends Enum	// class@0001e4 from classes.dex
{
    public final String name;
    private static final FccStorageType[] $VALUES;
    public static IpChange $ipChange;
    public static final FccStorageType CACHE;
    public static final FccStorageType SNAPSHOT;

    static {
       FccStorageType uFccStorageT = new FccStorageType("CACHE", 0, "cache");
       FccStorageType.CACHE = uFccStorageT;
       FccStorageType uFccStorageT1 = new FccStorageType("SNAPSHOT", 1, "snapshot");
       FccStorageType.SNAPSHOT = uFccStorageT1;
       FccStorageType[] uFccStorageT2 = new FccStorageType[]{uFccStorageT,uFccStorageT1};
       FccStorageType.$VALUES = uFccStorageT2;
    }
    private void FccStorageType(String p0,int p1,String p2){
       super(p0, p1);
       this.name = p2;
    }
    public static Object ipc$super(FccStorageType p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/storage/FccStorageType");
    }
    public static FccStorageType valueOf(String p0){
       IpChange $ipChange = FccStorageType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(FccStorageType.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("fd99bea6", objArray);
    }
    public static FccStorageType[] values(){
       IpChange $ipChange = FccStorageType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return FccStorageType.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("d5bb0857", objArray);
    }
}
