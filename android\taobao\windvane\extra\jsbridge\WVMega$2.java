package android.taobao.windvane.extra.jsbridge.WVMega$2;
import tb.s2d;
import android.taobao.windvane.extra.jsbridge.WVMega;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import java.lang.Object;
import com.alibaba.ability.result.ExecuteResult;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.nsw;
import org.json.JSONObject;
import java.util.Map;
import java.lang.Throwable;

public class WVMega$2 implements s2d	// class@0001a5 from classes.dex
{
    public final WVMega this$0;
    public final WVCallBackContext val$callback;
    public static IpChange $ipChange;

    public void WVMega$2(WVMega p0,WVCallBackContext p1){
       this.this$0 = p0;
       this.val$callback = p1;
       super();
    }
    public void onCallback(ExecuteResult p0){
       JSONObject i2;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = WVMega$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f183ed74", objArray);
          return;
       }else if(p0.getStatusCode() != 99){
          i = false;
       }
       nsw onsw = new nsw();
       onsw.i(i);
       try{
          i2 = new JSONObject();
          i2.put("data", new JSONObject(p0.toFormattedData()));
       }catch(org.json.JSONException e6){
          e6.printStackTrace();
       }
       onsw.h(i2);
       this.val$callback.onSuccess(onsw);
       return;
    }
}
