package android.taobao.windvane.webview.WVWebView$b$a;
import java.lang.Runnable;
import android.taobao.windvane.webview.WVWebView$b;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.webview.WVWebView;
import android.os.Handler;

public class WVWebView$b$a implements Runnable	// class@000311 from classes.dex
{
    public final WVWebView$b a;
    public static IpChange $ipChange;

    public void WVWebView$b$a(WVWebView$b p0){
       this.a = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = WVWebView$b$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          this.a.a.mHandler.sendEmptyMessage(405);
          return;
       }
    }
}
