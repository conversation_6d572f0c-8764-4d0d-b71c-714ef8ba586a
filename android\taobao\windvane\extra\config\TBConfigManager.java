package android.taobao.windvane.extra.config.TBConfigManager;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.sb9;
import java.lang.Throwable;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.StringBuilder;
import tb.v7t;
import tb.yaa;
import java.lang.Integer;
import com.taobao.orange.OrangeConfig;
import tb.bsw;
import tb.asw;
import android.content.Context;
import android.taobao.windvane.extra.config.TBConfigListenerV1;
import tb.z8l;
import android.taobao.windvane.extra.config.TBConfigManager$2;
import java.io.File;
import tb.vc9;
import android.taobao.windvane.extra.config.TBConfigManager$1;

public class TBConfigManager	// class@00018d from classes.dex
{
    private String configPath;
    private z8l mConfigListenerV1;
    private String useOrange;
    public static IpChange $ipChange;
    public static final String ANDROID_WINDVANE_CONFIG;
    public static final String WINDVANE_COMMMON_CONFIG;
    public static final String WINDVANE_COMMON_CONFIG;
    public static final String WINDVANE_CONFIG;
    public static final String WINDVANE_URL_CONFIG;
    private static TBConfigManager instance;

    static {
       t2o.a(0x3d8000c0);
       TBConfigManager.instance = null;
    }
    public void TBConfigManager(){
       super();
       this.useOrange = "true";
       this.mConfigListenerV1 = null;
       this.configPath = null;
    }
    public static void access$000(TBConfigManager p0){
       IpChange $ipChange = TBConfigManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("1dd2aeb3", objArray);
          return;
       }else {
          p0.setWVConfig();
          return;
       }
    }
    public static String access$100(TBConfigManager p0){
       IpChange $ipChange = TBConfigManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.configPath;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("fc5b73ec", objArray);
    }
    public static TBConfigManager getInstance(){
       IpChange $ipChange = TBConfigManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("ea0f4e80", objArray);
       }else if(TBConfigManager.instance == null){
          TBConfigManager tBConfigMana = TBConfigManager.class;
          _monitor_enter(tBConfigMana);
          if (TBConfigManager.instance == null) {
             TBConfigManager.instance = new TBConfigManager();
          }
          _monitor_exit(tBConfigMana);
       }
       return TBConfigManager.instance;
    }
    private void getLocalConfig(){
       byte[] uobyteArray;
       String[] stringArray;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = TBConfigManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          $ipChange.ipc$dispatch("5be31a5e", objArray);
          return;
       }else if((uobyteArray = sb9.f(this.configPath)) == null){
          return;
       }else {
          CharSequence uCharSequenc = new String(uobyteArray, "utf-8");
       }
    }
    private void setWVConfig(){
       IpChange $ipChange = TBConfigManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("d7f2ec6a", objArray);
          return;
       }else {
          bsw.b().a().m(TextUtils.equals("true", OrangeConfig.getInstance().getConfig("WindVane", "onlyBkpg", "true")));
          bsw.b().a().j(TextUtils.equals("true", OrangeConfig.getInstance().getConfig("WindVane", "closeUCHA", "false")));
          bsw.b().a().l(OrangeConfig.getInstance().getConfig("WindVane", "JSErrorRatio", "1.00"));
          bsw.b().a().n(TextUtils.equals(OrangeConfig.getInstance().getConfig("WindVane", "isOpenAIT", "true"), "true"));
          bsw.b().a().o(TextUtils.equals(OrangeConfig.getInstance().getConfig("WindVane", "isOpenH5PP", "true"), "true"));
          bsw.b().a().q(TextUtils.equals(OrangeConfig.getInstance().getConfig("WindVane", "isOpenUserPP", "true"), "true"));
          bsw.b().a().r(OrangeConfig.getInstance().getConfig("WindVane", "fSPFilterAnimation", "true"));
          bsw.b().a().p(TextUtils.equals(OrangeConfig.getInstance().getConfig("WindVane", "isOpenH5_2", "true"), "true"));
          yaa.m = TextUtils.equals(OrangeConfig.getInstance().getConfig("WindVane", "newFireEvent", "true"), "true");
          bsw.b().a().k(TextUtils.equals(OrangeConfig.getInstance().getConfig("WindVane", "filterIllegalUrl", "true"), "true"));
          v7t.i("WVConfig", "Performance.Config = ["+bsw.b().a().toString()+"]");
          return;
       }
    }
    public void initAfterAWP(Context p0){
       IpChange $ipChange = TBConfigManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("fa03ef33", objArray);
          return;
       }else if(this.mConfigListenerV1 == null){
          String[] stringArray = new String[]{"android_windvane_config","WindVane_common_config","WindVane","WindVane_URL_config"};
          this.mConfigListenerV1 = new TBConfigListenerV1();
          OrangeConfig.getInstance().registerListener(stringArray, this.mConfigListenerV1);
       }
       return;
    }
    public void initConfig(){
       IpChange $ipChange = TBConfigManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("6811c3f9", objArray);
          return;
       }else {
          v7t.i("WVConfig", "use orange config");
          String[] stringArray = new String[]{"windvane_common","windvane_domain","WindVane_URL_config","cookie_black_list","windvane_uc_core"};
          OrangeConfig.getInstance().registerListener(stringArray, new TBConfigManager$2(this));
          return;
       }
    }
    public void initEarly(Context p0){
       IpChange $ipChange = TBConfigManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f370a1ce", objArray);
          return;
       }else {
          this.configPath = vc9.e(p0, "windvane").getPath()+File.separator+"orange";
          File uFile = new File(this.configPath);
          if (!uFile.exists()) {
             try{
                uFile.createNewFile();
             }catch(java.io.IOException e5){
                e5.printStackTrace();
             }
          }
          this.getLocalConfig();
          String[] stringArray = new String[]{"WindVane"};
          OrangeConfig.getInstance().registerListener(stringArray, new TBConfigManager$1(this));
          return;
       }
    }
}
