package android.taobao.windvane.extra.uc.WVUCWebChromeClient$10;
import android.content.DialogInterface$OnClickListener;
import android.taobao.windvane.extra.uc.WVUCWebChromeClient;
import android.widget.EditText;
import com.uc.webview.export.JsPromptResult;
import java.lang.Object;
import android.content.DialogInterface;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import android.text.Editable;

public class WVUCWebChromeClient$10 implements DialogInterface$OnClickListener	// class@000240 from classes.dex
{
    public final WVUCWebChromeClient this$0;
    public final EditText val$editText;
    public final JsPromptResult val$res;
    public static IpChange $ipChange;

    public void WVUCWebChromeClient$10(WVUCWebChromeClient p0,EditText p1,JsPromptResult p2){
       this.this$0 = p0;
       this.val$editText = p1;
       this.val$res = p2;
       super();
    }
    public void onClick(DialogInterface p0,int p1){
       IpChange $ipChange = WVUCWebChromeClient$10.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1)};
          $ipChange.ipc$dispatch("7e49304d", objArray);
          return;
       }else if(this.val$editText.getText() != null){
          this.val$res.confirm(this.val$editText.getText().toString());
       }else {
          this.val$res.confirm();
       }
       return;
    }
}
