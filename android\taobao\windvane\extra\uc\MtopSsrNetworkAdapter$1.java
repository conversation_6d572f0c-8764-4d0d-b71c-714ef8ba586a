package android.taobao.windvane.extra.uc.MtopSsrNetworkAdapter$1;
import android.taobao.windvane.extra.uc.MtopSsrNetworkAdapter$MtopRequestListener;
import android.taobao.windvane.extra.uc.MtopSsrNetworkAdapter;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Long;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import java.lang.Boolean;
import java.lang.Integer;
import com.alibaba.fastjson.JSONObject;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import java.util.List;
import org.json.JSONArray;

public class MtopSsrNetworkAdapter$1 implements MtopSsrNetworkAdapter$MtopRequestListener	// class@000216 from classes.dex
{
    public final MtopSsrNetworkAdapter this$0;
    public static IpChange $ipChange;

    public void MtopSsrNetworkAdapter$1(MtopSsrNetworkAdapter p0){
       this.this$0 = p0;
       super();
    }
    public void beforeRequest(long p0,String p1,Map p2){
       IpChange $ipChange = MtopSsrNetworkAdapter$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0),p1,p2};
          $ipChange.ipc$dispatch("fab35b0e", objArray);
          return;
       }else {
          lcn.a(RVLLevel.Info, "WindVane/NetworkSSR").k("request", String.valueOf(p0)).m(MtopSsrNetworkAdapter.access$000(this.this$0)).a("url", p1).a("header", p2).f();
          return;
       }
    }
    public void onFinish(long p0,String p1,long p2,boolean p3){
       IpChange $ipChange = MtopSsrNetworkAdapter$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0),p1,new Long(p2),new Boolean(p3)};
          $ipChange.ipc$dispatch("59c46365", objArray);
          return;
       }else {
          lcn.a(RVLLevel.Info, "WindVane/NetworkSSR").k("finish", String.valueOf(p0)).m(MtopSsrNetworkAdapter.access$000(this.this$0)).a("url", p1).a("dataSize", Long.valueOf(p2)).a("isSuccess", Boolean.valueOf(p3)).f();
          return;
       }
    }
    public void onResponse(long p0,String p1,int p2,Map p3){
       RVLLevel info;
       IpChange $ipChange = MtopSsrNetworkAdapter$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0),p1,new Integer(p2),p3};
          $ipChange.ipc$dispatch("fe29489", objArray);
          return;
       }else if(p2 >= 200 && p2 <= 300){
          info = RVLLevel.Info;
       }else {
          info = RVLLevel.Warn;
       }
       JSONObject jSONObject = new JSONObject();
       if (p3 != null) {
          Iterator iterator = p3.entrySet().iterator();
          while (iterator.hasNext()) {
             Map$Entry uEntry = iterator.next();
             if (uEntry.getValue() != null && uEntry.getValue().size() > 0) {
                JSONArray jSONArray = new JSONArray();
                Iterator iterator1 = uEntry.getValue().iterator();
                while (iterator1.hasNext()) {
                   jSONArray.put(iterator1.next());
                }
                if (uEntry.getKey() != null) {
                   jSONObject.put(uEntry.getKey(), jSONArray);
                }
             }
             jSONObject.put(uEntry.getKey(), uEntry.getValue());
          }
       }
       lcn.a(info, "WindVane/NetworkSSR").k("response", String.valueOf(p0)).m(MtopSsrNetworkAdapter.access$000(this.this$0)).a("url", p1).a("statusCode", Integer.valueOf(p2)).a("header", jSONObject).f();
       return;
    }
}
