package android.taobao.windvane.extra.performance.WVH5PPManager;
import tb.t2o;
import android.taobao.windvane.webview.IWVWebView;
import java.lang.Object;
import android.taobao.windvane.extra.performance.WVPagePerformance;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Integer;
import android.view.View;
import android.taobao.windvane.extra.uc.interfaces.IRequestTiming;
import java.lang.Boolean;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.taobao.windvane.extra.uc.timing.RequestTimingMap;
import java.util.AbstractMap;
import android.taobao.windvane.extra.uc.timing.RequestTiming;
import android.taobao.windvane.extra.performance2.IPerformance;
import tb.acd;
import tb.lab;
import java.lang.Long;
import tb.ace;
import tb.cce;
import android.taobao.windvane.extra.performance.WVH5PPManager$1;
import android.webkit.ValueCallback;
import tb.xqw;
import tb.trw;
import com.uc.webview.export.WebView;
import java.lang.System;
import android.os.SystemClock;
import tb.vpw;
import tb.wpw;
import tb.avt;
import android.taobao.windvane.extra.performance.WVH5PPManager$2;
import tb.v7t;
import tb.bsw;
import tb.asw;
import tb.tc;
import tb.jpw;
import java.lang.Class;
import tb.yaa;
import android.content.Context;
import tb.x74;
import android.taobao.windvane.extra.core.WVRunningCoreInfo;
import android.taobao.windvane.startup.UCInitializerInfo;
import android.taobao.windvane.util.DeviceUtils;
import java.util.Map;
import tb.oba;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import android.taobao.windvane.extra.uc.pool.PreCreateInfo;
import java.lang.Throwable;

public class WVH5PPManager	// class@0001ce from classes.dex
{
    private String errorCode;
    private String errorMessage;
    private long h5_PP_T1;
    private long h5_PP_T1_uptime;
    private long h5_PP_TTI;
    private long h5_PP_initEnd;
    private long h5_PP_initEnd_uptime;
    private long h5_PP_initStart;
    private long h5_PP_initStart_uptime;
    private long h5_PP_loadRequest;
    private long h5_PP_loadRequest_uptime;
    private long h5_PP_startLoad;
    private long h5_PP_startLoad_uptime;
    private String h5_sys_core_version;
    private String htmlUrl;
    private int htmlZCacheState;
    private WVPagePerformance pagePerformance;
    private IWVWebView webView;
    private boolean webViewPreCreated;
    public static IpChange $ipChange;
    private static final String TAG;
    public static final int WV_H5PP_ZCache_State_Hit;
    public static final int WV_H5PP_ZCache_State_NotHit;
    public static final int WV_H5PP_ZCache_State_NotUse;
    private static int identify;

    static {
       t2o.a(0x3d800100);
       WVH5PPManager.identify = 0;
    }
    public void WVH5PPManager(IWVWebView p0){
       super();
       this.webViewPreCreated = false;
       this.webView = p0;
    }
    public static WVPagePerformance access$000(WVH5PPManager p0){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.pagePerformance;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("2cd5c67c", objArray);
    }
    public static IWVWebView access$100(WVH5PPManager p0){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.webView;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("cd5cd630", objArray);
    }
    private String getH5CoreTypeString(int p0){
       int i = 2;
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          objArray[1] = new Integer(p0);
          return $ipChange.ipc$dispatch("32b2af91", objArray);
       }else if(p0 == i){
          return "Sys";
       }else {
          return "U4";
       }
    }
    private static IRequestTiming getOrOpenRequestTiming(View p0,String p1,boolean p2){
       IRequestTiming iRequestTimi;
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Boolean(p2)};
          return $ipChange.ipc$dispatch("62cca929", objArray);
       }else if(p0 instanceof WVUCWebView && !TextUtils.isEmpty(p1)){
          p0 = p0.getExternalContext("requestTiming");
          if (p0 instanceof RequestTimingMap) {
             if ((iRequestTimi = p0.get(p1)) == null && p2) {
                iRequestTimi = new RequestTiming();
                p0.put(p1, iRequestTimi);
             }
          }else {
          label_004c :
             iRequestTimi = null;
          }
       }else {
          goto label_004c ;
       }
       return iRequestTimi;
    }
    public static IRequestTiming getRequestTiming(View p0,String p1){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVH5PPManager.getOrOpenRequestTiming(p0, p1, 0);
       }
       Object[] objArray = new Object[]{p0,p1};
       return $ipChange.ipc$dispatch("589eb46c", objArray);
    }
    private boolean isPreInitOrPreRender(View p0){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("f991b462", objArray).booleanValue();
       }else if(p0 instanceof IPerformance && p0.isPreInit()){
          return 1;
       }else if(p0 instanceof acd && p0.isPreRender()){
          return 1;
       }else {
          return 0;
       }
    }
    public static IRequestTiming openRequestTiming(View p0,String p1){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVH5PPManager.getOrOpenRequestTiming(p0, p1, 1);
       }
       Object[] objArray = new Object[]{p0,p1};
       return $ipChange.ipc$dispatch("58f2d3f8", objArray);
    }
    private void stageIfNonZero(lab p0,View p1,String p2,long p3){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,new Long(p3)};
          $ipChange.ipc$dispatch("22c9cdf5", objArray);
          return;
       }else if((p3) > 0 && p1 instanceof WVUCWebView){
          cce webViewPageM = p1.getWebViewContext().getWebViewPageModel();
          if ("H5_navigationStart".equals(p2)) {
             webViewPageM.onStageIfAbsent("H5_first_navigationStart", p3);
          }
          webViewPageM.onStage(p2, p3);
       }
       return;
    }
    private void takeW3CPP(){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("149eada7", objArray);
          return;
       }else if(this.pagePerformance == null){
          return;
       }else {
          this.webView.evaluateJavascript("\(function\(performance\){var timing=performance&&performance.timing;return timing&&JSON.stringify\({ns:timing.navigationStart,fs:timing.fetchStart,rs:timing.requestStart,re:timing.responseEnd,dl:timing.domLoading,dc:timing.domComplete,ds:timing.domContentLoadedEventStart,ls:timing.loadEventStart,le:timing.loadEventEnd}\)}\)\(window.performance\)", new WVH5PPManager$1(this));
          return;
       }
    }
    private void uploadHSCInfo(){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("b6a71e4c", objArray);
          return;
       }else if(trw.getWvPerformanceMonitorInterface() != null){
          trw.getWvPerformanceMonitorInterface().recordFSP(this.pagePerformance.getUrl(), (this.pagePerformance.getH5_PP_T2() - this.h5_PP_loadRequest));
          trw.getWvPerformanceMonitorInterface().recordTTI(this.pagePerformance.getUrl(), this.h5_PP_TTI);
       }
       return;
    }
    public String jsCodeForUserPP(){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "javascript:\(function\(\){var observer=new PerformanceObserver\(function\(list,obj\){for\(var entry of list.getEntries\(\)\){if\(entry.entryType==\'paint\'&&entry.name==\'first-paint\'\){console.log\(\'hybrid://WVPerformance:FP/receiveFPTime?{\"time\":\'+entry.startTime+\'}\'\)}if\(entry.entryType==\'longtask\'\){console.log\(\'hybrid://WVPerformance:TTI/receiveTTITime?{\"time\":\'+\(entry.startTime+entry.duration\)+\'}\'\)}}}\);observer.observe\({entryTypes:[\'longtask\',\'paint\']}\)}\)\(\)";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("dae6debb", objArray);
    }
    public void pageDidFailLoadWithError(String p0,String p1){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("df62d2a9", objArray);
          return;
       }else {
          this.errorCode = p0;
          this.errorMessage = p1;
          return;
       }
    }
    public void pageDidFinishIntercept(){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("1c3ca4e6", objArray);
       }
       return;
    }
    public void pageDidFinishLoad(String p0,WebView p1){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("985c38e0", objArray);
          return;
       }else {
          this.uploadInfo();
          WVPagePerformance $ipChange1 = new WVPagePerformance();
          this.pagePerformance = $ipChange1;
          $ipChange1.setH5_PP_startLoad(this.h5_PP_startLoad);
          this.pagePerformance.setH5_PP_startLoad_uptime(this.h5_PP_startLoad_uptime);
          this.pagePerformance.setH5_PP_finishLoad(System.currentTimeMillis());
          this.pagePerformance.setH5_PP_finishLoad_uptime(SystemClock.uptimeMillis());
          this.pagePerformance.setUrl(p0);
          this.pagePerformance.setH5_PP_T1(this.h5_PP_T1);
          this.pagePerformance.setH5_PP_T1_uptime(this.h5_PP_T1_uptime);
          this.pagePerformance.setH5_PP_errorCode(this.errorCode);
          this.pagePerformance.setH5_PP_errorMessage(this.errorMessage);
          this.pagePerformance.setH5_Core_Type_Str(this.getH5CoreTypeString(p1.getCurrentViewCoreType()));
          this.pagePerformance.setH5_Core_Type(p1.getCurrentViewCoreType());
          this.takeW3CPP();
          this.pagePerformance.setH5_PP_isFinished(1);
          vpw.b();
          if (vpw.commonConfig.Q0 == null) {
             this.uploadPPInfo(p1);
          }
          this.errorCode = null;
          this.errorMessage = null;
          return;
       }
    }
    public void pageDidLoadRequest(){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("56f8d40", objArray);
          return;
       }else {
          this.h5_PP_loadRequest = System.currentTimeMillis();
          this.h5_PP_loadRequest_uptime = SystemClock.uptimeMillis();
          return;
       }
    }
    public void pageDidStartIntercept(){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("d05ba97", objArray);
       }
       return;
    }
    public void pageDidStartLoad(){
       WVH5PPManager tpagePerform;
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("d395e75f", objArray);
          return;
       }else if((tpagePerform = this.pagePerformance) != null){
          tpagePerform.setH5_PP_TTI(this.h5_PP_TTI);
          this.h5_PP_TTI = 0;
       }
       this.h5_PP_startLoad = System.currentTimeMillis();
       this.h5_PP_startLoad_uptime = SystemClock.uptimeMillis();
       return;
    }
    public void receiveFPTime(long p0){
       WVH5PPManager tpagePerform;
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("2cb03403", objArray);
          return;
       }else if((tpagePerform = this.pagePerformance) == null){
          return;
       }else {
          tpagePerform.setH5_PP_FP(p0);
          return;
       }
    }
    public void receiveHtmlUrl(String p0){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("912cdd32", objArray);
          return;
       }else {
          this.htmlUrl = p0;
          return;
       }
    }
    public void receiveHtmlZCacheState(int p0,String p1){
       WVH5PPManager thtmlUrl;
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("8e96b8b", objArray);
          return;
       }else if((thtmlUrl = this.htmlUrl) == null){
          return;
       }else if(thtmlUrl.equals(p1)){
          this.htmlZCacheState = p0;
       }
       return;
    }
    public void receiveTTITime(long p0){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("e5a86b0a", objArray);
          return;
       }else if(((p0 - this.h5_PP_TTI) - 5000) <= 0){
          this.h5_PP_TTI = p0;
       }
       return;
    }
    public void recordFSP(long p0){
       WVH5PPManager tpagePerform;
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("4183b2b", objArray);
          return;
       }else if((tpagePerform = this.pagePerformance) == null){
          return;
       }else {
          tpagePerform.setH5_PP_FSP(p0);
          this.pagePerformance.setH5_PP_FSP_uptime(avt.a(p0));
          return;
       }
    }
    public void recordUCT1(long p0){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("89dbe361", objArray);
          return;
       }else {
          this.h5_PP_T1 = p0;
          this.h5_PP_T1_uptime = avt.a(p0);
          return;
       }
    }
    public void recordUCT2(long p0){
       WVH5PPManager tpagePerform;
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("8b90bc00", objArray);
          return;
       }else if((tpagePerform = this.pagePerformance) == null){
          return;
       }else {
          tpagePerform.setH5_PP_T2(p0);
          this.pagePerformance.setH5_PP_T2_uptime(avt.a(p0));
          return;
       }
    }
    public void setH5SysCoreVersion(String p0){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d5401a2c", objArray);
          return;
       }else {
          this.h5_sys_core_version = p0;
          return;
       }
    }
    public void setWebViewPreCreated(boolean p0){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("6ed04131", objArray);
          return;
       }else {
          this.webViewPreCreated = p0;
          return;
       }
    }
    public void trackLongestImageInfo(WVUCWebView p0){
       int i = 0;
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("189495d2", objArray);
          return;
       }else if(p0 != null && (!this.isPreInitOrPreRender(p0) && p0.getWebViewContext().getEnableNetworkTracing())){
          p0.getWebViewContext().setEnableNetworkTracing(i);
          p0.evaluateJavascript("\(function\(\) {\n    const startTime = new Date\(\).getTime\(\);\n    const entries = performance.getEntries\(\).filter\(it => it.initiatorType == \'img\' || it.name == \'ssr-end\'\);\n    const ssrEndIndex = entries.findIndex\(it => it.name == \'ssr-end\'\);\n    if \(ssrEndIndex >= 0\) {\n        const imagesBeforeSsrEnd = entries.splice\(0, ssrEndIndex\);\n        if \(imagesBeforeSsrEnd.length > 0\) {\n            const longestImage = imagesBeforeSsrEnd.reduce\(\(cur, prv\) => cur.duration > prv.duration ? cur : prv\)\n            const endTime = new Date\(\).getTime\(\);\n            return JSON.stringify\({\n                timeOrigin: performance.timeOrigin,\n                imageCountBeforeSsrEnd: imagesBeforeSsrEnd.length,\n                longestImage: longestImage,\n                trackCost: \(endTime - startTime\)\n            }\);\n        }\n    }\n    return \'\';\n}\)\(\);", new WVH5PPManager$2(this, p0));
          return;
       }else {
          v7t.a("H5PP", "ignore track longest image info");
          return;
       }
    }
    public void uploadInfo(){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("eabdc356", objArray);
          return;
       }else if(this.pagePerformance == null){
          return;
       }else {
          this.uploadHSCInfo();
          if (!bsw.b().a().f()) {
             return;
          }
          if (bsw.b().a().g()) {
             return;
          }
          this.pagePerformance.getUrl();
          this.pagePerformance.getH5_PP_isFinished();
          this.pagePerformance.getH5_PP_errorCode();
          this.pagePerformance.getH5_PP_errorMessage();
          this.pagePerformance.getH5_PP_startLoad();
          this.pagePerformance.getH5_PP_navigationStart();
          this.pagePerformance.getH5_PP_fetchStart();
          this.pagePerformance.getH5_PP_responseEnd();
          this.pagePerformance.getH5_PP_domContentLoadedEventStart();
          this.pagePerformance.getH5_PP_loadEventStart();
          this.pagePerformance.getH5_PP_loadEventEnd();
          this.pagePerformance.getH5_PP_FP();
          this.pagePerformance.getH5_PP_FSP();
          this.pagePerformance.getH5_PP_TTI();
          this.pagePerformance.getH5_PP_T1();
          this.pagePerformance.getH5_PP_T2();
          this.pagePerformance.getH5_PP_finishLoad();
          return;
       }
    }
    public void uploadPPInfo(View p0){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("68aaf3fc", objArray);
          return;
       }else {
          this.uploadToNativeApm(p0);
          return;
       }
    }
    public void uploadToNativeApm(View p0){
       lab olab;
       String coreVersion;
       Iterator obj;
       PreCreateInfo preCreateInf;
       Map$Entry uEntry;
       int i = 0;
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("27f36300", objArray);
          return;
       }else if(p0 instanceof IPerformance && p0.isPreInit()){
          return;
       }else if(p0 instanceof acd && p0.isPreRender()){
          return;
       }else if(vpw.commonConfig.J2 == null && !tc.a(p0)){
          v7t.d("H5PP", "enableReportApm is false");
          return;
       }else if((olab = jpw.c().a(lab.class)) != null){
          if (p0 instanceof WVUCWebView) {
             cce webViewPageM = p0.getWebViewContext().getWebViewPageModel();
             webViewPageM.onProperty("H5_URL", this.pagePerformance.getUrl());
             webViewPageM.onProperty("H5_process", x74.h(yaa.n));
             webViewPageM.onProperty("H5_isFinished", Boolean.valueOf(this.pagePerformance.getH5_PP_isFinished()));
             webViewPageM.onProperty("H5_errorCode", this.pagePerformance.getH5_PP_errorCode());
             webViewPageM.onProperty("H5_errorMessage", this.pagePerformance.getH5_PP_errorMessage());
             webViewPageM.onProperty("H5_htmlZCacheState", Integer.valueOf(this.htmlZCacheState));
             webViewPageM.onProperty("H5_coreType", Integer.valueOf(this.pagePerformance.getH5_Core_Type()));
             webViewPageM.onProperty("H5_coreTypeStr", this.pagePerformance.getH5_Core_Type_Str());
             webViewPageM.onProperty("H5_sysCoreVersion", this.h5_sys_core_version);
             webViewPageM.onProperty("H5_isPreCreatedWebView", Boolean.valueOf(this.webViewPreCreated));
             if ((coreVersion = WVRunningCoreInfo.getCoreVersion()) != null) {
                webViewPageM.onProperty("H5_sdkCoreVersion", coreVersion);
             }
             if ((obj = UCInitializerInfo.a().e("uc_use_httpcache")) != null) {
                webViewPageM.onProperty("H5_ucUseHttpCache", obj);
             }
             try{
                webViewPageM.onProperty("H5_deviceScore", Integer.valueOf(DeviceUtils.a()));
                obj = oba.a().entrySet().iterator();
                while (obj.hasNext()) {
                   if ((uEntry = obj.next()) == null) {
                      continue ;
                   }
                   webViewPageM.onPropertyIfAbsent(uEntry.getKey(), uEntry.getValue());
                }
                if ((preCreateInf = p0.getWebViewContext().getPreCreateInfo()) != null) {
                   webViewPageM.onStageIfAbsent("H5_preCreateStart", preCreateInf.getCreateStartTime());
                   webViewPageM.onStageIfAbsent("H5_preCreateEnd", preCreateInf.getCreateEndTime());
                   webViewPageM.onPropertyIfAbsent("H5_isFirstCreate", Boolean.valueOf(preCreateInf.isFirst()));
                   if (preCreateInf.getLoadUrlStartTime() != null) {
                      webViewPageM.onStageIfAbsent("H5_preCreateLoadUrlStart", preCreateInf.getLoadUrlStartTime().longValue());
                   }
                   if (preCreateInf.getLoadUrlEndTime() != null) {
                      webViewPageM.onStageIfAbsent("H5_preCreateLoadUrlEnd", preCreateInf.getLoadUrlEndTime().longValue());
                   }
                }
             }catch(java.lang.Exception e0){
             }
          }
          this.stageIfNonZero(olab, p0, "H5_initStart", this.h5_PP_initStart_uptime);
          this.stageIfNonZero(olab, p0, "H5_initEnd", this.h5_PP_initEnd_uptime);
          this.stageIfNonZero(olab, p0, "H5_loadRequest", this.h5_PP_loadRequest_uptime);
          this.stageIfNonZero(olab, p0, "H5_startLoad", this.pagePerformance.getH5_PP_startLoad_uptime());
          this.stageIfNonZero(olab, p0, "H5_navigationStart", this.pagePerformance.getH5_PP_navigationStart_uptime());
          this.stageIfNonZero(olab, p0, "H5_fetchStart", this.pagePerformance.getH5_PP_fetchStart_uptime());
          this.stageIfNonZero(olab, p0, "H5_responseEnd", this.pagePerformance.getH5_PP_responseEnd_uptime());
          this.stageIfNonZero(olab, p0, "H5_domLoading", this.pagePerformance.h5_PP_domLoading_uptime);
          this.stageIfNonZero(olab, p0, "H5_domContentLoadedEventStart", this.pagePerformance.getH5_PP_domContentLoadedEventStart_uptime());
          this.stageIfNonZero(olab, p0, "H5_loadEventStart", this.pagePerformance.getH5_PP_loadEventStart_uptime());
          this.stageIfNonZero(olab, p0, "H5_loadEventEnd", this.pagePerformance.getH5_PP_loadEventEnd_uptime());
          this.stageIfNonZero(olab, p0, "H5_firstPaint", this.pagePerformance.getH5_PP_FP_uptime());
          this.stageIfNonZero(olab, p0, "H5_firstScreenPaint", this.pagePerformance.getH5_PP_FSP_uptime());
          this.stageIfNonZero(olab, p0, "H5_timeToInteractive", this.pagePerformance.getH5_PP_TTI_uptime());
          this.stageIfNonZero(olab, p0, "H5_finishLoad", this.pagePerformance.getH5_PP_finishLoad_uptime());
          this.stageIfNonZero(olab, p0, "H5_coreInitStart", UCInitializerInfo.a().b(e0));
          this.stageIfNonZero(olab, p0, "H5_coreInitSuccess", UCInitializerInfo.a().b(7));
          this.stageIfNonZero(olab, p0, "H5_coreInitFail", UCInitializerInfo.a().b(8));
          this.stageIfNonZero(olab, p0, "ucRenderProcessReady", UCInitializerInfo.a().b(9));
          this.stageIfNonZero(olab, p0, "ucGpuProcessReady", UCInitializerInfo.a().b(10));
          this.stageIfNonZero(olab, p0, "H5_firstPreCreateStart", UCInitializerInfo.a().b(11));
          this.stageIfNonZero(olab, p0, "H5_firstPreCreateEnd", UCInitializerInfo.a().b(12));
       }
       return;
    }
    public void webViewDidFinishInit(){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("b9093a90", objArray);
          return;
       }else {
          this.h5_PP_initEnd = System.currentTimeMillis();
          this.h5_PP_initEnd_uptime = SystemClock.uptimeMillis();
          return;
       }
    }
    public void webViewDidStartInit(){
       IpChange $ipChange = WVH5PPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("e1a45c13", objArray);
          return;
       }else {
          this.h5_PP_initStart = System.currentTimeMillis();
          this.h5_PP_initStart_uptime = SystemClock.uptimeMillis();
          return;
       }
    }
}
