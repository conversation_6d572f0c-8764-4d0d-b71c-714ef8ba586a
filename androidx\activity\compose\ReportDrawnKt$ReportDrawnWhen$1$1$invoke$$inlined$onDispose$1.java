package androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$1;
import tb.gi20;
import java.lang.Object;

public final class ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$1 implements gi20	// class@00049c from classes.dex
{

    public void ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$1(){
       super();
    }
    public void dispose(){
    }
}
