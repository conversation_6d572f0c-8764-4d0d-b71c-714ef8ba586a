package android.taobao.windvane.extra.uc.AliNetworkAdapterNew$2;
import tb.esd;
import android.taobao.windvane.extra.uc.AliNetworkAdapterNew;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ace;
import tb.cce;
import java.lang.Long;

public class AliNetworkAdapterNew$2 implements esd	// class@0001fe from classes.dex
{
    public final AliNetworkAdapterNew this$0;
    public final WVUCWebView val$webview;
    public static IpChange $ipChange;

    public void AliNetworkAdapterNew$2(AliNetworkAdapterNew p0,WVUCWebView p1){
       this.this$0 = p0;
       this.val$webview = p1;
       super();
    }
    public void recordProperty(String p0,Object p1){
       AliNetworkAdapterNew$2 tval$webview;
       IpChange $ipChange = AliNetworkAdapterNew$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4faa9773", objArray);
          return;
       }else if((tval$webview = this.val$webview) != null){
          tval$webview.getWebViewContext().getWebViewPageModel().onPropertyIfAbsent(p0, p1);
       }
       return;
    }
    public void recordStage(String p0,long p1){
       AliNetworkAdapterNew$2 tval$webview;
       IpChange $ipChange = AliNetworkAdapterNew$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("6a661a86", objArray);
          return;
       }else if((tval$webview = this.val$webview) != null){
          tval$webview.getWebViewContext().getWebViewPageModel().onStage(p0, p1);
       }
       return;
    }
}
