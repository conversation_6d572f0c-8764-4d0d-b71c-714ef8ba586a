package android.taobao.windvane.extra.uc.WVPrefetchNetworkAdapter$CallableThread;
import java.util.concurrent.Callable;
import tb.t2o;
import android.taobao.windvane.extra.uc.WVPrefetchNetworkAdapter;
import java.lang.Object;
import anetwork.channel.Response;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.System;
import anetwork.channel.Network;
import anetwork.channel.Request;
import tb.y71;
import java.lang.StringBuilder;
import tb.v7t;
import java.lang.Throwable;

public class WVPrefetchNetworkAdapter$CallableThread implements Callable	// class@000232 from classes.dex
{
    public final WVPrefetchNetworkAdapter this$0;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800168);
    }
    public void WVPrefetchNetworkAdapter$CallableThread(WVPrefetchNetworkAdapter p0){
       this.this$0 = p0;
       super();
    }
    public Response call(){
       String str = "WVPrefetch netTime=";
       String str1 = " netTime";
       IpChange $ipChange = WVPrefetchNetworkAdapter$CallableThread.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("9ebc4f27", objArray);
       }else {
          Response response = WVPrefetchNetworkAdapter.access$100(this.this$0).syncSend(WVPrefetchNetworkAdapter.access$000(this.this$0), null);
          y71.init();
          long l = System.currentTimeMillis() - System.currentTimeMillis();
          String str2 = WVPrefetchNetworkAdapter.access$200(this.this$0);
          String str3 = (!WVPrefetchNetworkAdapter.access$300(this.this$0))? "short": "long";
          y71.commitPreloadMainHtmlInfo(str2, l, str3.concat(str1));
          v7t.d("WVPrefetchNetworkAdapter", str+l);
          return response;
       }
    }
    public Object call(){
       return this.call();
    }
}
