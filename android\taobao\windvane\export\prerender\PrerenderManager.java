package android.taobao.windvane.export.prerender.PrerenderManager;
import tb.t2o;
import java.util.ArrayList;
import java.lang.Object;
import java.util.List;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.uum;
import android.taobao.windvane.extra.uc.WVUCWebView;
import tb.ckf;
import java.util.Iterator;
import tb.wum;
import tb.xum;
import tb.zcd;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;
import android.content.Context;
import android.taobao.windvane.export.prerender.PrerenderManager$acquirePrerenderWebView$1;
import java.lang.Runnable;
import com.taobao.themis.kernel.utils.CommonExtKt;
import tb.g1a;
import android.taobao.windvane.export.prerender.PrerenderManager$preRender$1;
import tb.d1a;

public final class PrerenderManager	// class@00017b from classes.dex
{
    public static IpChange $ipChange;
    public static final PrerenderManager INSTANCE;
    public static final List a;

    static {
       t2o.a(0x3d80009e);
       t2o.a(0x3d80009b);
       PrerenderManager.INSTANCE = new PrerenderManager();
       PrerenderManager.a = new ArrayList();
    }
    public void PrerenderManager(){
       super();
    }
    public static final List a(PrerenderManager p0){
       IpChange $ipChange = PrerenderManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return PrerenderManager.a;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("aa72f3f8", objArray);
    }
    public WVUCWebView b(uum p0){
       WVUCWebView wVUCWebView1;
       IpChange $ipChange = PrerenderManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("26c12223", objArray);
       }else {
          ckf.g(p0, "params");
          Iterator iterator = PrerenderManager.a.iterator();
          while (true) {
             WVUCWebView wVUCWebView = null;
             if (iterator.hasNext()) {
                wVUCWebView1 = iterator.next();
                Object obj = wVUCWebView1;
                xum oxum = obj.a();
                if (!obj.c() || (p0.b() != oxum.b() || !oxum.c().a(oxum.e(), p0.c()))) {
                   continue ;
                }
             }else {
                wVUCWebView1 = wVUCWebView;
             }
             if (wVUCWebView1 != null) {
                lcn.f(RVLLevel.Info, "Themis/Performance/Prerender", "hit prerender webview");
                PrerenderManager.a.remove(wVUCWebView1);
                wVUCWebView1.b().setOuterContext(p0.a());
                if ((wVUCWebView1.a().a()) > 0) {
                   CommonExtKt.p(PrerenderManager$acquirePrerenderWebView$1.INSTANCE, wVUCWebView1.a().a());
                   break ;
                }
                break ;
             }else {
                return wVUCWebView;
             }
          }
          return wVUCWebView1.b();
       }
    }
    public void c(xum p0,g1a p1){
       IpChange $ipChange = PrerenderManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("400d5b9e", objArray);
          return;
       }else {
          ckf.g(p0, "params");
          ckf.g(p1, "callback");
          CommonExtKt.o(new PrerenderManager$preRender$1(p1, p0));
          return;
       }
    }
}
