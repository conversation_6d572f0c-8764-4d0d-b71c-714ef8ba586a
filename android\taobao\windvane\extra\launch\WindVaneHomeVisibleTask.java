package android.taobao.windvane.extra.launch.WindVaneHomeVisibleTask;
import android.taobao.windvane.extra.launch.InitOnceTask;
import tb.t2o;
import android.app.Application;
import java.util.HashMap;
import android.content.Context;
import tb.gvm;
import java.lang.String;
import tb.v7t;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Class;
import java.lang.reflect.Method;
import java.lang.Object;
import java.lang.Throwable;
import com.android.alibaba.ip.runtime.IpChange;
import android.taobao.windvane.extra.config.TBConfigManager;
import tb.xg4;
import tb.vpw;
import tb.btw;
import tb.eqw;
import tb.ypw;
import tb.xsw;
import android.taobao.windvane.config.WVConfigManager;
import tb.ipb;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.taobao.windvane.extra.launch.WVOptimizedStartup$Params;
import android.taobao.windvane.extra.launch.WVOptimizedStartup;
import tb.lqw;
import tb.xrw;
import tb.jqw;
import tb.nrw;
import android.taobao.windvane.extra.WVSchemeProcessor;
import tb.qsw;
import tb.psw;

public class WindVaneHomeVisibleTask extends InitOnceTask	// class@0001ba from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000ef);
    }
    public void WindVaneHomeVisibleTask(){
       super();
    }
    private void initUCAndPreStartup(Application p0,HashMap p1){
       gvm.a(p0);
       v7t.i("WindVaneSDK", "trying to init uc core ");
       Class[] uClassArray = new Class[0];
       Object[] objArray = new Object[0];
       WVUCWebView.class.getDeclaredMethod("staticInitializeOnce", uClassArray).invoke(null, objArray);
       v7t.i("WindVaneSDK", "init windvane called");
       return;
    }
    private void initWindVaneConfig(Application p0,HashMap p1){
       IpChange $ipChange = WindVaneHomeVisibleTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4772e957", objArray);
          return;
       }else {
          TBConfigManager.getInstance().initEarly(p0);
          xg4.k();
          vpw.b().d();
          btw.c().e();
          eqw.c().e();
          ypw.b().d();
          xsw.b().d();
          WVConfigManager.a().b("windvane_common", vpw.b());
          WVConfigManager.a().b("windvane_domain", eqw.c());
          WVConfigManager.a().b("WindVane_URL_config", btw.c());
          WVConfigManager.a().b("cookie_black_list", ypw.b());
          WVConfigManager.a().b("windvane_uc_core", xsw.b());
          TBConfigManager.getInstance().initConfig();
          return;
       }
    }
    public static Object ipc$super(WindVaneHomeVisibleTask p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/launch/WindVaneHomeVisibleTask");
    }
    public String getName(){
       IpChange $ipChange = WindVaneHomeVisibleTask.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "WindVaneHomeVisibleTask";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7c09e698", objArray);
    }
    public void initImpl(Application p0,HashMap p1){
       IpChange $ipChange = WindVaneHomeVisibleTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("73f761cb", objArray);
          return;
       }else {
          WVOptimizedStartup.startup(new WVOptimizedStartup$Params(p0));
          lqw.d().c(xrw.getInstance(), lqw.e);
          nrw.init();
          psw.b(new WVSchemeProcessor());
          return;
       }
    }
}
