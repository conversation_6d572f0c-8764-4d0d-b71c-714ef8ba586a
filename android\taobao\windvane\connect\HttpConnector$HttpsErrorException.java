package android.taobao.windvane.connect.HttpConnector$HttpsErrorException;
import java.lang.Exception;
import tb.t2o;
import android.taobao.windvane.connect.HttpConnector;
import java.lang.String;

public class HttpConnector$HttpsErrorException extends Exception	// class@00014f from classes.dex
{
    public final HttpConnector this$0;

    static {
       t2o.a(0x3d80003d);
    }
    public void HttpConnector$HttpsErrorException(HttpConnector p0,String p1){
       this.this$0 = p0;
       super(p1);
    }
}
