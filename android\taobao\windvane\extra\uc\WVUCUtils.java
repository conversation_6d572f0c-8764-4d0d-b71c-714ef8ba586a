package android.taobao.windvane.extra.uc.WVUCUtils;
import tb.t2o;
import java.lang.Object;
import java.io.Closeable;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Throwable;
import java.io.File;
import java.io.FileInputStream;
import java.io.InputStreamReader;
import java.io.InputStream;
import java.io.BufferedReader;
import java.io.Reader;
import java.lang.CharSequence;
import java.lang.Boolean;
import android.os.Build$VERSION;
import tb.zsw;
import java.lang.StringBuilder;
import tb.v7t;
import android.os.Build;
import tb.yaa;
import java.lang.ClassLoader;
import android.content.Context;
import java.lang.Class;
import java.lang.reflect.Method;
import java.lang.System;
import java.lang.reflect.Field;

public class WVUCUtils	// class@00023f from classes.dex
{
    public static IpChange $ipChange;
    public static final String CONFIG_KEY;
    public static final String TAG;
    public static final long VAL_ARM;
    public static final long VAL_DEAFAULT;
    public static final long VAL_X86;
    private static String sAbi;
    private static String sAbi2;
    private static String[] sAbiList;
    private static String sArch;
    private static String sCpuAbi;
    private static String[] sSupportedABIs;

    static {
       t2o.a(0x3d800174);
    }
    public void WVUCUtils(){
       super();
    }
    private static void close(Closeable p0){
       IpChange $ipChange = WVUCUtils.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("10f5fa8c", objArray);
          return;
       }else if(p0 != null){
          try{
             p0.close();
          }catch(java.lang.Exception e4){
             e4.printStackTrace();
          }
       }
       return;
    }
    private static String getFromSystemProp(String p0){
       String str;
       String[] stringArray;
       int i = 1;
       IpChange $ipChange = WVUCUtils.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("ded5037d", objArray);
       }else {
          FileInputStream uFileInputSt = new FileInputStream(new File("/system/build.prop"));
          InputStreamReader inputStreamR = new InputStreamReader(uFileInputSt);
          BufferedReader uBufferedRea = new BufferedReader(inputStreamR);
          do {
             if ((str = uBufferedRea.readLine()) != null) {
                if (!str.contains(p0)) {
                   continue ;
                }else {
                }
             }else {
                WVUCUtils.close(uBufferedRea);
                WVUCUtils.close(inputStreamR);
                WVUCUtils.close(uFileInputSt);
                return null;
             }
             stringArray = str.split("=");
          } while (stringArray.length == 2 && stringArray[0].trim().equals(p0));
          WVUCUtils.close(uBufferedRea);
          WVUCUtils.close(inputStreamR);
          WVUCUtils.close(uFileInputSt);
          return stringArray[i].trim();
       }
    }
    public static boolean is64Bit(){
       boolean b;
       IpChange $ipChange = WVUCUtils.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("8bf7f490", objArray).booleanValue();
       }else if(Build$VERSION.SDK_INT >= 23){
          b = zsw.a();
          v7t.i("WVUCUtils", "is 64 bit = ["+b+"]");
       }else {
          b = WVUCUtils.isART64();
       }
       return b;
    }
    private static boolean is64bitCPU(){
       IpChange $ipChange = WVUCUtils.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("72807fce", objArray).booleanValue();
       }else {
          String[] sUPPORTED_AB = Build.SUPPORTED_ABIS;
          object oobject = (sUPPORTED_AB.length > 0)? sUPPORTED_AB[i]: null;
          if (oobject != null && oobject.contains("arm64")) {
             return true;
          }else {
             return i;
          }
       }
    }
    private static boolean isART64(){
       Object obj;
       int i = 1;
       int i1 = 0;
       try{
          Class[] uClassArray = new Class[i];
          uClassArray[i1] = String.class;
          Object[] objArray = new Object[i];
          objArray[i1] = "art";
          if ((obj = ClassLoader.class.getDeclaredMethod("findLibrary", uClassArray).invoke(yaa.n.getClassLoader(), objArray)) != null) {
             return obj.contains("lib64");
          }
          return i1;
       }catch(java.lang.Exception e0){
          return WVUCUtils.is64bitCPU();
       }
    }
    public static boolean isArchContains(String p0){
       String sArch;
       String[] sSupportedAB;
       object oobject;
       if (WVUCUtils.sArch == null) {
          WVUCUtils.sArch = System.getProperty("os.arch");
       }
       if ((sArch = WVUCUtils.sArch) != null && sArch.toLowerCase().contains(p0)) {
          return true;
       }else if(WVUCUtils.sAbi == null){
          try{
             WVUCUtils.sAbi = Build.CPU_ABI;
             WVUCUtils.sAbi2 = Build.CPU_ABI2;
          }catch(java.lang.Exception e0){
             e0.printStackTrace();
          }
       }
       if ((sArch = WVUCUtils.sAbi) != null && sArch.toLowerCase().contains(p0)) {
          return true;
       }else if(WVUCUtils.sSupportedABIs == null){
          try{
             WVUCUtils.sSupportedABIs = Build.class.getField("SUPPORTED_ABIS").get(null);
          }catch(java.lang.Exception e0){
             e0.printStackTrace();
          }
       }
       if ((sSupportedAB = WVUCUtils.sSupportedABIs) != null && (sSupportedAB.length > 0 && ((oobject = sSupportedAB[0]) != null && oobject.toLowerCase().contains(p0)))) {
          return true;
       }else if(WVUCUtils.sCpuAbi == null){
          WVUCUtils.sCpuAbi = WVUCUtils.getFromSystemProp("ro.product.cpu.abi");
       }
       if ((sArch = WVUCUtils.sCpuAbi) != null && sArch.toLowerCase().contains(p0)) {
          return true;
       }else if(WVUCUtils.sAbiList == null && ((sArch = WVUCUtils.getFromSystemProp("ro.product.cpu.abilist")) != null && sArch.length())){
          WVUCUtils.sAbiList = sArch.split(",");
       }
       if ((sSupportedAB = WVUCUtils.sAbiList) != null && (sSupportedAB.length > 0 && ((oobject = sSupportedAB[0]) != null && oobject.toLowerCase().contains(p0)))) {
          return true;
       }else {
          return 0;
       }
    }
}
