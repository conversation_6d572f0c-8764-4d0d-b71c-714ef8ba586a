package android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$3;
import android.webkit.ValueCallback;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$d;
import android.taobao.windvane.export.cache.memory.model.ResourceItemModel;
import java.lang.Object;
import com.uc.webview.export.WebResourceResponse;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager;

public final class MemoryResWarmupManager$3 implements ValueCallback	// class@000157 from classes.dex
{
    public final MemoryResWarmupManager$d val$callback;
    public final ResourceItemModel val$model;
    public static IpChange $ipChange;

    public void MemoryResWarmupManager$3(MemoryResWarmupManager$d p0,ResourceItemModel p1){
       this.val$callback = p0;
       this.val$model = p1;
       super();
    }
    public void onReceiveValue(WebResourceResponse p0){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = MemoryResWarmupManager$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d58c00bc", objArray);
          return;
       }else if(p0 == null){
          MemoryResWarmupManager.a(this.val$callback, i1, "response is null");
          return;
       }else if(p0.getStatusCode() != 200){
          MemoryResWarmupManager.a(this.val$callback, i1, p0.getReasonPhrase());
          return;
       }else {
          MemoryResWarmupManager.d(this.val$model, p0, this.val$callback);
          MemoryResWarmupManager.a(this.val$callback, i, "from http cache");
          return;
       }
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
}
