package androidx.activity.compose.PredictiveBackHandlerCallback;
import androidx.activity.OnBackPressedCallback;
import tb.uu4;
import tb.u1a;
import androidx.activity.compose.OnBackInstance;
import androidx.activity.BackEventCompat;
import java.lang.Object;

public final class PredictiveBackHandlerCallback extends OnBackPressedCallback	// class@00048d from classes.dex
{
    private u1a currentOnBack;
    private OnBackInstance onBackInstance;
    private uu4 onBackScope;

    public void PredictiveBackHandlerCallback(boolean p0,uu4 p1,u1a p2){
       super(p0);
       this.onBackScope = p1;
       this.currentOnBack = p2;
    }
    public final u1a getCurrentOnBack(){
       return this.currentOnBack;
    }
    public final uu4 getOnBackScope(){
       return this.onBackScope;
    }
    public void handleOnBackCancelled(){
       PredictiveBackHandlerCallback tonBackInsta;
       super.handleOnBackCancelled();
       if ((tonBackInsta = this.onBackInstance) != null) {
          tonBackInsta.cancel();
       }
       if ((tonBackInsta = this.onBackInstance) != null) {
          tonBackInsta.setPredictiveBack(false);
       }
       return;
    }
    public void handleOnBackPressed(){
       PredictiveBackHandlerCallback tonBackInsta;
       if ((tonBackInsta = this.onBackInstance) != null && !tonBackInsta.isPredictiveBack()) {
          tonBackInsta.cancel();
          this.onBackInstance = null;
       }
       if (this.onBackInstance == null) {
          this.onBackInstance = new OnBackInstance(this.onBackScope, false, this.currentOnBack, this);
       }
       if ((tonBackInsta = this.onBackInstance) != null) {
          tonBackInsta.close();
       }
       if ((tonBackInsta = this.onBackInstance) != null) {
          tonBackInsta.setPredictiveBack(false);
       }
       return;
    }
    public void handleOnBackProgressed(BackEventCompat p0){
       PredictiveBackHandlerCallback tonBackInsta;
       super.handleOnBackProgressed(p0);
       if ((tonBackInsta = this.onBackInstance) != null) {
          tonBackInsta.send-JP2dKIU(p0);
       }
       return;
    }
    public void handleOnBackStarted(BackEventCompat p0){
       PredictiveBackHandlerCallback tonBackInsta;
       super.handleOnBackStarted(p0);
       if ((tonBackInsta = this.onBackInstance) != null) {
          tonBackInsta.cancel();
       }
       if (this.isEnabled()) {
          this.onBackInstance = new OnBackInstance(this.onBackScope, true, this.currentOnBack, this);
       }
       return;
    }
    public final void setCurrentOnBack(u1a p0){
       this.currentOnBack = p0;
    }
    public final void setIsEnabled(boolean p0){
       PredictiveBackHandlerCallback tonBackInsta;
       if (!p0 && (this.isEnabled() && (tonBackInsta = this.onBackInstance) != null)) {
          tonBackInsta.cancel();
       }
       this.setEnabled(p0);
       return;
    }
    public final void setOnBackScope(uu4 p0){
       this.onBackScope = p0;
    }
}
