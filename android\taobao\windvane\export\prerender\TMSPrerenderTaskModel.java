package android.taobao.windvane.export.prerender.TMSPrerenderTaskModel;
import java.io.Serializable;
import tb.t2o;
import java.lang.Object;
import java.lang.Long;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Number;
import java.util.List;
import java.lang.Integer;

public final class TMSPrerenderTaskModel implements Serializable	// class@000181 from classes.dex
{
    private Long delayNextTime;
    private String endTime;
    private int prerenderType;
    private List spmBVerifyValue;
    private String startTime;
    private String url;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000ae);
    }
    public void TMSPrerenderTaskModel(){
       super();
       this.prerenderType = 1;
    }
    public final Long getDelayNextTime(){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.delayNextTime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5758c941", objArray);
    }
    public final String getEndTime(){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.endTime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c5b56827", objArray);
    }
    public final int getPrerenderType(){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.prerenderType;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ed16f717", objArray).intValue();
    }
    public final List getSpmBVerifyValue(){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.spmBVerifyValue;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("65d4be90", objArray);
    }
    public final String getStartTime(){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.startTime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7fef9d00", objArray);
    }
    public final String getUrl(){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.url;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("de8f0660", objArray);
    }
    public final void setDelayNextTime(Long p0){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f87e1207", objArray);
          return;
       }else {
          this.delayNextTime = p0;
          return;
       }
    }
    public final void setEndTime(String p0){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f7fb2597", objArray);
          return;
       }else {
          this.endTime = p0;
          return;
       }
    }
    public final void setPrerenderType(int p0){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("4cf3e26b", objArray);
          return;
       }else {
          this.prerenderType = p0;
          return;
       }
    }
    public final void setSpmBVerifyValue(List p0){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("bf5c813c", objArray);
          return;
       }else {
          this.spmBVerifyValue = p0;
          return;
       }
    }
    public final void setStartTime(String p0){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("41bede", objArray);
          return;
       }else {
          this.startTime = p0;
          return;
       }
    }
    public final void setUrl(String p0){
       IpChange $ipChange = TMSPrerenderTaskModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e1dea87e", objArray);
          return;
       }else {
          this.url = p0;
          return;
       }
    }
}
