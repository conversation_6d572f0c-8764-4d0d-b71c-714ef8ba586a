package android.taobao.windvane.webview.WVWebViewClient$3;
import android.webkit.ValueCallback;
import android.taobao.windvane.webview.WVWebViewClient;
import android.taobao.windvane.webview.WVWebView;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;
import tb.v7t;
import android.taobao.windvane.jsbridge.WVBridgeEngine;

public class WVWebViewClient$3 implements ValueCallback	// class@000317 from classes.dex
{
    public final WVWebViewClient this$0;
    public final WVWebView val$webview;
    public static IpChange $ipChange;

    public void WVWebViewClient$3(WVWebViewClient p0,WVWebView p1){
       this.this$0 = p0;
       this.val$webview = p1;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       IpChange $ipChange = WVWebViewClient$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else {
          v7t.i("WVJsBridge", "has windvane :"+p0);
          if ("false".equals(p0)) {
             this.val$webview.loadUrl("javascript:"+WVBridgeEngine.WINDVANE_CORE_JS);
          }
          return;
       }
    }
}
