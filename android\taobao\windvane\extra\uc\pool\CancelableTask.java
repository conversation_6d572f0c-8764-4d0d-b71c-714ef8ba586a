package android.taobao.windvane.extra.uc.pool.CancelableTask;
import java.lang.Runnable;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public abstract class CancelableTask implements Runnable	// class@000277 from classes.dex
{
    private boolean mIsCanceled;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8001ac);
    }
    public void CancelableTask(){
       super();
    }
    public void cancel(){
       int i = 1;
       IpChange $ipChange = CancelableTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("707fe601", objArray);
          return;
       }else {
          this.mIsCanceled = i;
          return;
       }
    }
    public abstract void doRun();
    public abstract void onCancel();
    public final void run(){
       IpChange $ipChange = CancelableTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(this.mIsCanceled != null){
          this.onCancel();
       }else {
          this.doRun();
       }
       return;
    }
}
