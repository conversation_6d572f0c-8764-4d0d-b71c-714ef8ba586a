package android.taobao.windvane.extra.uc.WebViewContext;
import tb.ace;
import tb.t2o;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import java.util.concurrent.CopyOnWriteArrayList;
import android.taobao.windvane.extra.performance.WVWebViewPageModel;
import tb.vpw;
import tb.wpw;
import java.util.concurrent.atomic.AtomicBoolean;
import android.taobao.windvane.extra.uc.WebViewContext$1;
import android.view.View$OnAttachStateChangeListener;
import android.view.View;
import android.taobao.windvane.extra.performance.BuiltinWebViewPageModel;
import tb.cce;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import java.util.List;
import tb.ycd;
import tb.add;
import tb.w9o;
import tb.x74;
import tb.ace$a;
import tb.ace$b;
import android.taobao.windvane.extra.uc.pool.PreCreateInfo;
import java.util.Iterator;
import com.uc.webview.export.extension.UCExtension;
import com.uc.webview.export.WebView;

public class WebViewContext implements ace	// class@00026e from classes.dex
{
    private String customMegaBizId;
    private ace$b customMegaDataMapAdapter;
    private String customMegaNamespace;
    private boolean mEnableAsyncJSAPIChannel;
    private boolean mEnableNetworkTracing;
    private boolean mHitSnapshot;
    private boolean mIsClientPrerender;
    private boolean mIsThemis;
    private PreCreateInfo mPreCreateInfo;
    private String mRealUrl;
    private w9o mResponseInfo;
    private final WVUCWebView mWebView;
    public ace$a megaHandler;
    private final List prerenderAttachEventListeners;
    private final List prerenderSuccessEventListeners;
    private final WVWebViewPageModel webViewPageModel;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8001a2);
       t2o.a(0x3d8000b2);
    }
    public void WebViewContext(WVUCWebView p0){
       super();
       this.prerenderAttachEventListeners = new CopyOnWriteArrayList();
       this.prerenderSuccessEventListeners = new CopyOnWriteArrayList();
       boolean b = false;
       this.mIsClientPrerender = b;
       this.mHitSnapshot = b;
       this.mEnableAsyncJSAPIChannel = b;
       this.mIsThemis = b;
       this.mEnableNetworkTracing = b;
       this.mWebView = p0;
       this.webViewPageModel = new WVWebViewPageModel();
       if (vpw.commonConfig.r2 != null) {
          p0.addOnAttachStateChangeListener(new WebViewContext$1(this, new AtomicBoolean(b), p0));
       }else {
          this.addWebViewPageModel(new BuiltinWebViewPageModel(p0));
       }
       return;
    }
    public static boolean access$000(WebViewContext p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mIsClientPrerender;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("635d5e1a", objArray).booleanValue();
    }
    public static List access$100(WebViewContext p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.prerenderAttachEventListeners;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("ecb578b2", objArray);
    }
    public void addPrerenderAttachEventListener(ycd p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8dc5aad3", objArray);
          return;
       }else {
          this.prerenderAttachEventListeners.add(p0);
          return;
       }
    }
    public void addPrerenderSuccessEventListener(add p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("117e5a3f", objArray);
          return;
       }else {
          this.prerenderSuccessEventListeners.add(p0);
          return;
       }
    }
    public void addWebViewPageModel(cce p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("63ed38db", objArray);
          return;
       }else {
          this.webViewPageModel.addWebViewPageModel(p0);
          return;
       }
    }
    public String getCustomMegaBizId(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.customMegaBizId;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5c39b884", objArray);
    }
    public String getCustomMegaNamespace(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.customMegaNamespace;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c0b2ee57", objArray);
    }
    public boolean getEnableAsyncJSAPIChannel(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mEnableAsyncJSAPIChannel;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("d9de2ec0", objArray).booleanValue();
    }
    public boolean getEnableNetworkTracing(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mEnableNetworkTracing;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("a6aa2198", objArray).booleanValue();
    }
    public w9o getMainFrameResponseInfo(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mResponseInfo;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c45b28a9", objArray);
    }
    public String getMegaBizId(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return x74.c(this.mWebView.getCurrentUrl(), this.customMegaBizId);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("28d88d33", objArray);
    }
    public ace$a getMegaHandler(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.megaHandler;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("e244b519", objArray);
    }
    public ace$b getMegaUserDataMapAdapter(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.customMegaDataMapAdapter;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("866f2a96", objArray);
    }
    public PreCreateInfo getPreCreateInfo(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mPreCreateInfo;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c7ffbd37", objArray);
    }
    public String getRealUrl(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mRealUrl;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("cbf55f1e", objArray);
    }
    public cce getWebViewPageModel(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.webViewPageModel;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9884ab6a", objArray);
    }
    public boolean isClientPrerender(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mIsClientPrerender;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("fdf4d26f", objArray).booleanValue();
    }
    public boolean isHitSnapshot(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mHitSnapshot;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("39726cf8", objArray).booleanValue();
    }
    public boolean isThemis(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mIsThemis;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("a9f4329b", objArray).booleanValue();
    }
    public void notifyPrerenderSuccess(){
       add uoadd;
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("2e9c8c3a", objArray);
          return;
       }else {
          Iterator iterator = this.prerenderSuccessEventListeners.iterator();
          while (iterator.hasNext()) {
             if ((uoadd = iterator.next()) != null) {
                uoadd.onSuccess();
             }
          }
          this.prerenderSuccessEventListeners.clear();
          return;
       }
    }
    public void removePrerenderAttachEventListener(){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("ea49719b", objArray);
          return;
       }else {
          this.prerenderAttachEventListeners.clear();
          return;
       }
    }
    public void setCustomMegaBizId(String p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e5252cda", objArray);
          return;
       }else {
          this.customMegaBizId = p0;
          return;
       }
    }
    public void setCustomMegaNamespace(String p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("1d805767", objArray);
          return;
       }else {
          this.customMegaNamespace = p0;
          return;
       }
    }
    public void setEnableAsyncJSAPIChannel(boolean p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("5cd1bea4", objArray);
          return;
       }else {
          this.mEnableAsyncJSAPIChannel = p0;
          return;
       }
    }
    public void setEnableNetworkTracing(boolean p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("78671c34", objArray);
          return;
       }else {
          this.mEnableNetworkTracing = p0;
          return;
       }
    }
    public void setHitSnapshot(boolean p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("96c82398", objArray);
          return;
       }else {
          this.mHitSnapshot = p0;
          return;
       }
    }
    public void setIsClientPrerender(boolean p0){
       UCExtension uCExtension;
       int i = 1;
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("5cb840cb", objArray);
          return;
       }else {
          this.mIsClientPrerender = p0;
          if (p0 && (uCExtension = this.mWebView.getUCExtension()) != null) {
             uCExtension.setIsPreRender(i);
          }
          return;
       }
    }
    public void setMainFrameResponseInfo(w9o p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("929d85dd", objArray);
          return;
       }else {
          this.mResponseInfo = p0;
          return;
       }
    }
    public void setMegaHandler(ace$a p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8dd7303", objArray);
          return;
       }else {
          this.megaHandler = p0;
          return;
       }
    }
    public void setMegaUserDataMapAdapter(ace$b p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d3c42e9a", objArray);
          return;
       }else {
          this.customMegaDataMapAdapter = p0;
          return;
       }
    }
    public void setPreCreateInfo(PreCreateInfo p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("7d09c0d7", objArray);
          return;
       }else {
          this.mPreCreateInfo = p0;
          return;
       }
    }
    public void setRealUrl(String p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("b9ba0d80", objArray);
          return;
       }else {
          this.mRealUrl = p0;
          return;
       }
    }
    public void setThemis(boolean p0){
       IpChange $ipChange = WebViewContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("58d9f0e5", objArray);
          return;
       }else {
          this.mIsThemis = p0;
          return;
       }
    }
}
