package android.taobao.windvane.extra.uc.UCSetupServiceUtil;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.uc.UCSetupService;
import tb.r9u;
import tb.vpw;
import tb.xsw;
import tb.ypw;

public class UCSetupServiceUtil	// class@00022b from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800160);
    }
    public void UCSetupServiceUtil(){
       super();
    }
    public static void configUCSettingsBeforeInit(){
       IpChange $ipChange = UCSetupServiceUtil.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("fa393aa9", objArray);
          return;
       }else {
          UCSetupService.configUCSettingsBeforeInit();
          return;
       }
    }
    public static void preloadUCNecessaryConfigSync(){
       IpChange $ipChange = UCSetupServiceUtil.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("da4bd7dc", objArray);
          return;
       }else {
          r9u.b("preloadUCNecessaryConfig");
          vpw.b().d();
          xsw.b().d();
          ypw.b().d();
          r9u.d();
          return;
       }
    }
}
