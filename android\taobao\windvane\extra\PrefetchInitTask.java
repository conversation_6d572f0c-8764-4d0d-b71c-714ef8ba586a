package android.taobao.windvane.extra.PrefetchInitTask;
import tb.t2o;
import java.lang.Object;
import android.app.Application;
import java.util.HashMap;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.content.Context;
import tb.t;
import android.taobao.windvane.thread.WVThreadPool;
import android.taobao.windvane.extra.PrefetchInitTask$1;
import java.lang.Runnable;
import java.lang.StringBuilder;
import tb.v7t;
import java.lang.Throwable;
import tb.x74;

public class PrefetchInitTask	// class@000185 from classes.dex
{
    public static IpChange $ipChange;
    private static final String ENABLE_WV_PREFETCH;
    private static final String TAG;

    static {
       t2o.a(0x3d8000b9);
    }
    public void PrefetchInitTask(){
       super();
    }
    public static void init(Application p0,HashMap p1){
       String str = "PrefetchInitTask";
       String str1 = "startupUrl=";
       IpChange $ipChange = PrefetchInitTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("dddb138b", objArray);
          return;
       }else if(!t.a(p0, "enable_wv_prefetch")){
          return;
       }else {
          String str2 = p1.get("startupUrl");
          WVThreadPool.getInstance().execute(new PrefetchInitTask$1(str2, p0));
          v7t.d(str, str1+str2);
          return;
       }
    }
}
