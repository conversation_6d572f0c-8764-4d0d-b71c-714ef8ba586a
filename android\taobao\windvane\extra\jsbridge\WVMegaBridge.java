package android.taobao.windvane.extra.jsbridge.WVMegaBridge;
import java.io.Serializable;
import tb.t2o;
import android.taobao.windvane.extra.uc.WVUCWebView;
import com.alibaba.ability.hub.AbilityHubAdapter;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.jsbridge.WVBridgeEngine;
import android.taobao.windvane.jsbridge.MegaBridgeJS;
import tb.ace;
import tb.cce;
import android.taobao.windvane.extra.performance.WVWebViewPageModel;
import java.util.concurrent.ConcurrentHashMap;
import com.alibaba.fastjson.JSON;
import tb.ace$a;
import java.util.concurrent.CountDownLatch;
import android.os.Handler;
import android.os.Looper;
import android.taobao.windvane.extra.jsbridge.WVMegaBridge$1;
import java.lang.Runnable;
import java.lang.Throwable;

public class WVMegaBridge implements Serializable	// class@0001a9 from classes.dex
{
    private final AbilityHubAdapter mAbilityHubAdapter;
    private final WVUCWebView mWebView;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000dc);
    }
    public void WVMegaBridge(WVUCWebView p0,AbilityHubAdapter p1){
       super();
       this.mAbilityHubAdapter = p1;
       this.mWebView = p0;
    }
    public static WVUCWebView access$000(WVMegaBridge p0){
       IpChange $ipChange = WVMegaBridge.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mWebView;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("79e61341", objArray);
    }
    public static AbilityHubAdapter access$100(WVMegaBridge p0){
       IpChange $ipChange = WVMegaBridge.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mAbilityHubAdapter;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("d5dfc142", objArray);
    }
    public String getInjectJS(){
       IpChange $ipChange = WVMegaBridge.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mWebView.getInjectJS();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("27c608e9", objArray);
    }
    public String getMegaBridgeJs(){
       IpChange $ipChange = WVMegaBridge.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("9dedbf7f", objArray);
       }else if(vpw.commonConfig.e2 != null){
          return WVBridgeEngine.WINDVANE_CORE_JS;
       }else {
          return MegaBridgeJS.c();
       }
    }
    public String getStages(){
       IpChange $ipChange = WVMegaBridge.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("254c7ae", objArray);
       }else {
          try{
             cce webViewPageM = this.mWebView.getWebViewContext().getWebViewPageModel();
             if (webViewPageM instanceof WVWebViewPageModel) {
                return JSON.toJSONString(webViewPageM.getStageMap());
             }
             return "{}";
          }catch(java.lang.Exception e0){
          }
       }
    }
    public String nativeCall(String p0,String p1,String p2){
       ace$a megaHandler;
       int i = 1;
       IpChange $ipChange = WVMegaBridge.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("2ffe45e6", objArray);
       }else if((megaHandler = this.mWebView.getWebViewContext().getMegaHandler()) != null){
          return megaHandler.a(p0, p1, p2);
       }else {
          String[] stringArray = new String[]{null};
          CountDownLatch uCountDownLa = new CountDownLatch(i);
          Handler handler = new Handler(Looper.getMainLooper());
          WVMegaBridge$1 v11 = new WVMegaBridge$1(this, p0, p1, p2, stringArray, uCountDownLa);
          try{
             handler.post(v11);
             uCountDownLa.await();
          }catch(java.lang.InterruptedException e13){
             e13.printStackTrace();
          }
          return stringArray[0];
       }
    }
}
