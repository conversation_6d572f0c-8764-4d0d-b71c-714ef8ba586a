package androidx.activity.compose.ReportDrawnKt$ReportDrawnAfter$1$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import androidx.activity.FullyDrawnReporter;
import tb.g1a;
import tb.ar4;
import java.lang.Object;
import tb.uu4;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;

public final class ReportDrawnKt$ReportDrawnAfter$1$1 extends SuspendLambda implements u1a	// class@000499 from classes.dex
{
    public final g1a $block;
    public final FullyDrawnReporter $fullyDrawnReporter;
    public Object L$0;
    public int label;

    public void ReportDrawnKt$ReportDrawnAfter$1$1(FullyDrawnReporter p0,g1a p1,ar4 p2){
       this.$fullyDrawnReporter = p0;
       this.$block = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       return new ReportDrawnKt$ReportDrawnAfter$1$1(this.$fullyDrawnReporter, this.$block, p1);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(uu4 p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       ReportDrawnKt$ReportDrawnAfter$1$1 tlabel;
       ReportDrawnKt$ReportDrawnAfter$1$1 obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel == 1) {
             obj = this.L$0;
             b.b(p0);
          }else {
             throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
          }
       }else {
          b.b(p0);
          p0 = this.$fullyDrawnReporter;
          tlabel = this.$block;
          p0.addReporter();
          if (p0.isFullyDrawnReported()) {
          label_003d :
             return xhv.INSTANCE;
          }else {
             this.L$0 = p0;
             this.label = 1;
             if (tlabel.invoke(this) == obj) {
                return obj;
             }else {
                obj = p0;
             }
          }
       }
       obj.removeReporter();
       goto label_003d ;
    }
}
