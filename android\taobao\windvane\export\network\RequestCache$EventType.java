package android.taobao.windvane.export.network.RequestCache$EventType;
import java.lang.annotation.Annotation;

public interface abstract RequestCache$EventType implements Annotation	// class@00016a from classes.dex
{
    public static final int ON_CUSTOM_EVENT = 5;
    public static final int ON_DATA_RECEIVED = 1;
    public static final int ON_ERROR = 2;
    public static final int ON_FINISHED = 3;
    public static final int ON_NETWORK_RESPONSE = 4;
    public static final int ON_RESPONSE;

}
