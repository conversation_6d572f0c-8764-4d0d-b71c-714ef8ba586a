package android.taobao.windvane.extra.jsbridge.TBUploadService;
import android.os.Handler$Callback;
import android.taobao.windvane.jsbridge.api.b;
import tb.t2o;
import android.os.Handler;
import android.os.Looper;
import android.taobao.windvane.jsbridge.api.WVCamera$g;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import java.lang.StringBuilder;
import android.taobao.windvane.util.MimeTypeEnum;
import tb.opw;
import java.io.File;
import tb.vc9;
import tb.nsw;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import tb.x6e;
import tb.omv;
import android.taobao.windvane.extra.jsbridge.TBUploadService$2;
import android.taobao.windvane.extra.jsbridge.TBUploadService$3;
import tb.z6e;
import tb.mzd;
import tb.v7t;
import java.lang.Throwable;
import mtopsdk.mtop.upload.domain.UploadFileInfo;
import mtopsdk.mtop.upload.FileUploadMgr;
import android.taobao.windvane.extra.jsbridge.TBUploadService$4;
import mtopsdk.mtop.upload.FileUploadBaseListener;
import tb.x74;
import com.android.alibaba.ip.runtime.InstantReloadException;
import android.taobao.windvane.webview.IWVWebView;
import tb.y71;
import tb.lex;
import java.util.Map;
import android.taobao.windvane.extra.WVIAdapter;
import android.taobao.windvane.thread.WVThreadPool;
import android.taobao.windvane.extra.jsbridge.TBUploadService$1;
import java.lang.Runnable;
import android.os.Message;
import java.lang.Boolean;
import org.json.JSONObject;
import java.lang.CharSequence;
import tb.vpw;
import tb.wpw;
import tb.sb9;
import org.json.JSONArray;

public class TBUploadService extends b implements Handler$Callback	// class@00019f from classes.dex
{
    private int currentCount;
    private WVCallBackContext mCallback;
    private Handler mHandler;
    private JSONArray multiPictureResult;
    public static IpChange $ipChange;
    private static final String LAST_PIC;
    private static final String MUTI_SELECTION;
    private static final int NOTIFY_ERROR;
    private static final int NOTIFY_FINISH;
    private static final int NOTIFY_START;
    private static final String TAG;

    static {
       t2o.a(0x3d8000d0);
    }
    public void TBUploadService(){
       super();
       this.mHandler = null;
       this.currentCount = 0;
       this.mHandler = new Handler(Looper.getMainLooper(), this);
    }
    public static void access$000(TBUploadService p0,WVCamera$g p1){
       IpChange $ipChange = TBUploadService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("3648d410", objArray);
          return;
       }else {
          p0.doMtopUpload(p1);
          return;
       }
    }
    public static Handler access$100(TBUploadService p0){
       IpChange $ipChange = TBUploadService.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mHandler;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("39b52b8b", objArray);
    }
    private void doMtopUpload(WVCamera$g p0){
       int i = 1;
       String str = "TBUploadService";
       String str1 = "do aus upload ";
       String str2 = ".";
       IpChange $ipChange = TBUploadService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5b3784d1", objArray);
          return;
       }else {
          try{
             File uFile = File.createTempFile("windvane", str2+MimeTypeEnum.JPG.getSuffix(), opw.e().f(i));
             p0.r = uFile.getAbsolutePath();
             if (!vc9.a(new File(p0.a), uFile)) {
                nsw onsw = new nsw();
                onsw.b("errorInfo", "Failed to copy file!");
                this.mCallback.error(onsw);
                return;
             }else {
                omv.a().uploadAsync(new TBUploadService$2(this, p0, uFile), new TBUploadService$3(this, new nsw(), p0), this.mHandler);
                v7t.i(str, str1+p0.a);
                return;
             }
          }catch(java.io.IOException e10){
             v7t.d(str, x74.i(e10));
             return;
          }
       }
    }
    public static Object ipc$super(TBUploadService p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/jsbridge/TBUploadService");
    }
    public void doUpload(WVCamera$g p0,WVCallBackContext p1){
       WVIAdapter b;
       String str = "TBUploadService bizCode:";
       IpChange $ipChange = TBUploadService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("cecb6326", objArray);
          return;
       }else if(p0 == null){
          v7t.a("TBUploadService", "UploadParams is null.");
          p1.error(new nsw());
          return;
       }else {
          this.mCallback = p1;
          y71.commitOffMonitor(p1.getWebview().getUrl(), str+p0.e, p0.d);
          if ("2.0".equals(p0.d)) {
             if ((b = lex.b) != null) {
                b.getLoginInfo(null);
             }
             WVThreadPool.getInstance().execute(new TBUploadService$1(this, p0));
          }else {
             nsw onsw = new nsw();
             onsw.b("msg", "1.0 will not supported, please use 2.0");
             p1.error(onsw);
          }
          return;
       }
    }
    public boolean handleMessage(Message p0){
       int i3;
       int i4;
       int i5;
       int i6;
       int i7;
       int i8;
       int i9;
       int i10;
       String str6;
       String str7;
       String str8;
       JSONArray jSONArray1;
       String str9;
       JSONObject oobject2;
       nsw onsw;
       Message obj;
       String str15;
       object oobject = this;
       object oobject1 = p0;
       int i = 1;
       int i1 = 0;
       String str = "identifier";
       String str1 = "tfsKey";
       String str2 = "images";
       IpChange $ipChange = TBUploadService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,oobject1};
          return $ipChange.ipc$dispatch("282a8c1d", objArray).booleanValue();
       }else {
          String str3 = "\"isLastPic\":\"true\"";
          String str4 = "tempFilePath";
          int i2 = 0;
          switch (oobject1.what){
              case 2001:
              case 2002:
                if (oobject1.obj != null) {
                   if (v7t.h()) {
                      v7t.a("TBUploadService", "upload file success, retString: "+oobject1.obj.m());
                   }
                   String str5 = oobject1.obj.m();
                   try{
                      JSONObject jSONObject = new JSONObject(str5);
                      JSONArray jSONArray = jSONObject.optJSONArray(str2);
                      try{
                         String str10 = jSONObject.optString("url");
                         try{
                            String str11 = jSONObject.optString("resourceURL");
                            try{
                               String str12 = jSONObject.optString("localPath");
                               try{
                                  str4 = jSONObject.optString(str4);
                                  try{
                                     String str13 = jSONObject.optString(str1);
                                     try{
                                        i9 = jSONObject.optString(str);
                                        try{
                                           i = jSONObject.optInt("selectSize");
                                           try{
                                              if (jSONObject.has("base64Data")) {
                                                 i2 = jSONObject.optString("base64Data");
                                              }
                                              String str14 = str2;
                                              str6 = str4;
                                              str7 = i2;
                                              str4 = str10;
                                              str2 = str12;
                                              i2 = i;
                                              jSONArray1 = jSONArray;
                                              str8 = str11;
                                              str9 = str13;
                                              str11 = str3;
                                           label_0113 :
                                              str3 = i9;
                                              try{
                                                 oobject2 = new JSONObject();
                                                 oobject2.put("url", str4);
                                                 oobject2.put("resourceURL", str8);
                                                 oobject2.put("localPath", str2);
                                                 oobject2.put(str1, str9);
                                                 oobject2.put(str, str3);
                                                 if (str7 != null) {
                                                    oobject2.put("base64Data", str7);
                                                 }
                                              }catch(org.json.JSONException e0){
                                                 v7t.d("TBUploadService", x74.i(e0));
                                              }
                                              if (!str5.contains("\"mutipleSelection\":\"1\"")) {
                                                 this.mCallback.success(oobject2.toString());
                                              }else {
                                                 JSONObject jSONObject1 = oobject2;
                                                 TBUploadService tBUploadServ = this;
                                                 vpw.b();
                                                 if (vpw.commonConfig.O0 == null) {
                                                    if (str5.contains(i6)) {
                                                       if (jSONArray1 == null) {
                                                          tBUploadServ.mCallback.success(jSONObject1.toString());
                                                       }else {
                                                          onsw = new nsw();
                                                          onsw.c(i10, jSONArray1);
                                                          tBUploadServ.mCallback.success(onsw);
                                                       }
                                                    }
                                                 }else {
                                                    str5 = i10;
                                                    if (tBUploadServ.multiPictureResult == null) {
                                                       tBUploadServ.multiPictureResult = new JSONArray();
                                                    }
                                                    tBUploadServ.currentCount = tBUploadServ.currentCount + 1;
                                                    tBUploadServ.multiPictureResult.put(jSONObject1);
                                                    if (tBUploadServ.currentCount == i2) {
                                                       onsw = new nsw();
                                                       onsw.c(str5, tBUploadServ.multiPictureResult);
                                                       tBUploadServ.mCallback.success(onsw);
                                                       this.resetMultiPictureResult();
                                                    }
                                                 }
                                                 tBUploadServ.mCallback.fireEvent("WVPhoto.Event.uploadPhotoSuccess", jSONObject1.toString());
                                              }
                                              sb9.c(str6);
                                              i = true;
                                           }catch(org.json.JSONException e0){
                                              i10 = i;
                                           }
                                           v7t.d("TBUploadService", x74.i(e0));
                                           str6 = i4;
                                           str7 = i2;
                                           i2 = i10;
                                           str4 = i5;
                                           str8 = i6;
                                           i10 = str2;
                                           jSONArray1 = i3;
                                           i6 = str3;
                                           str2 = i7;
                                           str9 = i8;
                                           goto label_0113 ;
                                        }catch(org.json.JSONException e0){
                                        }
                                     }catch(org.json.JSONException e0){
                                        i9 = i2;
                                     label_0117 :
                                        i10 = 0;
                                     }
                                  }catch(org.json.JSONException e0){
                                     i8 = i2;
                                  }
                               }catch(org.json.JSONException e0){
                                  i4 = i2;
                                  i8 = i4;
                               label_0121 :
                                  i9 = i8;
                                  goto label_0117 ;
                               }
                            }catch(org.json.JSONException e0){
                               i4 = i2;
                               i7 = i4;
                            }
                            i8 = i7;
                            goto label_0121 ;
                         }catch(org.json.JSONException e0){
                            i4 = i2;
                            i6 = i4;
                         }
                         i7 = i6;
                      }catch(org.json.JSONException e0){
                         i4 = i2;
                      }
                      i5 = i4;
                      i6 = i5;
                   }catch(org.json.JSONException e0){
                      i3 = i2;
                      i4 = i3;
                   }
                }
                return i;
                break;
              case 2003:
                if ((obj = oobject1.obj) != null) {
                   Message message = obj;
                   str = message.m();
                   try{
                      str15 = new JSONObject(str).optString(str4);
                   }catch(org.json.JSONException e0){
                      v7t.d("TBUploadService", x74.i(e0));
                   }
                   if (str.contains("\"mutipleSelection\":\"1\"")) {
                      oobject.mCallback.fireEvent("WVPhoto.Event.uploadPhotoFailed", str);
                      vpw.b();
                      if (vpw.commonConfig.O0 == null) {
                         if (str.contains(str3)) {
                            oobject.mCallback.error(message);
                         }
                      }else {
                         oobject.mCallback.error(message);
                      }
                      this.resetMultiPictureResult();
                   }else {
                      oobject.mCallback.error(message);
                   }
                   sb9.c(str15);
                }else {
                   oobject.mCallback.error();
                }
                return i;
                break;
              default:
                return i1;
          }
          v7t.a("TBUploadService", "start upload file ...");
          oobject.mCallback.fireEvent("WVPhoto.Event.prepareUploadPhotoSuccess", "{}");
          return i;
       }
    }
    public void resetMultiPictureResult(){
       IpChange $ipChange = TBUploadService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("c33741b8", objArray);
          return;
       }else {
          this.multiPictureResult = null;
          this.currentCount = 0;
          return;
       }
    }
}
