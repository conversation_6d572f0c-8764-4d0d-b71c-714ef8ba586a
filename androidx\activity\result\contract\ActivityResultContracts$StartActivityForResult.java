package androidx.activity.result.contract.ActivityResultContracts$StartActivityForResult;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.activity.result.contract.ActivityResultContracts$StartActivityForResult$Companion;
import tb.a07;
import android.content.Context;
import android.content.Intent;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.activity.result.ActivityResult;

public final class ActivityResultContracts$StartActivityForResult extends ActivityResultContract	// class@0004dc from classes.dex
{
    public static final ActivityResultContracts$StartActivityForResult$Companion Companion;
    public static final String EXTRA_ACTIVITY_OPTIONS_BUNDLE;

    static {
       ActivityResultContracts$StartActivityForResult.Companion = new ActivityResultContracts$StartActivityForResult$Companion(null);
    }
    public void ActivityResultContracts$StartActivityForResult(){
       super();
    }
    public Intent createIntent(Context p0,Intent p1){
       ckf.g(p0, "context");
       ckf.g(p1, "input");
       return p1;
    }
    public Intent createIntent(Context p0,Object p1){
       return this.createIntent(p0, p1);
    }
    public ActivityResult parseResult(int p0,Intent p1){
       return new ActivityResult(p0, p1);
    }
    public Object parseResult(int p0,Intent p1){
       return this.parseResult(p0, p1);
    }
}
