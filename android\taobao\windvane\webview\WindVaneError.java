package android.taobao.windvane.webview.WindVaneError;
import java.lang.Error;
import tb.t2o;
import java.lang.String;
import java.lang.Throwable;

public class WindVaneError extends Error	// class@000319 from classes.dex
{
    private static final long serialVersionUID = 0x793c8608d1db9044;

    static {
       t2o.a(0x3d8002ff);
    }
    public void WindVaneError(){
       super();
    }
    public void WindVaneError(String p0){
       super(p0);
    }
    public void WindVaneError(String p0,Throwable p1){
       super(p0, p1);
    }
    public void WindVaneError(Throwable p0){
       super(p0);
    }
}
