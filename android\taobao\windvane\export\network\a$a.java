package android.taobao.windvane.export.network.a$a;
import java.lang.Runnable;
import android.taobao.windvane.export.network.a;
import android.taobao.windvane.export.network.Request;
import android.taobao.windvane.export.network.RequestCallback;
import android.os.Handler;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class a$a implements Runnable	// class@00016f from classes.dex
{
    public final Request a;
    public final RequestCallback b;
    public final Handler c;
    public final a d;
    public static IpChange $ipChange;

    public void a$a(a p0,Request p1,RequestCallback p2,Handler p3){
       this.d = p0;
       this.a = p1;
       this.b = p2;
       this.c = p3;
       super();
    }
    public void run(){
       IpChange $ipChange = a$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          a.a(this.d, this.a, this.b, this.c);
          return;
       }
    }
}
