package android.taobao.windvane.extra.jsi.JsiMode;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;
import java.lang.Boolean;

public final class JsiMode extends Enum	// class@0001ac from classes.dex
{
    private final boolean enableQjs;
    private final boolean enableV8;
    private static final JsiMode[] $VALUES;
    public static IpChange $ipChange;
    public static final JsiMode QJS;
    public static final JsiMode V8;
    public static final JsiMode V8_QJS;

    static {
       JsiMode jsiMode = new JsiMode("V8", 0, true, 0);
       JsiMode.V8 = jsiMode;
       JsiMode jsiMode1 = new JsiMode("QJS", true, 0, true);
       JsiMode.QJS = jsiMode1;
       JsiMode jsiMode2 = new JsiMode("V8_QJS", 2, true, true);
       JsiMode.V8_QJS = jsiMode2;
       JsiMode[] jsiModeArray = new JsiMode[]{jsiMode,jsiMode1,jsiMode2};
       JsiMode.$VALUES = jsiModeArray;
    }
    private void JsiMode(String p0,int p1,boolean p2,boolean p3){
       super(p0, p1);
       this.enableV8 = p2;
       this.enableQjs = p3;
    }
    public static Object ipc$super(JsiMode p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/jsi/JsiMode");
    }
    public static JsiMode valueOf(String p0){
       IpChange $ipChange = JsiMode.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(JsiMode.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("45a462f9", objArray);
    }
    public static JsiMode[] values(){
       IpChange $ipChange = JsiMode.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return JsiMode.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("da503de8", objArray);
    }
    public boolean isQjsEnable(){
       IpChange $ipChange = JsiMode.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.enableQjs;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c2f8089e", objArray).booleanValue();
    }
    public boolean isV8Enable(){
       IpChange $ipChange = JsiMode.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.enableV8;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("d82052fa", objArray).booleanValue();
    }
}
