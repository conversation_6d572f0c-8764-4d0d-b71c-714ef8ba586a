package android.taobao.windvane.extra.uc.WVUCWebView$WVValueCallback;
import android.webkit.ValueCallback;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;
import android.taobao.windvane.extra.core.WVCore;
import tb.v7t;
import android.taobao.windvane.extra.uc.WVUCWebView;
import tb.iuv;
import tb.vpw;
import tb.wpw;
import tb.y71;
import java.lang.Throwable;

public class WVUCWebView$WVValueCallback implements ValueCallback	// class@000260 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800196);
    }
    public void WVUCWebView$WVValueCallback(){
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       IpChange $ipChange = WVUCWebView$WVValueCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else {
          v7t.i("WVUCWebView", "support : "+WVCore.getInstance().isUCSupport()+" UC SDK Callback : "+p0);
          iuv.commitEvent(0x3bcd, String.valueOf(WVCore.getInstance().isUCSupport()), String.valueOf(WVUCWebView.getUseTaobaoNetwork()), p0);
          if (vpw.commonConfig.A3 != null) {
             y71.commitUCWebViewSDKInfo(p0);
          }
          return;
       }
    }
}
