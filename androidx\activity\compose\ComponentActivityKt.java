package androidx.activity.compose.ComponentActivityKt;
import android.view.ViewGroup$LayoutParams;
import androidx.activity.ComponentActivity;
import tb.ja20;
import tb.u1a;
import android.view.Window;
import android.app.Activity;
import android.view.View;
import android.view.ViewGroup;
import androidx.compose.ui.platform.ComposeView;
import androidx.compose.ui.platform.AbstractComposeView;
import android.content.Context;
import android.util.AttributeSet;
import tb.a07;
import java.lang.Object;
import androidx.lifecycle.LifecycleOwner;
import androidx.lifecycle.ViewTreeLifecycleOwner;
import androidx.lifecycle.ViewModelStoreOwner;
import androidx.lifecycle.ViewTreeViewModelStoreOwner;
import androidx.savedstate.SavedStateRegistryOwner;
import androidx.savedstate.ViewTreeSavedStateRegistryOwner;

public final class ComponentActivityKt	// class@000482 from classes.dex
{
    private static final ViewGroup$LayoutParams DefaultActivityContentLayoutParams;

    static {
       ComponentActivityKt.DefaultActivityContentLayoutParams = new ViewGroup$LayoutParams(-2, -2);
    }
    public static final void setContent(ComponentActivity p0,ja20 p1,u1a p2){
       View childAt = p0.getWindow().getDecorView().findViewById(0x1020002).getChildAt(0);
       if (childAt instanceof ComposeView) {
       }else {
          childAt = null;
       }
       if (childAt != null) {
          childAt.setParentCompositionContext(p1);
          childAt.setContent(p2);
       }else {
          ComposeView childAt1 = new ComposeView(p0, null, 0, 6, null);
          childAt.setParentCompositionContext(p1);
          childAt.setContent(p2);
          ComponentActivityKt.setOwners(p0);
          p0.setContentView(childAt, ComponentActivityKt.DefaultActivityContentLayoutParams);
       }
       return;
    }
    public static void setContent$default(ComponentActivity p0,ja20 p1,u1a p2,int p3,Object p4){
       if ((p3 & 0x01)) {
          p1 = null;
       }
       ComponentActivityKt.setContent(p0, p1, p2);
       return;
    }
    private static final void setOwners(ComponentActivity p0){
       View decorView = p0.getWindow().getDecorView();
       if (ViewTreeLifecycleOwner.get(decorView) == null) {
          ViewTreeLifecycleOwner.set(decorView, p0);
       }
       if (ViewTreeViewModelStoreOwner.get(decorView) == null) {
          ViewTreeViewModelStoreOwner.set(decorView, p0);
       }
       if (ViewTreeSavedStateRegistryOwner.get(decorView) == null) {
          ViewTreeSavedStateRegistryOwner.set(decorView, p0);
       }
       return;
    }
}
