package android.taobao.windvane.extra.performance.WVPagePerformance;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Number;
import java.lang.Boolean;
import java.lang.Integer;
import java.lang.Long;
import tb.avt;

public class WVPagePerformance	// class@0001cf from classes.dex
{
    private long h5_PP_FP;
    private long h5_PP_FSP;
    private long h5_PP_FSP_uptime;
    private long h5_PP_T1;
    private long h5_PP_T1_uptime;
    private long h5_PP_T2;
    private long h5_PP_T2_uptime;
    private long h5_PP_TTI;
    public long h5_PP_domComplete;
    public long h5_PP_domComplete_uptime;
    private long h5_PP_domContentLoadedEventStart;
    private long h5_PP_domContentLoadedEventStart_uptime;
    public long h5_PP_domLoading;
    public long h5_PP_domLoading_uptime;
    private String h5_PP_errorCode;
    private String h5_PP_errorMessage;
    private long h5_PP_fetchStart;
    private long h5_PP_fetchStart_uptime;
    private long h5_PP_finishLoad;
    private long h5_PP_finishLoad_uptime;
    private long h5_PP_interceptEnd;
    private long h5_PP_interceptStart;
    private boolean h5_PP_isFinished;
    private long h5_PP_loadEventEnd;
    private long h5_PP_loadEventEnd_uptime;
    private long h5_PP_loadEventStart;
    private long h5_PP_loadEventStart_uptime;
    private long h5_PP_navigationStart;
    private long h5_PP_navigationStart_uptime;
    public long h5_PP_requestStart;
    public long h5_PP_requestStart_uptime;
    private long h5_PP_responseEnd;
    private long h5_PP_responseEnd_uptime;
    private long h5_PP_startLoad;
    private long h5_PP_startLoad_uptime;
    private int h5_core_type;
    private String h5_core_type_str;
    private String url;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800104);
    }
    public void WVPagePerformance(){
       super();
    }
    public int getH5_Core_Type(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_core_type;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8d57ad8", objArray).intValue();
    }
    public String getH5_Core_Type_Str(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_core_type_str;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1acff149", objArray);
    }
    public long getH5_PP_FP(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return (this.h5_PP_FP + this.getH5_PP_navigationStart());
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("e4d888e8", objArray).longValue();
    }
    public long getH5_PP_FP_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return (this.h5_PP_FP + this.getH5_PP_navigationStart_uptime());
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("d1ebd8f5", objArray).longValue();
    }
    public long getH5_PP_FSP(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_FSP;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("bd38efdb", objArray).longValue();
    }
    public long getH5_PP_FSP_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_FSP_uptime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("202a4aa2", objArray).longValue();
    }
    public long getH5_PP_T1(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_T1;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("fb0788fb", objArray).longValue();
    }
    public long getH5_PP_T1_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_T1_uptime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ee932d82", objArray).longValue();
    }
    public long getH5_PP_T2(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_T2;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("fb15a07c", objArray).longValue();
    }
    public long getH5_PP_T2_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_T2_uptime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("f644d2e1", objArray).longValue();
    }
    public long getH5_PP_TTI(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("a3226181", objArray).longValue();
       }else {
          WVPagePerformance th5_PP_TTI = this.h5_PP_TTI;
          if (!(th5_PP_TTI)) {
             return this.getH5_PP_FSP();
          }
          return (th5_PP_TTI + this.getH5_PP_navigationStart());
       }
    }
    public long getH5_PP_TTI_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("32f2d23c", objArray).longValue();
       }else {
          WVPagePerformance th5_PP_TTI = this.h5_PP_TTI;
          if (!(th5_PP_TTI)) {
             return this.getH5_PP_FSP_uptime();
          }
          return (th5_PP_TTI + this.getH5_PP_navigationStart_uptime());
       }
    }
    public long getH5_PP_domContentLoadedEventStart(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_domContentLoadedEventStart;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("4bc364e2", objArray).longValue();
    }
    public long getH5_PP_domContentLoadedEventStart_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_domContentLoadedEventStart_uptime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("c30a6fbb", objArray).longValue();
    }
    public String getH5_PP_errorCode(){
       WVPagePerformance th5_PP_error;
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("b3ce45a7", objArray);
       }else if((th5_PP_error = this.h5_PP_errorCode) == null){
          th5_PP_error = "";
       }
       return th5_PP_error;
    }
    public String getH5_PP_errorMessage(){
       WVPagePerformance th5_PP_error;
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("bce594f7", objArray);
       }else if((th5_PP_error = this.h5_PP_errorMessage) == null){
          th5_PP_error = "";
       }
       return th5_PP_error;
    }
    public long getH5_PP_fetchStart(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_fetchStart;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("b8566fe6", objArray).longValue();
    }
    public long getH5_PP_fetchStart_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_fetchStart_uptime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("e789b837", objArray).longValue();
    }
    public long getH5_PP_finishLoad(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_finishLoad;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("f5de31b7", objArray).longValue();
    }
    public long getH5_PP_finishLoad_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_finishLoad_uptime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("45cc7946", objArray).longValue();
    }
    public long getH5_PP_interceptEnd(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_interceptEnd;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2bdfe197", objArray).longValue();
    }
    public long getH5_PP_interceptStart(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_interceptStart;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("17fa955e", objArray).longValue();
    }
    public boolean getH5_PP_isFinished(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_isFinished;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ef7ecbea", objArray).booleanValue();
    }
    public long getH5_PP_loadEventEnd(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_loadEventEnd;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2ae3cc5", objArray).longValue();
    }
    public long getH5_PP_loadEventEnd_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_loadEventEnd_uptime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8a3a8278", objArray).longValue();
    }
    public long getH5_PP_loadEventStart(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_loadEventStart;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("749edd0c", objArray).longValue();
    }
    public long getH5_PP_loadEventStart_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_loadEventStart_uptime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("95c95451", objArray).longValue();
    }
    public long getH5_PP_navigationStart(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_navigationStart;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2cdc6466", objArray).longValue();
    }
    public long getH5_PP_navigationStart_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_navigationStart_uptime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("d2bfb3b7", objArray).longValue();
    }
    public long getH5_PP_responseEnd(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_responseEnd;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("8bc92712", objArray).longValue();
    }
    public long getH5_PP_responseEnd_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_responseEnd_uptime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("819ed78b", objArray).longValue();
    }
    public long getH5_PP_startLoad(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_startLoad;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5f9c2ee0", objArray).longValue();
    }
    public long getH5_PP_startLoad_uptime(){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h5_PP_startLoad_uptime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9d80bfd", objArray).longValue();
    }
    public String getUrl(){
       WVPagePerformance turl;
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("de8f0660", objArray);
       }else if((turl = this.url) == null){
          turl = "";
       }
       return turl;
    }
    public void setH5_Core_Type(int p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("ed706132", objArray);
          return;
       }else {
          this.h5_core_type = p0;
          return;
       }
    }
    public void setH5_Core_Type_Str(String p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("7b70c64d", objArray);
          return;
       }else {
          this.h5_core_type_str = p0;
          return;
       }
    }
    public void setH5_PP_FP(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("490f72c4", objArray);
          return;
       }else {
          this.h5_PP_FP = p0;
          return;
       }
    }
    public void setH5_PP_FSP(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("b1dbebc9", objArray);
          return;
       }else {
          this.h5_PP_FSP = p0;
          return;
       }
    }
    public void setH5_PP_FSP_uptime(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("b532344a", objArray);
          return;
       }else {
          this.h5_PP_FSP_uptime = p0;
          return;
       }
    }
    public void setH5_PP_T1(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("f8c07511", objArray);
          return;
       }else {
          this.h5_PP_T1 = p0;
          return;
       }
    }
    public void setH5_PP_T1_uptime(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("5e262a02", objArray);
          return;
       }else {
          this.h5_PP_T1_uptime = p0;
          return;
       }
    }
    public void setH5_PP_T2(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("fa754db0", objArray);
          return;
       }else {
          this.h5_PP_T2 = p0;
          return;
       }
    }
    public void setH5_PP_T2_uptime(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("4ca93083", objArray);
          return;
       }else {
          this.h5_PP_T2_uptime = p0;
          return;
       }
    }
    public void setH5_PP_TTI(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("8920aee3", objArray);
          return;
       }else {
          this.h5_PP_TTI = p0;
          return;
       }
    }
    public void setH5_PP_domComplete(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("eb5ad051", objArray);
          return;
       }else {
          this.h5_PP_domComplete = p0;
          this.h5_PP_domComplete_uptime = avt.a(p0);
          return;
       }
    }
    public void setH5_PP_domContentLoadedEventStart(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("4210fa0a", objArray);
          return;
       }else {
          this.h5_PP_domContentLoadedEventStart = p0;
          this.h5_PP_domContentLoadedEventStart_uptime = avt.a(p0);
          return;
       }
    }
    public void setH5_PP_domLoading(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("8a3256f4", objArray);
          return;
       }else {
          this.h5_PP_domLoading = p0;
          this.h5_PP_domLoading_uptime = avt.a(p0);
          return;
       }
    }
    public void setH5_PP_errorCode(String p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("80224417", objArray);
          return;
       }else if(p0 == null){
          return;
       }else {
          this.h5_PP_errorCode = p0;
          return;
       }
    }
    public void setH5_PP_errorMessage(String p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("439ec55f", objArray);
          return;
       }else if(p0 == null){
          return;
       }else {
          this.h5_PP_errorMessage = p0;
          return;
       }
    }
    public void setH5_PP_fetchStart(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("228ab786", objArray);
          return;
       }else {
          this.h5_PP_fetchStart = p0;
          this.h5_PP_fetchStart_uptime = avt.a(p0);
          return;
       }
    }
    public void setH5_PP_finishLoad(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("95fb2fd5", objArray);
          return;
       }else {
          this.h5_PP_finishLoad = p0;
          return;
       }
    }
    public void setH5_PP_finishLoad_uptime(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("6eac8abe", objArray);
          return;
       }else {
          this.h5_PP_finishLoad_uptime = p0;
          return;
       }
    }
    public void setH5_PP_interceptEnd(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("664caef5", objArray);
          return;
       }else {
          this.h5_PP_interceptEnd = p0;
          return;
       }
    }
    public void setH5_PP_interceptStart(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("3124e70e", objArray);
          return;
       }else {
          this.h5_PP_interceptStart = p0;
          return;
       }
    }
    public void setH5_PP_isFinished(boolean p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("d06e1622", objArray);
          return;
       }else {
          this.h5_PP_isFinished = p0;
          return;
       }
    }
    public void setH5_PP_loadEventEnd(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("6949b987", objArray);
          return;
       }else {
          this.h5_PP_loadEventEnd = p0;
          this.h5_PP_loadEventEnd_uptime = avt.a(p0);
          return;
       }
    }
    public void setH5_PP_loadEventStart(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("69099520", objArray);
          return;
       }else {
          this.h5_PP_loadEventStart = p0;
          this.h5_PP_loadEventStart_uptime = avt.a(p0);
          return;
       }
    }
    public void setH5_PP_navigationStart(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("5e6f569e", objArray);
          return;
       }else {
          this.h5_PP_navigationStart = p0;
          this.h5_PP_navigationStart_uptime = avt.a(p0);
          return;
       }
    }
    public void setH5_PP_requestStart(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("157e14db", objArray);
          return;
       }else {
          this.h5_PP_requestStart = p0;
          this.h5_PP_requestStart_uptime = avt.a(p0);
          return;
       }
    }
    public void setH5_PP_responseEnd(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("1f9fcf72", objArray);
          return;
       }else {
          this.h5_PP_responseEnd = p0;
          this.h5_PP_responseEnd_uptime = avt.a(p0);
          return;
       }
    }
    public void setH5_PP_startLoad(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("e3d5464", objArray);
          return;
       }else {
          this.h5_PP_startLoad = p0;
          return;
       }
    }
    public void setH5_PP_startLoad_uptime(long p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("3941f74f", objArray);
          return;
       }else {
          this.h5_PP_startLoad_uptime = p0;
          return;
       }
    }
    public void setUrl(String p0){
       IpChange $ipChange = WVPagePerformance.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e1dea87e", objArray);
          return;
       }else {
          this.url = p0;
          return;
       }
    }
}
