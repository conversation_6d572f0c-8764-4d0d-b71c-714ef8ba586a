package android.taobao.windvane.jsbridge.api.b;
import tb.t2o;
import java.lang.Object;
import android.taobao.windvane.jsbridge.api.WVCamera$g;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import android.content.Context;
import android.taobao.windvane.webview.IWVWebView;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public abstract class b	// class@0002f1 from classes.dex
{
    public Context mContext;
    public IWVWebView mWebView;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d80024a);
    }
    public void b(){
       super();
    }
    public abstract void doUpload(WVCamera$g p0,WVCallBackContext p1);
    public void initialize(Context p0,IWVWebView p1){
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("940c25b5", objArray);
          return;
       }else {
          this.mContext = p0;
          this.mWebView = p1;
          return;
       }
    }
    public void resetMultiPictureResult(){
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("c33741b8", objArray);
       }
       return;
    }
}
