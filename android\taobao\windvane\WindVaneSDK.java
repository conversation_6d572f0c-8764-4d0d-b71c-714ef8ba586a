package android.taobao.windvane.WindVaneSDK;
import tb.t2o;
import java.lang.Object;
import android.content.Context;
import java.lang.String;
import tb.lpw;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import tb.v7t;
import android.app.Application;
import tb.yaa;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.opw;
import tb.zpw;
import android.content.res.Resources;
import android.content.res.AssetManager;
import java.io.File;
import tb.vc9;
import java.io.InputStream;
import com.taobao.codetrack.sdk.assets.AssetsDelegate;
import com.taobao.android.ab.api.ABGlobal;
import tb.mpw;
import tb.xg4;
import tb.iuv;
import tb.hrw;
import tb.drw;
import tb.irw;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Class;
import java.lang.reflect.Method;
import java.lang.Throwable;
import java.lang.IllegalArgumentException;
import java.lang.NullPointerException;
import tb.vpw;
import tb.btw;
import tb.eqw;
import tb.ypw;
import tb.xsw;
import android.taobao.windvane.config.WVConfigManager;
import tb.ipb;
import java.lang.Boolean;
import tb.ssw;
import android.taobao.windvane.config.EnvEnum;
import java.lang.StringBuilder;
import android.os.Build$VERSION;
import tb.x74;
import tb.kex;

public class WindVaneSDK	// class@000148 from classes.dex
{
    public static IpChange $ipChange;
    private static final String SPNAME_ENV;
    private static final String TAG;
    private static final String VALUE_NAME;
    private static final String WV_MULT;
    private static boolean initialized;
    private static boolean settedDataDirSuffix;

    static {
       t2o.a(0x3d800005);
       WindVaneSDK.initialized = false;
       WindVaneSDK.settedDataDirSuffix = false;
    }
    public void WindVaneSDK(){
       super();
    }
    public static void init(Context p0,String p1,int p2,lpw p3){
       IpChange $ipChange = WindVaneSDK.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Integer(p2),p3};
          $ipChange.ipc$dispatch("e3c2ca38", objArray);
          return;
       }else {
          WindVaneSDK.init(p0, p1, p3);
          return;
       }
    }
    public static void init(Context p0,String p1,lpw p2){
       File[] uFileArray;
       if (WindVaneSDK.initialized) {
          v7t.i("WindVaneSDK", "WindVaneSDK has already initialized");
          return;
       }else {
          v7t.i("WindVaneSDK", "WindVaneSDK init");
          if (p0 == null) {
             throw new NullPointerException("init error, context is null");
          }
          WindVaneSDK.webviewTarget28Support(p0);
          Application uApplication = (p0 instanceof Application)? p0: p0.getApplicationContext();
          yaa.n = uApplication;
          if (uApplication != null) {
             if (TextUtils.isEmpty(p1)) {
                p1 = "caches";
             }
             opw.e().g(p0, p1, 0);
             zpw.b(p0);
             AssetManager assets = yaa.n.getResources().getAssets();
             try{
                File uFile = vc9.e(yaa.n, "windvane/ucsdk");
                if ((uFileArray = uFile.listFiles()) != null && !uFileArray.length) {
                   vc9.h(AssetsDelegate.proxy_open(assets, "uclibs.zip"), uFile.getAbsolutePath());
                }
                p2.h = uFile.getAbsolutePath();
                v7t.i("WindVaneSDK", "UC init by uclibs");
             }catch(java.io.IOException e0){
             }
             if (ABGlobal.isFeatureOpened(p0, "OptInitWindVane")) {
                if (!mpw.a().b()) {
                   yaa.f().k(p2);
                }
             }else {
                yaa.f().k(p2);
             }
             xg4.k();
             iuv.init();
             WindVaneSDK.initConfig();
             irw.d(new hrw());
             v7t.i("WindVaneSDK", "trying to init uc core ");
             Class[] uClassArray = new Class[0];
             Object[] objArray = new Object[0];
             WVUCWebView.class.getDeclaredMethod("staticInitializeOnce", uClassArray).invoke(null, objArray);
             v7t.i("WindVaneSDK", "init windvane called");
             WindVaneSDK.initialized = true;
             return;
          }else {
             throw new IllegalArgumentException("init error, context should be Application or its subclass");
          }
       }
    }
    public static void init(Context p0,lpw p1){
       IpChange $ipChange = WindVaneSDK.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("f65be601", objArray);
          return;
       }else {
          WindVaneSDK.init(p0, null, 0, p1);
          return;
       }
    }
    private static void initConfig(){
       IpChange $ipChange = WindVaneSDK.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("6811c3f9", objArray);
          return;
       }else {
          vpw.b().d();
          btw.c().e();
          eqw.c().e();
          ypw.b().d();
          xsw.b().d();
          WVConfigManager.a().b("windvane_common", vpw.b());
          WVConfigManager.a().b("windvane_domain", eqw.c());
          WVConfigManager.a().b("WindVane_URL_config", btw.c());
          WVConfigManager.a().b("cookie_black_list", ypw.b());
          WVConfigManager.a().b("windvane_uc_core", xsw.b());
          return;
       }
    }
    public static void initURLCache(Context p0,String p1,int p2){
       IpChange $ipChange = WindVaneSDK.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Integer(p2)};
          $ipChange.ipc$dispatch("5293ba3b", objArray);
          return;
       }else {
          opw.e().g(p0, p1, p2);
          return;
       }
    }
    public static boolean isInitialized(){
       IpChange $ipChange = WindVaneSDK.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WindVaneSDK.initialized;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("18d112d5", objArray).booleanValue();
    }
    public static boolean isTrustedUrl(String p0){
       IpChange $ipChange = WindVaneSDK.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return ssw.j(p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("2c579ad7", objArray).booleanValue();
    }
    public static void setEnvMode(EnvEnum p0){
       String str = "evn_value";
       String str1 = "wv_evn";
       String str2 = "setEnvMode : ";
       IpChange $ipChange = WindVaneSDK.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("c73c191f", objArray);
          return;
       }else if(p0 != null){
          v7t.i(str1, str2+p0.getValue());
          yaa.j = p0;
          if (!(xg4.g(str1, str) - (long)p0.getKey())) {
             return;
          }else {
             WVConfigManager.a().c();
             xg4.l(str1, str, (long)p0.getKey());
          }
       }
       return;
    }
    public static void webviewTarget28Support(Context p0){
       int i = 1;
       IpChange $ipChange = WindVaneSDK.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          $ipChange.ipc$dispatch("69ff4e82", objArray);
          return;
       }else if(Build$VERSION.SDK_INT >= 28){
          String str = x74.h(p0);
          if (!TextUtils.isEmpty(str)) {
             kex.a(str);
             WindVaneSDK.settedDataDirSuffix = i;
          }
       }
       return;
    }
}
