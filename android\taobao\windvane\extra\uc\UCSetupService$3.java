package android.taobao.windvane.extra.uc.UCSetupService$3;
import com.uc.webview.export.extension.U4Engine$InitializerClient;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.lang.ClassLoader;
import com.android.alibaba.ip.runtime.IpChange;
import tb.v7t;
import android.taobao.windvane.startup.UCInitializerInfo;
import tb.bf1;
import tb.r9u;
import java.io.File;
import java.lang.Integer;
import com.uc.webview.export.extension.U4Engine$IDownloadHandle;
import java.lang.Boolean;
import android.taobao.windvane.extra.uc.UCSetupService$DownloadController;
import android.taobao.windvane.extra.uc.WVUCWebView;
import com.uc.webview.export.extension.IRunningCoreInfo;
import com.uc.webview.export.extension.IRunningCoreInfo$FailedInfo;
import com.uc.webview.base.UCKnownException;
import com.alibaba.fastjson.JSONObject;
import java.lang.Throwable;
import tb.y71;
import java.lang.System;
import android.taobao.windvane.extra.uc.UCSetupService;
import tb.yaa;
import android.content.Context;
import android.taobao.windvane.extra.core.WVRunningCoreInfo;

public final class UCSetupService$3 extends U4Engine$InitializerClient	// class@000225 from classes.dex
{
    public final String val$compressedLibPath;
    public final String val$specifiedDir;
    public final long val$start;
    public static IpChange $ipChange;

    public void UCSetupService$3(long p0,String p1,String p2){
       this.val$start = p0;
       this.val$specifiedDir = p1;
       this.val$compressedLibPath = p2;
       super();
    }
    public static Object ipc$super(UCSetupService$3 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/UCSetupService$3");
    }
    public void onDexReady(ClassLoader p0){
       IpChange $ipChange = UCSetupService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("ce93ec58", objArray);
          return;
       }else {
          v7t.d("UCSetupService", "initU4 onDexReady loader:"+p0);
          UCInitializerInfo.a().c(5);
          r9u.c(bf1.LOAD_DEX);
          return;
       }
    }
    public void onDownloadFinish(String p0,File p1){
       IpChange $ipChange = UCSetupService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("6e75e042", objArray);
          return;
       }else {
          v7t.d("UCSetupService", "onDownloadFinish:"+p0+", savedFile:"+p1.getAbsolutePath());
          UCInitializerInfo.a().c(2);
          return;
       }
    }
    public void onDownloadProgress(int p0){
       IpChange $ipChange = UCSetupService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("49af8908", objArray);
       }
       return;
    }
    public boolean onDownloadStart(String p0,U4Engine$IDownloadHandle p1){
       IpChange $ipChange = UCSetupService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("92f77ae7", objArray).booleanValue();
       }else {
          v7t.d("UCSetupService", "onDownloadStart:"+p0);
          UCInitializerInfo.a().c(1);
          if (!UCSetupService$DownloadController.getInstance().shouldDelay()) {
             return 1;
          }
          WVUCWebView.onUCMCoreDownloadIntercepted();
          UCSetupService$DownloadController.getInstance().delay(p1);
          return 0;
       }
    }
    public void onExtractFinish(File p0){
       IpChange $ipChange = UCSetupService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d9cdff53", objArray);
          return;
       }else {
          v7t.d("UCSetupService", "initU4 onExtractFinish dir:"+p0.getAbsolutePath());
          UCInitializerInfo.a().c(4);
          return;
       }
    }
    public boolean onExtractStart(File p0,File p1){
       int i = 3;
       IpChange $ipChange = UCSetupService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          objArray[1] = p0;
          objArray[2] = p1;
          return $ipChange.ipc$dispatch("e3a86699", objArray).booleanValue();
       }else {
          v7t.d("UCSetupService", "initU4 onExtractStart dir:"+p1.getAbsolutePath());
          v7t.d("UCSetupService", "initU4 onExtractStart:true");
          UCInitializerInfo.a().c(i);
          return 1;
       }
    }
    public void onFailed(IRunningCoreInfo p0){
       String str;
       String str1;
       IpChange $ipChange = UCSetupService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e994354d", objArray);
          return;
       }else {
          v7t.d("UCSetupService", "initU4 onFailed UC ExceptionValueCallback : "+p0);
          UCInitializerInfo.a().c(8);
          r9u.c(bf1.CORE_INIT);
          UCKnownException uCKnownExcep = (p0.failedInfo() != null)? p0.failedInfo().exception(): null;
          WVUCWebView.onUCMCoreInitFailed(uCKnownExcep);
          IRunningCoreInfo$FailedInfo uFailedInfo = p0.failedInfo();
          JSONObject jSONObject = new JSONObject();
          if (uFailedInfo != null) {
             str = uFailedInfo.reason();
             str1 = String.valueOf(uFailedInfo.errorCode());
          }else {
             str1 = "unknown";
             str = (uCKnownExcep == null)? str1: uCKnownExcep.getMessage();
          }
          jSONObject.put("message", str);
          jSONObject.put("specifiedDir", this.val$specifiedDir);
          jSONObject.put("compressedLibPath", this.val$compressedLibPath);
          y71.commitFail("wv_uc_init_v1", str1, jSONObject);
          return;
       }
    }
    public void onInitStart(IRunningCoreInfo p0){
       int i = 0;
       IpChange $ipChange = UCSetupService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("69438924", objArray);
          return;
       }else {
          v7t.d("UCSetupService", "initU4 onInitStart");
          UCInitializerInfo.a().c(i);
          return;
       }
    }
    public void onNativeReady(File p0){
       IpChange $ipChange = UCSetupService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("30cecceb", objArray);
          return;
       }else {
          v7t.d("UCSetupService", "initU4 onNativeReady libDir:"+p0.getAbsolutePath());
          UCInitializerInfo.a().c(6);
          r9u.c(bf1.LOAD_NATIVE);
          return;
       }
    }
    public void onSuccess(IRunningCoreInfo p0){
       Application n;
       IpChange $ipChange = UCSetupService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("aa94dd75", objArray);
          return;
       }else {
          v7t.d("UCSetupService", "initU4 onSuccess info:"+p0+", cost:"+(System.currentTimeMillis() - this.val$start));
          UCInitializerInfo.a().c(7);
          r9u.c(bf1.CORE_INIT);
          UCSetupService.configGlobalSettings();
          if ((n = yaa.n) != null) {
             WVUCWebView.onUCMCoreSwitched(n, this.val$start, p0);
          }
          WVRunningCoreInfo.setRunningCoreInfo(p0);
          return;
       }
    }
}
