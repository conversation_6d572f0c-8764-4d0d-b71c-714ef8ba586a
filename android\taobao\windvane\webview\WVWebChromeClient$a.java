package android.taobao.windvane.webview.WVWebChromeClient$a;
import android.webkit.ConsoleMessage$MessageLevel;
import java.lang.Enum;

public class WVWebChromeClient$a	// class@00030d from classes.dex
{
    public static final int[] $SwitchMap$android$webkit$ConsoleMessage$MessageLevel;

    static {
       int[] ointArray = new int[ConsoleMessage$MessageLevel.values().length];
       try{
          WVWebChromeClient$a.$SwitchMap$android$webkit$ConsoleMessage$MessageLevel = ointArray;
          ointArray[ConsoleMessage$MessageLevel.WARNING.ordinal()] = 1;
          try{
             WVWebChromeClient$a.$SwitchMap$android$webkit$ConsoleMessage$MessageLevel[ConsoleMessage$MessageLevel.ERROR.ordinal()] = 2;
             try{
                WVWebChromeClient$a.$SwitchMap$android$webkit$ConsoleMessage$MessageLevel[ConsoleMessage$MessageLevel.LOG.ordinal()] = 3;
                try{
                   WVWebChromeClient$a.$SwitchMap$android$webkit$ConsoleMessage$MessageLevel[ConsoleMessage$MessageLevel.TIP.ordinal()] = 4;
                   try{
                      WVWebChromeClient$a.$SwitchMap$android$webkit$ConsoleMessage$MessageLevel[ConsoleMessage$MessageLevel.DEBUG.ordinal()] = 5;
                   }catch(java.lang.NoSuchFieldError e0){
                   }
                }catch(java.lang.NoSuchFieldError e0){
                }
             }catch(java.lang.NoSuchFieldError e0){
             }
          }catch(java.lang.NoSuchFieldError e0){
          }
       }catch(java.lang.NoSuchFieldError e0){
       }
    }
}
