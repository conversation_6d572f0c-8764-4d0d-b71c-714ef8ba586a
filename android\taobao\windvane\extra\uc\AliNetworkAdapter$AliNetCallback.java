package android.taobao.windvane.extra.uc.AliNetworkAdapter$AliNetCallback;
import anetwork.channel.NetworkCallBack$FinishListener;
import anetwork.channel.NetworkCallBack$ResponseCodeListener;
import anetwork.channel.NetworkCallBack$ProgressListener;
import tb.t2o;
import android.taobao.windvane.extra.uc.AliNetworkAdapter;
import java.lang.Object;
import java.util.Map;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.util.List;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.aka;
import tb.bka;
import java.lang.Throwable;
import android.taobao.windvane.extra.uc.WVUCWebView;
import anetwork.channel.NetworkEvent$FinishEvent;
import tb.vpw;
import tb.wpw;
import tb.wqw;
import tb.trw;
import android.taobao.windvane.extra.uc.interfaces.IRequest;
import tb.gtw;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import android.taobao.windvane.extra.performance2.WVPageTracker;
import anetwork.channel.statist.StatisticData;
import java.lang.Long;
import java.lang.Integer;
import java.lang.Boolean;
import android.taobao.windvane.extra.performance2.WVWPData;
import android.taobao.windvane.extra.uc.AliRequestAdapter;
import tb.j2x;
import anetwork.channel.Request;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import java.lang.StringBuilder;
import tb.v7t;
import tb.y71;
import java.lang.Math;
import anetwork.channel.NetworkEvent$ProgressEvent;
import tb.csw;
import java.lang.System;
import android.taobao.windvane.extra.uc.interfaces.IRequestTiming;
import android.taobao.windvane.extra.uc.UCNetworkDelegate;
import org.json.JSONObject;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import org.json.JSONArray;
import android.taobao.windvane.util.MimeTypeEnum;
import tb.urb;

public class AliNetworkAdapter$AliNetCallback implements NetworkCallBack$FinishListener, NetworkCallBack$ResponseCodeListener, NetworkCallBack$ProgressListener	// class@0001fb from classes.dex
{
    public urb id;
    public EventHandler mEventHandler;
    public IRequest mReq;
    public String mUrl;
    public int size;
    public final AliNetworkAdapter this$0;
    public int total;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800131);
       t2o.a(0x264001d7);
       t2o.a(0x264001da);
       t2o.a(0x264001d9);
    }
    public void AliNetworkAdapter$AliNetCallback(AliNetworkAdapter p0){
       this.this$0 = p0;
       super();
       this.size = 0;
       this.total = 0;
    }
    private void addGreyPageInfo(Map p0){
       List list1;
       int i = 0;
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f54fe1e1", objArray);
          return;
       }else {
          List list = p0.get("x-air-grey");
          CharSequence uCharSequenc = null;
          String str = (list != null && list.size() > 0)? list.get(i): uCharSequenc;
          if ((list1 = p0.get("x-air-env")) != null && list1.size() > 0) {
             uCharSequenc = list1.get(i);
          }
          if (!TextUtils.isEmpty(uCharSequenc) || !TextUtils.isEmpty(str)) {
             bka.c().a(new aka(this.mUrl, uCharSequenc, str));
          }
          return;
       }
    }
    private String getCacheUrlFromWebView(){
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("aa12ed6d", objArray);
       }else if(AliNetworkAdapter.access$000(this.this$0) != null){
          return AliNetworkAdapter.access$000(this.this$0).getCachedUrl();
       }else {
          return null;
       }
    }
    private void monitorOnFinished(NetworkEvent$FinishEvent p0){
       AliNetworkAdapter$AliNetCallback tmReq;
       Map headers;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f74e6fd2", objArray);
          return;
       }else {
          vpw.b();
          if (vpw.commonConfig.H0 != null && (p0 != null && (p0.getHttpCode() < 0 && trw.getWVNetWorkMonitorInterface() != null))) {
             String str = null;
             if ((tmReq = this.mReq) != null && (headers = tmReq.getHeaders()) != null) {
                String str1 = headers.get("Referer");
                str = (TextUtils.isEmpty(str1))? headers.get("referer"): str1;
             }
             if ((tmReq = this.mUrl) == null || !TextUtils.equals(gtw.i(tmReq), gtw.i(AliNetworkAdapter.access$000(this.this$0).getCachedUrl()))) {
                i = 0;
             }
             i1 = i;
             if (vpw.commonConfig.c1 != null) {
                lcn.a(RVLLevel.Info, AliNetworkAdapter.access$400()).k("finish", this.this$0.getCurId()).m(AliNetworkAdapter.access$000(this.this$0).pageTracker.getPageIdentifier()).a("url", this.mUrl).a("dataSize", Long.valueOf(p0.getStatisticData().totalSize)).f();
             }
             wqw wVNetWorkMon = trw.getWVNetWorkMonitorInterface();
             AliNetworkAdapter$AliNetCallback tmUrl = this.mUrl;
             String str2 = (str == null)? "unknown": str;
             wVNetWorkMon.onResponse(tmUrl, str2, p0.getHttpCode(), i1, null);
             this.wpUpload(str, p0.getHttpCode(), this.mUrl, i1);
          }
          return;
       }
    }
    private void monitorOnResponse(int p0,String p1,String p2,boolean p3,Map p4){
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1,p2,new Boolean(p3),p4};
          $ipChange.ipc$dispatch("3becd1a7", objArray);
          return;
       }else if(trw.getWVNetWorkMonitorInterface() != null){
          if (TextUtils.isEmpty(p2)) {
             p2 = "unknown";
          }
          trw.getWVNetWorkMonitorInterface().onResponse(this.mUrl, p2, p0, p3, p4);
          if (p0 >= 400) {
             this.wpUpload(p2, p0, p1, p3);
          }else if(p0 >= 200 && p0 < 300){
             if (p3) {
                AliNetworkAdapter.access$000(this.this$0).wpData.setPageCurrentStatus("responseHtml");
             }else if(p1 != null && p1.endsWith(".js")){
                AliNetworkAdapter.access$000(this.this$0).wpData.setPageCurrentStatus("responseJs");
             }
          }
       }
       return;
    }
    private void monitorOnResponseNew(int p0,String p1,String p2,boolean p3,Map p4,boolean p5,IRequest p6){
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1,p2,new Boolean(p3),p4,new Boolean(p5),p6};
          $ipChange.ipc$dispatch("703defab", objArray);
          return;
       }else {
          this.monitorOnResponse(p0, p1, p2, p3, p4);
          if (p6 instanceof AliRequestAdapter) {
             RVLLevel info = (p5)? RVLLevel.Info: RVLLevel.Warn;
             lcn.a(info, AliNetworkAdapter.access$400()).k("response", p6.getCurId()).m(p6.getPId()).a("url", p1).a("statusCode", Integer.valueOf(p0)).a("header", p4).f();
          }
          return;
       }
    }
    private void monitorWebpResource(String p0){
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("926ab5d5", objArray);
          return;
       }else if(j2x.needSampleWebp()){
          String cachedUrl = (AliNetworkAdapter.access$000(this.this$0) != null)? AliNetworkAdapter.access$000(this.this$0).getCachedUrl(): null;
          if (TextUtils.isEmpty(cachedUrl)) {
             return;
          }else {
             j2x.commitWebpResource(p0, cachedUrl, 3);
          }
       }
       return;
    }
    private void reSendRequest(int p0,String p1){
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("726261f2", objArray);
          return;
       }else {
          AliNetworkAdapter$AliNetCallback tmReq = this.mReq;
          Request aliRequest = tmReq.getAliRequest();
          Request request = tmReq.createRequest(p1, aliRequest.getMethod(), aliRequest.getHeaders());
          AliNetworkAdapter.access$100(this.this$0, request);
          AliNetworkAdapter.access$200(this.this$0, tmReq);
          EventHandler eventHandler = tmReq.getEventHandler();
          v7t.d("AliNetwork", "404 reSendRequest eventId="+tmReq.getEventHandler().hashCode()+", unchangedUrl="+p1+",isSync="+eventHandler.isSynchronous()+",errorUrl="+aliRequest.getUrlString());
          AliNetworkAdapter.access$300(this.this$0, tmReq, request, eventHandler);
          y71.commitFail("reSendRequest", p0, p1, aliRequest.getUrlString());
          return;
       }
    }
    private void wpUpload(String p0,int p1,String p2,boolean p3){
       int i = 0;
       String str = "js Download failed: errorCode=";
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2,new Boolean(p3)};
          $ipChange.ipc$dispatch("50bf9432", objArray);
          return;
       }else if(p3){
          AliNetworkAdapter.access$000(this.this$0).wpData.setHtmlError(p1, p2);
          p1.ucBkpg = i;
          v7t.d("AliNetwork", "onOccurWhitePage "+p2);
          if ((Math.random() - vpw.commonConfig.n0) >= 0) {
             y71.commitWPData(p0, AliNetworkAdapter.access$000(this.this$0).wpData);
          }
       }else if(p2 != null && p2.endsWith(".js")){
          AliNetworkAdapter.access$000(this.this$0).wpData.setJsErrorCode(p1, p2);
          AliNetworkAdapter.access$000(this.this$0).wpData.addProbableReason(str+p1+" url="+this.mUrl);
       }
       return;
    }
    public void onDataReceived(NetworkEvent$ProgressEvent p0,Object p1){
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("e27ec0e9", objArray);
          return;
       }else {
          v7t.i("AliNetwork", "[onDataReceived] event:"+p0+"event.getSize\(\):"+p0.getSize()+", data:"+p0.getBytedata().length+" bytes");
          this.mEventHandler.data(p0.getBytedata(), p0.getSize());
          this.size = this.size + p0.getSize();
          if (this.total == null) {
             this.total = p0.getTotal();
          }
          return;
       }
    }
    public void onFinished(NetworkEvent$FinishEvent p0,Object p1){
       IRequestTiming requestTimin;
       int i = 0;
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("a00910e8", objArray);
          return;
       }else if(AliNetworkAdapter.access$000(this.this$0) != null){
          AliNetworkAdapter.access$000(this.this$0).pageTracker.onResourceFinished(this.mUrl, this.size);
       }
       String str = "AliNetwork";
       v7t.d(str, "onFinished code = "+p0.getHttpCode()+", url = "+this.mUrl+", rev_size = "+this.size+", total_size = "+this.total);
       if (vpw.commonConfig.p3 != null) {
          if (AliNetworkAdapter.access$000(this.this$0) != null) {
             this.monitorOnFinished(p0);
          }
       }else {
          this.monitorOnFinished(p0);
       }
       if (trw.getPerformanceMonitor() != null) {
          trw.getPerformanceMonitor().didResourceFinishLoadAtTime(this.mUrl, System.currentTimeMillis(), p0.getStatisticData().connectionType, 0);
       }
       p1 = this.mReq;
       if ((requestTimin = p1.getRequestTiming()) != null) {
          requestTimin.setTNetRequestStatics(p0.getStatisticData());
       }
       i = p0.getHttpCode();
       UCNetworkDelegate.getInstance().onFinish(i, this.mUrl);
       if (i < 0) {
          v7t.d(str, "error code="+i+",desc="+p0.getDesc()+",url="+this.mUrl);
          this.mEventHandler.error(i, p0.getDesc());
          p1.complete();
          return;
       }else {
          p1.cancelPhase = "enddata";
          this.mEventHandler.endData();
          p1.complete();
          return;
       }
    }
    public boolean onResponseCode(int p0,Map p1,Object p2){
       String key;
       Map headers;
       String str3;
       String str4;
       List list;
       String str5;
       int i3;
       int i4;
       String str7;
       wpw owpw;
       String str8;
       String str9;
       String str10;
       boolean b;
       object oobject = this;
       int i = p0;
       object oobject1 = p1;
       int i1 = 2;
       int i2 = 1;
       String str = "utparamcnt";
       String str1 = "onResponseCode code=";
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,new Integer(i),oobject1,p2};
          return $ipChange.ipc$dispatch("45b007d6", objArray).booleanValue();
       }else if(i != 404 && (i != 400 || (p2 == null || !oobject.mReq instanceof AliRequestAdapter))){
          vpw.b();
          if (vpw.commonConfig.P0 != null) {
             oobject.reSendRequest(i, p2);
             return 0;
          }
       }
       v7t.d("AliNetwork", str1+i+",url="+oobject.mUrl);
       JSONObject jSONObject = new JSONObject();
       String str2 = null;
       if (oobject1 != null) {
          Iterator iterator = p1.entrySet().iterator();
          while (iterator.hasNext()) {
             Map$Entry uEntry = iterator.next();
             if (uEntry.getValue() != null && uEntry.getValue().size() > 0) {
                JSONArray jSONArray = new JSONArray();
                Iterator iterator1 = uEntry.getValue().iterator();
                while (iterator1.hasNext()) {
                   jSONArray.put(iterator1.next());
                   i2 = 1;
                }
                if (uEntry.getKey() != null) {
                   jSONObject.put(uEntry.getKey(), jSONArray);
                }
             }else {
                jSONObject.put(uEntry.getKey(), uEntry.getValue());
             }
             key = uEntry.getKey();
             if ("content-type".equalsIgnoreCase(key)) {
                str2 = key;
             }
             key = 1;
          }
       }
       oobject.addGreyPageInfo(oobject1);
       key = gtw.j(gtw.i(oobject.mUrl));
       if ((headers = oobject.mReq.getHeaders()) != null) {
          str3 = headers.get("Referer");
          if (TextUtils.isEmpty(str3)) {
             str3 = headers.get("referer");
          }
          str4 = str3;
       }else {
          str4 = "";
       }
       if (oobject1 != null) {
          if (str2 == null) {
             str2 = "Content-Type";
          }
          if ((list = oobject1.get(str2)) != null && list.size() > 0) {
             Iterator iterator2 = list.iterator();
             str2 = 0;
             str3 = 0;
             while (iterator2.hasNext()) {
                if ((str5 = iterator2.next()) != null && str5.startsWith(MimeTypeEnum.HTML.getMimeType())) {
                   str2 = 1;
                }
                if (str5 != null && str5.startsWith(MimeTypeEnum.WEBP.getMimeType())) {
                   str3 = 1;
                }
             }
             i3 = str2;
             i4 = str3;
          label_0171 :
             String str6 = "1";
             str5 = (i3)? str6: "0";
             wpw commonConfig = vpw.commonConfig;
             if (commonConfig.c1 == null) {
                AliNetworkAdapter$AliNetCallback mUrl = oobject.mUrl;
                str7 = (commonConfig.L1 != null)? this.getCacheUrlFromWebView(): str4;
                owpw = commonConfig;
                str8 = str5;
                str9 = str6;
                str10 = str4;
                this.monitorOnResponse(p0, mUrl, str7, i3, p1);
             }else {
                owpw = commonConfig;
                str8 = str5;
                str9 = str6;
                str10 = str4;
             }
             iterator2 = 200;
             if (i4 && i == iterator2) {
                oobject.monitorWebpResource(oobject.mUrl);
             }
             if (i >= iterator2 && (i <= 304 || i == 307)) {
                if (i == 302) {
                   if (oobject1 != null) {
                      if ((list = oobject1.get("Location")) == null) {
                         list = oobject1.get("location");
                      }
                      if (list != null) {
                         str2 = list.get(0);
                         if (!TextUtils.isEmpty(str2)) {
                            if (oobject.mUrl.contains("_afc_link=1")) {
                               vpw.b();
                               str3 = (owpw.T != null)? gtw.a(str2, "_afc_link", str9): str2.concat("&_afc_link=1");
                               str5 = gtw.d(oobject.mUrl, str);
                               if (!TextUtils.isEmpty(str5)) {
                                  vpw.b();
                                  str3 = (owpw.T != null)? gtw.a(str3, str, str5): str3.concat("&utparamcnt="+str5);
                               }
                               list.add(0, str3);
                            }
                            str2 = gtw.j(gtw.i(str2));
                         }
                      }else {
                      label_0242 :
                         str2 = "";
                      }
                   }else {
                      goto label_0242 ;
                   }
                   if (!TextUtils.isEmpty(str2)) {
                      if (!str2.equals("//err.tmall.com/error1.html") && !str2.equals("//err.taobao.com/error1.html")) {
                         if (str2.equals("//err.tmall.com/error2.html")) {
                            str5 = str10;
                            y71.commitStatusCode(oobject.mUrl, str5, String.valueOf(500), str8, UCNetworkDelegate.getInstance().getBizCodeByUrl(key));
                         }else {
                         label_027a :
                            str5 = str10;
                         }
                      }else {
                         str5 = str10;
                         y71.commitStatusCode(oobject.mUrl, str5, String.valueOf(404), str8, UCNetworkDelegate.getInstance().getBizCodeByUrl(key));
                      }
                   }else {
                      goto label_027a ;
                   }
                   key = str2;
                }else {
                   str7 = str10;
                   key = "";
                }
                b = true;
             }else {
                str7 = str10;
                y71.commitStatusCode(oobject.mUrl, str7, String.valueOf(p0), str8, UCNetworkDelegate.getInstance().getBizCodeByUrl(key));
                key = "";
                b = false;
             }
             if (owpw.c1 != null) {
                AliNetworkAdapter$AliNetCallback mUrl1 = oobject.mUrl;
                if (owpw.L1 != null) {
                   str5 = this.getCacheUrlFromWebView();
                }
                this.monitorOnResponseNew(p0, mUrl1, str5, i3, p1, b, oobject.mReq);
             }
             if (AliNetworkAdapter.access$000(oobject.this$0) != null) {
                AliNetworkAdapter.access$000(oobject.this$0).pageTracker.onResourceReceivedStatusCode(oobject.mUrl, key, i);
             }
             key = "x-protocol";
             if (oobject1.containsKey(key) && oobject1.get(key).size()) {
                key = oobject1.get(key).get(0);
                if (!key.equals("http") && !key.equals("https")) {
                   oobject.mEventHandler.status(2, 0, i, "");
                }else {
                   oobject.mEventHandler.status(0, 0, i, "");
                }
             }else if(oobject1.containsKey(":status")){
                oobject.mEventHandler.status(2, 0, i, "");
             }else {
                oobject.mEventHandler.status(0, 0, i, "");
             }
             oobject.mEventHandler.headers(oobject1);
             return 0;
          }
       }
       i4 = 0;
       i3 = 0;
       goto label_0171 ;
    }
    public void setEventHandler(EventHandler p0){
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("31cc001", objArray);
          return;
       }else {
          this.mEventHandler = p0;
          return;
       }
    }
    public void setId(urb p0){
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("799f6d32", objArray);
          return;
       }else {
          this.id = p0;
          return;
       }
    }
    public void setRequest(IRequest p0){
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4e61966", objArray);
          return;
       }else {
          this.mReq = p0;
          return;
       }
    }
    public void setURL(String p0){
       IpChange $ipChange = AliNetworkAdapter$AliNetCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("1b79a47e", objArray);
          return;
       }else {
          this.mUrl = p0;
          return;
       }
    }
}
