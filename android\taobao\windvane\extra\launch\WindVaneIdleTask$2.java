package android.taobao.windvane.extra.launch.WindVaneIdleTask$2;
import java.lang.Runnable;
import android.taobao.windvane.extra.launch.WindVaneIdleTask;
import android.app.Application;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class WindVaneIdleTask$2 implements Runnable	// class@0001bc from classes.dex
{
    public final WindVaneIdleTask this$0;
    public final Application val$application;
    public static IpChange $ipChange;

    public void WindVaneIdleTask$2(WindVaneIdleTask p0,Application p1){
       this.this$0 = p0;
       this.val$application = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = WindVaneIdleTask$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          WindVaneIdleTask.access$100(this.this$0, this.val$application);
          return;
       }
    }
}
