package android.taobao.windvane.extra.uc.UCCoreStartup;
import tb.t2o;
import java.util.concurrent.atomic.AtomicBoolean;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.thread.WVThreadPool;
import tb.v7t;
import android.content.Context;
import android.taobao.windvane.extra.core.WVCore;
import android.content.pm.ApplicationInfo;
import java.io.File;
import com.uc.webview.export.extension.U4Engine;
import android.taobao.windvane.extra.uc.remotefetch.WVUCRemoteFetcher;
import java.lang.CharSequence;
import android.text.TextUtils;
import com.uc.webview.base.io.PathUtils;
import com.uc.webview.export.extension.U4Engine$Initializer;
import java.lang.StringBuilder;
import java.lang.Throwable;
import com.uc.webview.export.extension.U4Engine$Extractor;
import java.lang.System;
import tb.yaa;
import android.app.Application;
import tb.vpw;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Long;
import java.lang.ClassLoader;

public class UCCoreStartup	// class@00021f from classes.dex
{
    public static IpChange $ipChange;
    private static final boolean ENABLE_ANDROID_LOG;
    private static final boolean ENABLE_PREPROCESS;
    private static final String TAG;
    private static final AtomicBoolean sPreprocessAtomic;
    private static UCCoreStartup sUCCoreStartup;

    static {
       t2o.a(0x3d800154);
       UCCoreStartup.sPreprocessAtomic = new AtomicBoolean(false);
    }
    public void UCCoreStartup(){
       super();
    }
    public static UCCoreStartup getInstance(){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("349f76e1", objArray);
       }else if(UCCoreStartup.sUCCoreStartup == null){
          WVThreadPool wVThreadPool = WVThreadPool.class;
          _monitor_enter(wVThreadPool);
          if (UCCoreStartup.sUCCoreStartup == null) {
             UCCoreStartup.printAndroidLogE("UCCoreStartup instance enable preprocess true");
             UCCoreStartup.sUCCoreStartup = new UCCoreStartup();
          }
          _monitor_exit(wVThreadPool);
       }
       return UCCoreStartup.sUCCoreStartup;
    }
    private static void printAndroidLogE(String p0){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("205a5f4", objArray);
          return;
       }else {
          v7t.d("UCCoreStartup", p0);
          return;
       }
    }
    public static String ucCore7ZFilePath(Context p0){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("951686d0", objArray);
       }else if(WVCore.getInstance().isUCInner()){
          return U4Engine.getInnerCompressedFilePath(p0.getApplicationInfo().nativeLibraryDir).getAbsolutePath();
       }else {
          return WVUCRemoteFetcher.fetchUCRemoteLocal();
       }
    }
    public static String ucCore7ZFilePath(String p0){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return U4Engine.getInnerCompressedFilePath(p0).getAbsolutePath();
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("61ce6a1e", objArray);
    }
    public static String ucCoreRootDirPath(Context p0){
       File runningDir;
       File extractDir;
       int i = 0;
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("47207de2", objArray);
       }else if(WVCore.getInstance().isUCInner()){
          return U4Engine.getExtractDir(p0, new File(UCCoreStartup.ucCore7ZFilePath(p0))).getAbsolutePath();
       }else if((runningDir = U4Engine.getRunningDir(p0, i)) != null){
          return runningDir.getAbsolutePath();
       }else {
          String str = WVUCRemoteFetcher.fetchUCRemoteLocal();
          if (!TextUtils.isEmpty(str) && (extractDir = U4Engine.getExtractDir(p0, new File(str))) != null) {
             return extractDir.getAbsolutePath();
          }
          return "";
       }
    }
    public static String ucCoreRootDirPath(Context p0,String p1){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return U4Engine.getExtractDir(p0, new File(UCCoreStartup.ucCore7ZFilePath(p1))).getAbsolutePath();
       }
       Object[] objArray = new Object[]{p0,p1};
       return $ipChange.ipc$dispatch("460bd4d8", objArray);
    }
    public static String ucCoreSoDirPath(Context p0){
       File runningDir;
       File extractDir;
       int i = 0;
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("d3b11968", objArray);
       }else if(WVCore.getInstance().isUCInner()){
          return PathUtils.getDirCoreLib(U4Engine.getExtractDir(p0, new File(UCCoreStartup.ucCore7ZFilePath(p0)))).getAbsolutePath();
       }else if((runningDir = U4Engine.getRunningDir(p0, i)) != null){
          return PathUtils.getDirCoreLib(runningDir).getAbsolutePath();
       }else {
          String str = WVUCRemoteFetcher.fetchUCRemoteLocal();
          if (!TextUtils.isEmpty(str) && (extractDir = U4Engine.getExtractDir(p0, new File(str))) != null) {
             return PathUtils.getDirCoreLib(extractDir).getAbsolutePath();
          }
          return "";
       }
    }
    public static String ucCoreSoDirPath(Context p0,String p1){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return PathUtils.getDirCoreLib(U4Engine.getExtractDir(p0, new File(UCCoreStartup.ucCore7ZFilePath(p1)))).getAbsolutePath();
       }
       Object[] objArray = new Object[]{p0,p1};
       return $ipChange.ipc$dispatch("9efadade", objArray);
    }
    public void initPreprocess(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          $ipChange.ipc$dispatch("65d14223", objArray);
          return;
       }else if(UCCoreStartup.sPreprocessAtomic.compareAndSet(i, i1)){
          UCCoreStartup.printAndroidLogE("initPreprocess");
          U4Engine.createInitializer();
       }
       return;
    }
    public void preDecompress(Context p0,String p1){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("48daea05", objArray);
          return;
       }else {
          UCCoreStartup.printAndroidLogE("preDecompress "+p0+", "+p1);
          U4Engine.createExtractor().setContext(p0.getApplicationContext()).setCompressedFile(new File(p1)).start();
          return;
       }
    }
    public void preInitUCCore(Context p0,String p1){
       String str = "UCCoreStartup";
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("1f0f454b", objArray);
          return;
       }else {
          UCCoreStartup.printAndroidLogE("preInitUCCore "+p0+", "+p1);
          long l = System.currentTimeMillis();
          v7t.i(str, "trying to init uc core");
          if (yaa.n == null && p0 instanceof Application) {
             yaa.n = p0;
          }
          vpw.b().d();
          IpChange $ipChange1 = WVUCWebView.$ipChange;
          UCCoreStartup.printAndroidLogE("initU4Core elapse: "+Long.toString((System.currentTimeMillis() - l)));
          return;
       }
    }
    public void preloadClass(ClassLoader p0){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("441d8bb4", objArray);
       }
       return;
    }
    public void preloadIcu(Context p0,String p1){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("ebd2c299", objArray);
       }
       return;
    }
    public void preloadIo(Context p0,String p1){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("18dc23bc", objArray);
       }
       return;
    }
    public void preloadPak(Context p0,String p1){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4678c6ba", objArray);
       }
       return;
    }
    public void preloadSo(Context p0,String p1){
       IpChange $ipChange = UCCoreStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("143c27c6", objArray);
       }
       return;
    }
}
