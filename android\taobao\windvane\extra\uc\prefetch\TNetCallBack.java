package android.taobao.windvane.extra.uc.prefetch.TNetCallBack;
import anetwork.channel.NetworkCallBack$FinishListener;
import anetwork.channel.NetworkCallBack$ResponseCodeListener;
import anetwork.channel.NetworkCallBack$ProgressListener;

public interface abstract T<PERSON><PERSON>allBack implements NetworkCallBack$FinishListener, NetworkCallBack$ResponseCodeListener, NetworkCallBack$ProgressListener	// class@000291 from classes.dex
{
	/* No methods */
}