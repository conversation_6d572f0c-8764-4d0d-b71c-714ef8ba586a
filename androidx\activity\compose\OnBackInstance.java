package androidx.activity.compose.OnBackInstance;
import tb.uu4;
import tb.u1a;
import androidx.activity.OnBackPressedCallback;
import java.lang.Object;
import kotlinx.coroutines.channels.BufferOverflow;
import tb.g1a;
import kotlinx.coroutines.channels.c;
import kotlinx.coroutines.channels.d;
import androidx.activity.compose.OnBackInstance$job$1;
import tb.ar4;
import kotlin.coroutines.d;
import kotlinx.coroutines.CoroutineStart;
import kotlinx.coroutines.m;
import tb.mn2;
import java.util.concurrent.CancellationException;
import java.lang.String;
import kotlinx.coroutines.channels.ReceiveChannel;
import kotlinx.coroutines.m$a;
import kotlinx.coroutines.channels.i;
import java.lang.Throwable;
import kotlinx.coroutines.channels.i$a;
import androidx.activity.BackEventCompat;

public final class OnBackInstance	// class@00048c from classes.dex
{
    private final c channel;
    private boolean isPredictiveBack;
    private final m job;

    public void OnBackInstance(uu4 p0,boolean p1,u1a p2,OnBackPressedCallback p3){
       super();
       this.isPredictiveBack = p1;
       this.channel = d.b(-2, BufferOverflow.SUSPEND, null, 4, null);
       this.job = mn2.b(p0, null, null, new OnBackInstance$job$1(p3, p2, this, null), 3, null);
    }
    public final void cancel(){
       this.channel.a(new CancellationException("onBack cancelled"));
       m$a.b(this.job, null, 1, null);
    }
    public final boolean close(){
       return i$a.a(this.channel, null, 1, null);
    }
    public final c getChannel(){
       return this.channel;
    }
    public final m getJob(){
       return this.job;
    }
    public final boolean isPredictiveBack(){
       return this.isPredictiveBack;
    }
    public final Object send-JP2dKIU(BackEventCompat p0){
       return this.channel.m(p0);
    }
    public final void setPredictiveBack(boolean p0){
       this.isPredictiveBack = p0;
    }
}
