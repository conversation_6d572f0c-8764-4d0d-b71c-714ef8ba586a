package android.taobao.windvane.extra.uc.WVUCWebView$1$4;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.WVUCWebView$1;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.uc.WVUCWebView;
import android.content.Context;
import android.os.Handler;
import tb.voe;

public class WVUCWebView$1$4 implements Runnable	// class@000250 from classes.dex
{
    public final WVUCWebView$1 this$1;
    public static IpChange $ipChange;

    public void WVUCWebView$1$4(WVUCWebView$1 p0){
       this.this$1 = p0;
       super();
    }
    public void run(){
       WVUCWebView context;
       IpChange $ipChange = WVUCWebView$1$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if((context = this.this$1.this$0.context) == null){
          return;
       }else {
          voe.g(context.getApplicationContext(), WVUCWebView.access$300(this.this$1.this$0), this.this$1.this$0.mHandler);
          return;
       }
    }
}
