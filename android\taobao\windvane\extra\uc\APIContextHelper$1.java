package android.taobao.windvane.extra.uc.APIContextHelper$1;
import java.lang.Runnable;
import java.lang.String;
import android.net.Uri;
import java.util.concurrent.CountDownLatch;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import android.taobao.windvane.extra.uc.APIContextHelper;

public final class APIContextHelper$1 implements Runnable	// class@0001f7 from classes.dex
{
    public final CountDownLatch val$countDownLatch;
    public final String val$encodedAPIInfo;
    public final String[] val$result;
    public final Uri val$uri;
    public static IpChange $ipChange;

    public void APIContextHelper$1(String[] p0,Uri p1,String p2,CountDownLatch p3){
       this.val$result = p0;
       this.val$uri = p1;
       this.val$encodedAPIInfo = p2;
       this.val$countDownLatch = p3;
       super();
    }
    public void run(){
       int i = 0;
       IpChange $ipChange = APIContextHelper$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          this.val$result[i] = APIContextHelper.access$000(this.val$uri, this.val$encodedAPIInfo);
          this.val$countDownLatch.countDown();
          return;
       }
    }
}
