package androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$Companion;
import tb.a07;
import android.content.Context;
import android.content.pm.ResolveInfo;
import androidx.activity.result.PickVisualMediaRequest;
import android.content.Intent;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VisualMediaType;
import android.content.pm.ActivityInfo;
import android.content.pm.ApplicationInfo;
import java.lang.IllegalStateException;
import androidx.activity.result.contract.ActivityResultContract$SynchronousResult;
import android.net.Uri;
import androidx.activity.result.contract.ActivityResultContracts$GetMultipleContents;
import java.util.List;
import androidx.activity.result.contract.ActivityResultContracts$GetMultipleContents$Companion;
import tb.i04;

public class ActivityResultContracts$PickVisualMedia extends ActivityResultContract	// class@0004d7 from classes.dex
{
    public static final String ACTION_SYSTEM_FALLBACK_PICK_IMAGES = "androidx.activity.result.contract.action.PICK_IMAGES";
    public static final ActivityResultContracts$PickVisualMedia$Companion Companion;
    public static final String EXTRA_SYSTEM_FALLBACK_PICK_IMAGES_MAX;
    public static final String GMS_ACTION_PICK_IMAGES;
    public static final String GMS_EXTRA_PICK_IMAGES_MAX;

    static {
       ActivityResultContracts$PickVisualMedia.Companion = new ActivityResultContracts$PickVisualMedia$Companion(null);
    }
    public void ActivityResultContracts$PickVisualMedia(){
       super();
    }
    public static final ResolveInfo getGmsPicker$activity_release(Context p0){
       return ActivityResultContracts$PickVisualMedia.Companion.getGmsPicker$activity_release(p0);
    }
    public static final ResolveInfo getSystemFallbackPicker$activity_release(Context p0){
       return ActivityResultContracts$PickVisualMedia.Companion.getSystemFallbackPicker$activity_release(p0);
    }
    public static final boolean isGmsPickerAvailable$activity_release(Context p0){
       return ActivityResultContracts$PickVisualMedia.Companion.isGmsPickerAvailable$activity_release(p0);
    }
    public static final boolean isPhotoPickerAvailable(){
       return ActivityResultContracts$PickVisualMedia.Companion.isPhotoPickerAvailable();
    }
    public static final boolean isPhotoPickerAvailable(Context p0){
       return ActivityResultContracts$PickVisualMedia.Companion.isPhotoPickerAvailable(p0);
    }
    public static final boolean isSystemFallbackPickerAvailable$activity_release(Context p0){
       return ActivityResultContracts$PickVisualMedia.Companion.isSystemFallbackPickerAvailable$activity_release(p0);
    }
    public static final boolean isSystemPickerAvailable$activity_release(){
       return ActivityResultContracts$PickVisualMedia.Companion.isSystemPickerAvailable$activity_release();
    }
    public Intent createIntent(Context p0,PickVisualMediaRequest p1){
       Intent intent;
       ResolveInfo systemFallba;
       Intent intent1;
       ckf.g(p0, "context");
       ckf.g(p1, "input");
       ActivityResultContracts$PickVisualMedia$Companion companion = ActivityResultContracts$PickVisualMedia.Companion;
       if (companion.isSystemPickerAvailable$activity_release()) {
          intent = new Intent("android.provider.action.PICK_IMAGES");
          intent.setType(companion.getVisualMimeType$activity_release(p1.getMediaType()));
       }else {
          String str = "Required value was null.";
          if (companion.isSystemFallbackPickerAvailable$activity_release(p0)) {
             if ((systemFallba = companion.getSystemFallbackPicker$activity_release(p0)) != null) {
                systemFallba = systemFallba.activityInfo;
                intent1 = new Intent("androidx.activity.result.contract.action.PICK_IMAGES");
                intent1.setClassName(systemFallba.applicationInfo.packageName, systemFallba.name);
                intent1.setType(companion.getVisualMimeType$activity_release(p1.getMediaType()));
             }else {
                throw new IllegalStateException(str);
             }
          }else if(companion.isGmsPickerAvailable$activity_release(p0)){
             if ((systemFallba = companion.getGmsPicker$activity_release(p0)) != null) {
                systemFallba = systemFallba.activityInfo;
                intent1 = new Intent("com.google.android.gms.provider.action.PICK_IMAGES");
                intent1.setClassName(systemFallba.applicationInfo.packageName, systemFallba.name);
                intent1.setType(companion.getVisualMimeType$activity_release(p1.getMediaType()));
             }else {
                throw new IllegalStateException(str);
             }
          }else {
             intent = new Intent("android.intent.action.OPEN_DOCUMENT");
             intent.setType(companion.getVisualMimeType$activity_release(p1.getMediaType()));
             if (intent.getType() == null) {
                intent.setType("*/*");
                String[] stringArray = new String[]{"image/*","video/*"};
                intent.putExtra("android.intent.extra.MIME_TYPES", stringArray);
             }
          }
          intent = intent1;
       }
       return intent;
    }
    public Intent createIntent(Context p0,Object p1){
       return this.createIntent(p0, p1);
    }
    public final ActivityResultContract$SynchronousResult getSynchronousResult(Context p0,PickVisualMediaRequest p1){
       ckf.g(p0, "context");
       ckf.g(p1, "input");
       return null;
    }
    public ActivityResultContract$SynchronousResult getSynchronousResult(Context p0,Object p1){
       return this.getSynchronousResult(p0, p1);
    }
    public final Uri parseResult(int p0,Intent p1){
       Uri data;
       Uri uri = null;
       if (p0 != -1) {
          Uri uri1 = uri;
       }
       if (p1 != null) {
          if ((data = p1.getData()) == null) {
             data = i04.c0(ActivityResultContracts$GetMultipleContents.Companion.getClipDataUris$activity_release(p1));
          }
          uri = data;
       }
       return uri;
    }
    public Object parseResult(int p0,Intent p1){
       return this.parseResult(p0, p1);
    }
}
