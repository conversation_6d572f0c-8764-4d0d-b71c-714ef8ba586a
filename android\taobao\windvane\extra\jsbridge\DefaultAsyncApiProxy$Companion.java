package android.taobao.windvane.extra.jsbridge.DefaultAsyncApiProxy$Companion;
import tb.t2o;
import java.lang.Object;
import tb.a07;

public final class DefaultAsyncApiProxy$Companion	// class@000197 from classes.dex
{

    static {
       t2o.a(0x3d8000cd);
    }
    private void DefaultAsyncApiProxy$Companion(){
       super();
    }
    public void DefaultAsyncApiProxy$Companion(a07 p0){
       super();
    }
}
