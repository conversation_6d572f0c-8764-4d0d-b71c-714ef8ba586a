package android.taobao.yuzhuang.ManufacturerProcess$b$a;
import java.io.FilenameFilter;
import android.taobao.yuzhuang.ManufacturerProcess$b;
import java.lang.Object;
import java.io.File;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.CharSequence;

public class ManufacturerProcess$b$a implements FilenameFilter	// class@00031d from classes.dex
{
    public static IpChange $ipChange;

    public void ManufacturerProcess$b$a(ManufacturerProcess$b p0){
       super();
    }
    public boolean accept(File p0,String p1){
       int i = 1;
       IpChange $ipChange = ManufacturerProcess$b$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("6696dd14", objArray).booleanValue();
       }else if(p1.toLowerCase().contains("taobao") && p1.endsWith(".apk")){
          i = false;
       }
       return i;
    }
}
