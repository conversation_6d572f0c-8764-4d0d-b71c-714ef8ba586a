package android.taobao.windvane.webview.WVWebView$b$b;
import java.lang.Runnable;
import android.taobao.windvane.webview.WVWebView$b;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.webview.WVWebView;
import android.content.Context;
import android.os.Handler;
import tb.voe;

public class WVWebView$b$b implements Runnable	// class@000312 from classes.dex
{
    public final WVWebView$b a;
    public static IpChange $ipChange;

    public void WVWebView$b$b(WVWebView$b p0){
       this.a = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = WVWebView$b$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          voe.g(this.a.a.context.getApplicationContext(), WVWebView.access$100(this.a.a), this.a.a.mHandler);
          return;
       }
    }
}
