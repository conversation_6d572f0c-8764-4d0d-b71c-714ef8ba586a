package android.taobao.windvane.extra.uc.UCSetupService$2;
import com.uc.webview.export.extension.IStatsHandler;
import android.taobao.windvane.extra.uc.WVUCWebView$WVValueCallback;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;

public final class UCSetupService$2 extends IStatsHandler	// class@000224 from classes.dex
{
    private final WVUCWebView$WVValueCallback mValueCallback;
    public static IpChange $ipChange;

    public void UCSetupService$2(){
       super();
       this.mValueCallback = new WVUCWebView$WVValueCallback();
    }
    public static Object ipc$super(UCSetupService$2 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/UCSetupService$2");
    }
    public boolean stat(String p0){
       IpChange $ipChange = UCSetupService$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ac6f09e9", objArray).booleanValue();
       }else {
          this.mValueCallback.onReceiveValue(p0);
          return 1;
       }
    }
}
