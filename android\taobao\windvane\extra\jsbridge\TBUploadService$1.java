package android.taobao.windvane.extra.jsbridge.TBUploadService$1;
import java.lang.Runnable;
import android.taobao.windvane.extra.jsbridge.TBUploadService;
import android.taobao.windvane.jsbridge.api.WVCamera$g;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class TBUploadService$1 implements Runnable	// class@00019b from classes.dex
{
    public final TBUploadService this$0;
    public final WVCamera$g val$params;
    public static IpChange $ipChange;

    public void TBUploadService$1(TBUploadService p0,WVCamera$g p1){
       this.this$0 = p0;
       this.val$params = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = TBUploadService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          TBUploadService.access$000(this.this$0, this.val$params);
          return;
       }
    }
}
