package android.taobao.windvane.extra.WVSchemeProcessor;
import tb.qsw;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;
import anet.channel.strategy.IStrategyInstance;
import anet.channel.strategy.StrategyCenter;
import java.lang.StringBuilder;
import tb.v7t;

public class WVSchemeProcessor implements qsw	// class@000189 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000be);
       t2o.a(0x3d8002eb);
    }
    public void WVSchemeProcessor(){
       super();
    }
    public String dealUrlScheme(String p0){
       String str = "WVSchemeProcesor deal url, origin_url=[";
       IpChange $ipChange = WVSchemeProcessor.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("849ca447", objArray);
       }else if(!TextUtils.isEmpty(p0) && (!p0.startsWith("javascript:") && !p0.equals("about:blank"))){
          String formalizeUrl = StrategyCenter.getInstance().getFormalizeUrl(p0);
          v7t.d("Processor", str+p0+"], new_url=["+formalizeUrl+"]");
          if (TextUtils.isEmpty(formalizeUrl)) {
             return p0;
          }
          return formalizeUrl;
       }else {
          return p0;
       }
    }
}
