package android.taobao.windvane.extra.jsbridge.TBUploadService$4;
import mtopsdk.mtop.upload.FileUploadBaseListener;
import android.taobao.windvane.extra.jsbridge.TBUploadService;
import tb.nsw;
import android.taobao.windvane.jsbridge.api.WVCamera$g;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import tb.v7t;
import java.lang.StringBuilder;
import java.lang.Integer;
import android.os.Handler;
import android.os.Message;
import org.json.JSONArray;
import mtopsdk.mtop.upload.domain.UploadFileInfo;
import android.graphics.Bitmap;
import tb.voe;
import tb.itw;

public class TBUploadService$4 implements FileUploadBaseListener	// class@00019e from classes.dex
{
    public final TBUploadService this$0;
    public final WVCamera$g val$params;
    public final nsw val$result;
    public static IpChange $ipChange;

    public void TBUploadService$4(TBUploadService p0,nsw p1,WVCamera$g p2){
       this.this$0 = p0;
       this.val$result = p1;
       this.val$params = p2;
       super();
    }
    public void onError(String p0,String p1){
       IpChange $ipChange = TBUploadService$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("97d08c84", objArray);
          return;
       }else if(v7t.h()){
          v7t.n("TBUploadService", "mtop upload file error. code: "+p0+";msg: "+p1);
       }
       this.val$result.b("errorCode", p0);
       this.val$result.b("errorMsg", p1);
       this.val$result.b("localPath", this.val$params.a);
       this.val$result.b("tempFilePath", this.val$params.r);
       this.val$result.a("selectSize", Integer.valueOf(this.val$params.s));
       this.val$result.b("identifier", this.val$params.h);
       Message.obtain(TBUploadService.access$100(this.this$0), 2003, this.val$result).sendToTarget();
       return;
    }
    public void onError(String p0,String p1,String p2){
       IpChange $ipChange = TBUploadService$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("ffe51d4e", objArray);
          return;
       }else if(v7t.h()){
          v7t.n("TBUploadService", "mtop upload file error. code: "+p1+";msg: "+p2+";type: "+p0);
       }
       this.val$result.b("errorType", p0);
       this.val$result.b("errorCode", p1);
       this.val$result.b("errorMsg", p2);
       this.val$result.b("localPath", this.val$params.a);
       this.val$result.b("tempFilePath", this.val$params.r);
       this.val$result.a("selectSize", Integer.valueOf(this.val$params.s));
       this.val$result.b("identifier", this.val$params.h);
       Message.obtain(TBUploadService.access$100(this.this$0), 2003, this.val$result).sendToTarget();
       return;
    }
    public void onFinish(String p0){
       int i1;
       int i = 1;
       IpChange $ipChange = TBUploadService$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("99807463", objArray);
          return;
       }else {
          this.val$result.k();
          this.val$result.b("url", this.val$params.b);
          this.val$result.b("localPath", this.val$params.a);
          this.val$result.b("resourceURL", p0);
          if (i1 = p0.lastIndexOf("/") + i) {
             this.val$result.b("tfsKey", p0.substring(i1));
          }
          TBUploadService$4 tval$params = this.val$params;
          if (tval$params.l != null) {
             this.val$result.c("images", tval$params.o);
          }
          this.val$result.b("tempFilePath", this.val$params.r);
          this.val$result.a("selectSize", Integer.valueOf(this.val$params.s));
          this.val$result.b("identifier", this.val$params.h);
          Message.obtain(TBUploadService.access$100(this.this$0), 2002, this.val$result).sendToTarget();
          return;
       }
    }
    public void onFinish(UploadFileInfo p0,String p1){
       Bitmap uBitmap;
       int i1;
       int i = 1;
       IpChange $ipChange = TBUploadService$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("57d514e0", objArray);
          return;
       }else {
          this.val$result.k();
          this.val$result.b("url", this.val$params.b);
          this.val$result.b("localPath", this.val$params.a);
          this.val$result.b("resourceURL", p1);
          this.val$result.b("tempFilePath", this.val$params.r);
          this.val$result.a("selectSize", Integer.valueOf(this.val$params.s));
          this.val$result.b("identifier", this.val$params.h);
          TBUploadService$4 tval$params = this.val$params;
          if (tval$params.p != null && (uBitmap = voe.c(tval$params.a, 1024)) != null) {
             this.val$result.b("base64Data", itw.a(uBitmap));
          }
          if (i1 = p1.lastIndexOf("/") + i) {
             this.val$result.b("tfsKey", p1.substring(i1));
          }
          tval$params = this.val$params;
          if (tval$params.l != null) {
             this.val$result.c("images", tval$params.o);
          }
          Message.obtain(TBUploadService.access$100(this.this$0), 2002, this.val$result).sendToTarget();
          return;
       }
    }
    public void onProgress(int p0){
       IpChange $ipChange = TBUploadService$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("5ec8f5b0", objArray);
       }
       return;
    }
    public void onStart(){
       IpChange $ipChange = TBUploadService$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("7f2d84ca", objArray);
          return;
       }else {
          this.val$result.j("");
          Message.obtain(TBUploadService.access$100(this.this$0), 2001, this.val$result).sendToTarget();
          return;
       }
    }
}
