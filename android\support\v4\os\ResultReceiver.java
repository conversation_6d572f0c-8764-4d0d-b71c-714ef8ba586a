package android.support.v4.os.ResultReceiver;
import android.os.Parcelable;
import android.support.v4.os.ResultReceiver$1;
import android.os.Handler;
import java.lang.Object;
import android.os.Parcel;
import android.os.IBinder;
import android.support.v4.os.IResultReceiver;
import android.support.v4.os.IResultReceiver$Stub;
import android.os.Bundle;
import android.support.v4.os.ResultReceiver$MyRunnable;
import java.lang.Runnable;
import android.support.v4.os.ResultReceiver$MyResultReceiver;
import android.os.IInterface;

public class ResultReceiver implements Parcelable	// class@00013b from classes.dex
{
    final Handler mHandler;
    final boolean mLocal;
    IResultReceiver mReceiver;
    public static final Parcelable$Creator CREATOR;

    static {
       ResultReceiver.CREATOR = new ResultReceiver$1();
    }
    public void ResultReceiver(Handler p0){
       super();
       this.mLocal = true;
       this.mHandler = p0;
    }
    public void ResultReceiver(Parcel p0){
       super();
       this.mLocal = false;
       this.mHandler = null;
       this.mReceiver = IResultReceiver$Stub.asInterface(p0.readStrongBinder());
    }
    public int describeContents(){
       return 0;
    }
    public void onReceiveResult(int p0,Bundle p1){
    }
    public void send(int p0,Bundle p1){
       ResultReceiver tmHandler;
       if (this.mLocal != null) {
          if ((tmHandler = this.mHandler) != null) {
             tmHandler.post(new ResultReceiver$MyRunnable(this, p0, p1));
          }else {
             this.onReceiveResult(p0, p1);
          }
          return;
       }else if((tmHandler = this.mReceiver) != null){
          tmHandler.send(p0, p1);
       }
       return;
    }
    public void writeToParcel(Parcel p0,int p1){
       _monitor_enter(this);
       if (this.mReceiver == null) {
          this.mReceiver = new ResultReceiver$MyResultReceiver(this);
       }
       p0.writeStrongBinder(this.mReceiver.asBinder());
       _monitor_exit(this);
       return;
    }
}
