package android.taobao.windvane.export.prerender.PrerenderManager$acquirePrerenderWebView$1;
import java.lang.Runnable;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.export.prerender.TMSPrerenderService;
import android.taobao.windvane.export.prerender.PrerenderManager$acquirePrerenderWebView$1$1;
import tb.g1a;

public final class PrerenderManager$acquirePrerenderWebView$1 implements Runnable	// class@000176 from classes.dex
{
    public static IpChange $ipChange;
    public static final PrerenderManager$acquirePrerenderWebView$1 INSTANCE;

    static {
       PrerenderManager$acquirePrerenderWebView$1.INSTANCE = new PrerenderManager$acquirePrerenderWebView$1();
    }
    public void PrerenderManager$acquirePrerenderWebView$1(){
       super();
    }
    public final void run(){
       IpChange $ipChange = PrerenderManager$acquirePrerenderWebView$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          TMSPrerenderService.INSTANCE.f(PrerenderManager$acquirePrerenderWebView$1$1.INSTANCE);
          return;
       }
    }
}
