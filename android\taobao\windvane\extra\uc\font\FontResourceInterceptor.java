package android.taobao.windvane.extra.uc.font.FontResourceInterceptor;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import java.util.List;
import java.util.Arrays;
import com.uc.webview.export.WebView;
import com.uc.webview.export.WebResourceResponse;
import com.android.alibaba.ip.runtime.IpChange;
import tb.vpw;
import tb.wpw;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.net.Uri;
import android.content.Context;
import android.view.View;
import android.content.res.Resources;
import android.content.res.AssetManager;
import java.lang.StringBuilder;
import java.io.InputStream;
import com.taobao.codetrack.sdk.assets.AssetsDelegate;
import java.util.HashMap;
import java.util.Map;
import java.lang.Throwable;
import tb.v7t;

public class FontResourceInterceptor	// class@00026f from classes.dex
{
    public static IpChange $ipChange;
    private static final List FONT_SUFFIX;
    private static final String TAG;

    static {
       t2o.a(0x3d8001a4);
       String[] stringArray = new String[]{"ttf","otf","woff"};
       FontResourceInterceptor.FONT_SUFFIX = Arrays.asList(stringArray);
    }
    public void FontResourceInterceptor(){
       super();
    }
    public static WebResourceResponse interceptFontResources(WebView p0,String p1){
       int i1;
       Context context;
       InputStream inputStream;
       int i = 1;
       String str = "font/";
       String str1 = "iconfonts/";
       IpChange $ipChange = FontResourceInterceptor.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("b98c1e89", objArray);
       }else if(vpw.commonConfig.y1 == null){
          return null;
       }else if(TextUtils.isEmpty(p1)){
          return null;
       }else if(!p1.startsWith("font")){
          return null;
       }else {
          String str2 = "";
          if ((i1 = p1.lastIndexOf(".")) > 0 && i1 < (p1.length() - i)) {
             str2 = p1.substring((i1 + i));
          }
          if (!FontResourceInterceptor.FONT_SUFFIX.contains(str2)) {
             return null;
          }else {
             Uri uri = Uri.parse(p1);
             if (!uri.isHierarchical()) {
                return null;
             }else {
                uri = uri.getHost();
                if (TextUtils.isEmpty(uri)) {
                   return null;
                }else if((context = p0.getContext()) == null){
                   return null;
                }else if((inputStream = AssetsDelegate.proxy_open(context.getResources().getAssets(), str1+uri)) != null){
                   WebResourceResponse webResourceR = new WebResourceResponse(str+str2, "UTF-8", inputStream);
                   HashMap hashMap = new HashMap();
                   hashMap.put("Access-Control-Allow-Origin", "*");
                   hashMap.put("Cache-Control", "max-age=2592000,s-maxage=86400");
                   webResourceR.setResponseHeaders(hashMap);
                   return webResourceR;
                }else {
                   return null;
                }
             }
          }
       }
    }
}
