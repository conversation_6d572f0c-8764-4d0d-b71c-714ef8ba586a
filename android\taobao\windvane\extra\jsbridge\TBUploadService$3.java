package android.taobao.windvane.extra.jsbridge.TBUploadService$3;
import tb.mzd;
import android.taobao.windvane.extra.jsbridge.TBUploadService;
import tb.nsw;
import android.taobao.windvane.jsbridge.api.WVCamera$g;
import java.lang.Object;
import tb.z6e;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ndt;
import tb.v7t;
import java.lang.Integer;
import android.os.Handler;
import android.os.Message;
import java.lang.StringBuilder;
import tb.ozd;
import android.graphics.Bitmap;
import tb.voe;
import tb.itw;
import org.json.JSONArray;

public class TBUploadService$3 implements mzd	// class@00019d from classes.dex
{
    public final TBUploadService this$0;
    public final WVCamera$g val$params;
    public final nsw val$result;
    public static IpChange $ipChange;

    public void TBUploadService$3(TBUploadService p0,nsw p1,WVCamera$g p2){
       this.this$0 = p0;
       this.val$result = p1;
       this.val$params = p2;
       super();
    }
    public void onCancel(z6e p0){
       IpChange $ipChange = TBUploadService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("c7d021ed", objArray);
       }
       return;
    }
    public void onFailure(z6e p0,ndt p1){
       IpChange $ipChange = TBUploadService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("7789334b", objArray);
          return;
       }else {
          v7t.i("TBUploadService", "upload failed");
          this.val$result.b("subCode", p1.b);
          this.val$result.b("errorCode", p1.a);
          this.val$result.b("errorMsg", p1.c);
          this.val$result.b("localPath", this.val$params.a);
          this.val$result.b("tempFilePath", this.val$params.r);
          this.val$result.a("selectSize", Integer.valueOf(this.val$params.s));
          this.val$result.b("identifier", this.val$params.h);
          Message.obtain(TBUploadService.access$100(this.this$0), 2003, this.val$result).sendToTarget();
          return;
       }
    }
    public void onPause(z6e p0){
       IpChange $ipChange = TBUploadService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("c85aa60f", objArray);
       }
       return;
    }
    public void onProgress(z6e p0,int p1){
       IpChange $ipChange = TBUploadService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1)};
          $ipChange.ipc$dispatch("34b23fa9", objArray);
          return;
       }else {
          v7t.d("TBUploadService", "uploadFile onProgress "+String.valueOf(p1));
          return;
       }
    }
    public void onResume(z6e p0){
       IpChange $ipChange = TBUploadService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e581d4da", objArray);
       }
       return;
    }
    public void onStart(z6e p0){
       IpChange $ipChange = TBUploadService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f33e623", objArray);
       }
       return;
    }
    public void onSuccess(z6e p0,ozd p1){
       Bitmap uBitmap;
       int i1;
       int i = 1;
       IpChange $ipChange = TBUploadService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("10b3127c", objArray);
          return;
       }else {
          v7t.i("TBUploadService", "upload success");
          this.val$result.k();
          this.val$result.b("url", this.val$params.b);
          this.val$result.b("localPath", this.val$params.a);
          String str = p1.a();
          this.val$result.b("resourceURL", str);
          this.val$result.b("isLastPic", String.valueOf(this.val$params.l));
          this.val$result.b("mutipleSelection", this.val$params.j);
          this.val$result.b("tempFilePath", this.val$params.r);
          this.val$result.a("selectSize", Integer.valueOf(this.val$params.s));
          this.val$result.b("identifier", this.val$params.h);
          TBUploadService$3 tval$params = this.val$params;
          if (tval$params.p != null && (uBitmap = voe.c(tval$params.a, 1024)) != null) {
             this.val$result.b("base64Data", itw.a(uBitmap));
          }
          if (i1 = str.lastIndexOf("/") + i) {
             this.val$result.b("tfsKey", str.substring(i1));
          }
          TBUploadService$3 tval$params1 = this.val$params;
          if (tval$params1.l != null) {
             this.val$result.c("images", tval$params1.o);
          }
          Message.obtain(TBUploadService.access$100(this.this$0), 2002, this.val$result).sendToTarget();
          return;
       }
    }
    public void onWait(z6e p0){
       IpChange $ipChange = TBUploadService$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e3e24ed2", objArray);
       }
       return;
    }
}
