package android.taobao.windvane.extra.jsbridge.WVACCS;
import tb.kpw;
import tb.t2o;
import java.util.ArrayList;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import org.json.JSONObject;
import tb.nsw;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.content.Context;
import com.taobao.accs.ACCSManager;
import java.util.Map;
import tb.lqw;
import android.taobao.windvane.extra.jsbridge.WVACCS$ACCSWVEventListener;
import android.taobao.windvane.webview.IWVWebView;
import tb.jqw;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.net.URL;
import com.taobao.accs.ACCSManager$AccsRequest;
import java.lang.Boolean;

public class WVACCS extends kpw	// class@0001a1 from classes.dex
{
    public ArrayList serviceIdList;
    public static IpChange $ipChange;
    private static final String serviceClassName;
    private static final String serviceIdDefault;

    static {
       t2o.a(0x3d8000d5);
    }
    public void WVACCS(){
       super();
       this.serviceIdList = new ArrayList();
    }
    private void bindService(WVCallBackContext p0,String p1){
       CharSequence uCharSequenc;
       String str = "HY_PARAM_ERR";
       IpChange $ipChange = WVACCS.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("58028cc6", objArray);
          return;
       }else {
          try{
             p1 = new JSONObject(p1).optString("serviceId", "");
          }catch(org.json.JSONException e0){
             p0.error(new nsw(e0));
             uCharSequenc = null;
          }
          if (TextUtils.isEmpty(uCharSequenc)) {
             p0.error(new nsw(e0));
             return;
          }else if(this.serviceIdList == null){
             try{
                ArrayList str1 = new ArrayList();
                this.serviceIdList = str1;
                str1.add("windvane");
                ACCSManager.registerSerivce(this.mContext.getApplicationContext(), "windvane", "android.taobao.windvane.extra.jsbridge.WVACCSService");
             }catch(java.lang.Exception e0){
             }
          }
          if (this.serviceIdList.contains(uCharSequenc)) {
             p0.success();
          }else if(this.mContext != null){
             this.serviceIdList.add(uCharSequenc);
             ACCSManager.registerSerivce(this.mContext.getApplicationContext(), "windvane", "android.taobao.windvane.extra.jsbridge.WVACCSService");
             p0.success();
          }else {
             p0.error();
          }
          return;
       }
    }
    private void connectionState(WVCallBackContext p0,String p1){
       nsw onsw;
       String str = "false";
       String str1 = "status";
       IpChange $ipChange = WVACCS.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("7af1f3a1", objArray);
          return;
       }else {
          try{
             onsw = new nsw();
             if (ACCSManager.getChannelState(this.mContext) == null) {
                onsw.b(str1, str);
                p0.error();
             }
          }catch(java.lang.Exception e0){
             onsw.b(str1, e0);
             p0.error();
          }
          onsw.b(str1, "true");
          p0.success(onsw);
          return;
       }
    }
    private void init(Context p0){
       IpChange $ipChange = WVACCS.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("609fd211", objArray);
          return;
       }else {
          lqw.d().b(new WVACCS$ACCSWVEventListener(this.mWebView));
          return;
       }
    }
    public static Object ipc$super(WVACCS p0,String p1,Object[] p2){
       int i;
       if ((i = p1.hashCode()) != -1811143243) {
          if (i != -1504501726) {
             throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/jsbridge/WVACCS");
          }
          super.onDestroy();
          return null;
       }else {
          super.initialize(p2[0], p2[1]);
          return null;
       }
    }
    private void setData(WVCallBackContext p0,String p1){
       int i2;
       CharSequence uCharSequenc;
       CharSequence uCharSequenc1;
       ACCSManager$AccsRequest uAccsRequest;
       int i3;
       String str5;
       URL uRL;
       object oobject = this;
       object oobject1 = p0;
       object oobject2 = p1;
       int i = 0;
       String str = "HY_PARAM_ERR";
       String str1 = "";
       String str2 = "serviceId ";
       IpChange $ipChange = WVACCS.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,oobject1,oobject2};
          $ipChange.ipc$dispatch("fcd5df3a", objArray);
          return;
       }else {
          try{
             JSONObject jSONObject = new JSONObject(oobject2);
             String str3 = jSONObject.optString("serviceId", str1);
             try{
                if (TextUtils.isEmpty(str3)) {
                   try{
                      nsw onsw = new nsw();
                      onsw.b("msg", str2+str3+" is not bind!");
                      oobject1.error(onsw);
                      return;
                   }catch(org.json.JSONException e0){
                      i3 = str3;
                   }
                }else {
                   String str4 = jSONObject.optString("userId", str1);
                   JSONObject jSONObject1 = jSONObject.optJSONObject("options");
                   str5 = jSONObject.optString("data", str1);
                   if (jSONObject1 == null) {
                      oobject1.error(new nsw(str));
                      return;
                   }else {
                      String str6 = jSONObject1.optString("dataId", str1);
                      String str7 = jSONObject1.optString("host", str1);
                      String str8 = jSONObject1.optString("tag", str1);
                      boolean b = jSONObject1.optBoolean("isUnit", i);
                      i = jSONObject1.optInt("timeout", i);
                      String str9 = jSONObject1.optString("target", str1);
                      str1 = jSONObject1.optString("businessId", str1);
                      try{
                         uRL = new URL(str7);
                      }catch(java.lang.Exception e0){
                         uRL = 0;
                      }
                      ACCSManager$AccsRequest uAccsRequest1 = v12;
                      String str10 = str3;
                      ACCSManager$AccsRequest uAccsRequest2 = v12;
                      byte[] uobyteArray = str5.getBytes();
                      p1 = str3;
                      boolean b1 = b;
                      String str11 = str9;
                      String str12 = str5;
                      str5 = str8;
                      URL uRL1 = uRL;
                      str9 = str1;
                      uAccsRequest1 = new ACCSManager$AccsRequest(str4, str10, uobyteArray, str6, str11, uRL1, str9);
                      uAccsRequest2.setTag(str5);
                      uAccsRequest2.setIsUnitBusiness(b1);
                      uAccsRequest2.setTimeOut(i);
                      uCharSequenc = p1;
                      uAccsRequest = uAccsRequest2;
                      str5 = str12;
                   }
                }
             }catch(org.json.JSONException e0){
                i3 = str3;
             label_0059 :
                int i1 = 0;
             }catch(org.json.JSONException e0){
                int i4 = str3;
                i1 = str5;
                i3 = i4;
             }catch(org.json.JSONException e0){
             }catch(org.json.JSONException e0){
                i3 = str3;
                i1 = str5;
             }
          }catch(org.json.JSONException e0){
             $ipChange = 0;
             goto label_0059 ;
          }
          if (!TextUtils.isEmpty(uCharSequenc) && (!TextUtils.isEmpty(uCharSequenc1) && uAccsRequest != null)) {
             ACCSManager.sendData(e0.mContext, uAccsRequest);
             p0.success();
             return;
          }else {
             oobject1.error(new nsw(str));
             return;
          }
       }
    }
    private void unBindService(WVCallBackContext p0,String p1){
       CharSequence uCharSequenc;
       String str = "HY_PARAM_ERR";
       IpChange $ipChange = WVACCS.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("c97a5fad", objArray);
          return;
       }else {
          try{
             p1 = new JSONObject(p1).optString("serviceId", "");
          }catch(org.json.JSONException e0){
             p0.error(new nsw(e0));
             uCharSequenc = null;
          }
          if (TextUtils.isEmpty(uCharSequenc)) {
             p0.error(new nsw(e0));
             return;
          }else if(this.serviceIdList == null){
             this.serviceIdList = new ArrayList();
          }
          if (!this.serviceIdList.contains(uCharSequenc)) {
             p0.success();
          }else if(this.mContext != null){
             this.serviceIdList.remove(uCharSequenc);
             ACCSManager.unregisterService(this.mContext.getApplicationContext(), uCharSequenc);
             p0.success();
          }else {
             p0.error();
          }
          return;
       }
    }
    public boolean execute(String p0,String p1,WVCallBackContext p2){
       IpChange $ipChange = WVACCS.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("bcd41fd1", objArray).booleanValue();
       }else if("bindService".equals(p0)){
          this.bindService(p2, p1);
       }else if("unBindService".equals(p0)){
          this.unBindService(p2, p1);
       }else if("setData".equals(p0)){
          this.setData(p2, p1);
       }else if("connectionState".equals(p0)){
          this.connectionState(p2, p1);
       }else {
          return 0;
       }
       return 1;
    }
    public void initialize(Context p0,IWVWebView p1){
       IpChange $ipChange = WVACCS.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("940c25b5", objArray);
          return;
       }else {
          super.initialize(p0, p1);
          this.init(p0);
          return;
       }
    }
    public void onDestroy(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = WVACCS.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          $ipChange.ipc$dispatch("a6532022", objArray);
          return;
       }else if(this.mContext != null && this.serviceIdList != null){
          for (; i < this.serviceIdList.size(); i = i + i1) {
             ACCSManager.unregisterService(this.mContext.getApplicationContext(), this.serviceIdList.get(i));
          }
          this.serviceIdList.clear();
          this.serviceIdList = null;
       }
       super.onDestroy();
       return;
    }
}
