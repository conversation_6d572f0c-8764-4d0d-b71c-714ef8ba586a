package android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValueRange;
import tb.t2o;
import java.lang.Float;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;

public class FccStrategy$ConditionValueRange	// class@0001f3 from classes.dex
{
    public Float left;
    public Float right;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800129);
    }
    public void FccStrategy$ConditionValueRange(Float p0,Float p1){
       super();
       this.left = p0;
       this.right = p1;
    }
    public boolean checkValid(){
       FccStrategy$ConditionValueRange tleft;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = FccStrategy$ConditionValueRange.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("2b6d1a5f", objArray).booleanValue();
       }else if((tleft = this.left) == null && this.right == null){
          return i;
       }else if(tleft != null && this.right != null){
          if ((tleft.floatValue() - this.right.floatValue()) <= 0) {
             i = true;
          }
          return i;
       }else {
          return i1;
       }
    }
    public boolean isInRange(float p0){
       FccStrategy$ConditionValueRange tleft;
       FccStrategy$ConditionValueRange tright;
       int i = 1;
       IpChange $ipChange = FccStrategy$ConditionValueRange.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Float(p0)};
          return $ipChange.ipc$dispatch("fc8cea4f", objArray).booleanValue();
       }else if((tleft = this.left) != null && this.right != null){
          if ((tleft.floatValue() - p0) < 0 || (p0 - this.right.floatValue()) > 0) {
             i = false;
          }
          return i;
       }else if((tright = this.right) != null){
          if ((p0 - tright.floatValue()) > 0) {
             i = false;
          }
          return i;
       }else if(tleft != null){
          if ((tleft.floatValue() - p0) < 0) {
             i = false;
          }
          return i;
       }else {
          return 0;
       }
    }
}
