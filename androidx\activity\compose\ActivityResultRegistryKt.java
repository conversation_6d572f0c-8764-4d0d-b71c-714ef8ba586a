package androidx.activity.compose.ActivityResultRegistryKt;
import androidx.activity.result.contract.ActivityResultContract;
import tb.g1a;
import androidx.compose.runtime.a;
import androidx.activity.compose.ManagedActivityResultLauncher;
import java.lang.Object;
import tb.xf40;
import tb.me40;
import androidx.activity.compose.ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1;
import tb.m840;
import java.lang.String;
import tb.d1a;
import androidx.compose.runtime.saveable.RememberSaveableKt;
import androidx.activity.compose.LocalActivityResultRegistryOwner;
import androidx.activity.result.ActivityResultRegistryOwner;
import androidx.activity.result.ActivityResultRegistry;
import java.lang.Class;
import androidx.compose.runtime.a$a;
import androidx.activity.compose.ActivityResultLauncherHolder;
import androidx.activity.compose.ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1;
import androidx.compose.runtime.EffectsKt;
import java.lang.IllegalStateException;

public final class ActivityResultRegistryKt	// class@00047b from classes.dex
{

    public static final ManagedActivityResultLauncher rememberLauncherForActivityResult(ActivityResultContract p0,g1a p1,a p2,int p3){
       ActivityResultRegistryOwner current;
       Object a;
       ManagedActivityResultLauncher managedActiv;
       a uoa = p2;
       uoa.F(-1408504823);
       xf40 oxf40 = me40.o(p0, uoa, (p3 & 0x0e));
       xf40 oxf401 = me40.o(p1, uoa, ((p3 >> 3) & 0x0e));
       Object[] objArray = new Object[0];
       Object obj = RememberSaveableKt.d(objArray, null, null, ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1.INSTANCE, p2, 3072, 6);
       if ((current = LocalActivityResultRegistryOwner.INSTANCE.getCurrent(uoa, 6)) == null) {
          throw new IllegalStateException("No ActivityResultRegistryOwner was provided via LocalActivityResultRegistryOwner");
       }
       ActivityResultRegistry activityResu = current.getActivityResultRegistry();
       uoa.F(-1672765924);
       ActivityResultLauncherHolder uActivityRes = p2.o();
       a.Companion.getClass();
       a = a$a.a;
       if (uActivityRes == a) {
          uActivityRes = new ActivityResultLauncherHolder();
          uoa.E(uActivityRes);
       }
       p2.K();
       uoa.F(-1672765850);
       if ((managedActiv = p2.o()) == a) {
          managedActiv = new ManagedActivityResultLauncher(uActivityRes, oxf40);
          uoa.E(managedActiv);
       }
       Object obj1 = managedActiv;
       p2.K();
       uoa.F(-1672765582);
       ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1 orememberLau = p2.o();
       if ((((((uoa.y(uActivityRes) | uoa.y(activityResu)) | uoa.y(obj)) | uoa.y(p0)) | uoa.y(oxf401))) || orememberLau == a) {
          ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1 v11 = new ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1(uActivityRes, activityResu, obj, p0, oxf401);
          uoa.E(v11);
          orememberLau = v11;
       }
       p2.K();
       EffectsKt.a(activityResu, obj, p0, orememberLau, p2, ((p3 << 6) & 0x0380));
       p2.K();
       return obj1;
    }
}
