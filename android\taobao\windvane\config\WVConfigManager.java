package android.taobao.windvane.config.WVConfigManager;
import tb.t2o;
import java.lang.Object;
import java.util.concurrent.ConcurrentHashMap;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ipb;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;

public class WVConfigManager	// class@00014d from classes.dex
{
    public final ConcurrentHashMap a;
    public static IpChange $ipChange;
    public static final String CONFIGNAME_COMMON;
    public static final String CONFIGNAME_COOKIE;
    public static final String CONFIGNAME_DOMAIN;
    public static final String CONFIGNAME_LOCALE;
    public static final String CONFIGNAME_UC_CORE;
    public static final String CONFIGNAME_URL_CONFIG;
    public static final String SPNAME_CONFIG;
    public static WVConfigManager b;

    static {
       t2o.a(0x3d80002c);
       WVConfigManager.b = null;
    }
    public void WVConfigManager(){
       super();
       this.a = new ConcurrentHashMap();
    }
    public static WVConfigManager a(){
       IpChange $ipChange = WVConfigManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("b146ad30", objArray);
       }else if(WVConfigManager.b == null){
          WVConfigManager wVConfigMana = WVConfigManager.class;
          _monitor_enter(wVConfigMana);
          if (WVConfigManager.b == null) {
             WVConfigManager.b = new WVConfigManager();
          }
          _monitor_exit(wVConfigMana);
       }
       return WVConfigManager.b;
    }
    public void b(String p0,ipb p1){
       IpChange $ipChange = WVConfigManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("27c06ae3", objArray);
          return;
       }else {
          this.a.put(p0, p1);
          return;
       }
    }
    public void c(){
       WVConfigManager ta;
       IpChange $ipChange = WVConfigManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("6e187978", objArray);
          return;
       }else if((ta = this.a) != null){
          Iterator iterator = ta.entrySet().iterator();
          while (iterator.hasNext()) {
             iterator.next().getValue().a("");
          }
       }
       return;
    }
    public void d(String p0,String p1){
       ipb oipb;
       IpChange $ipChange = WVConfigManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("76314f8", objArray);
          return;
       }else if((oipb = this.a.get(p0)) != null){
          oipb.a(p1);
       }
       return;
    }
}
