package android.taobao.windvane.extra.uc.WVUCClient$1;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.uc.WVUCClient;
import com.uc.webview.export.WebView;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Integer;
import tb.srw;
import tb.trw;
import android.taobao.windvane.extra.core.WVCore;
import java.lang.StringBuilder;
import tb.v7t;

public class WVUCClient$1 implements ValueCallback	// class@000236 from classes.dex
{
    public final WVUCClient this$0;
    public final WebView val$webView;
    public static IpChange $ipChange;

    public void WVUCClient$1(WVUCClient p0,WebView p1){
       this.this$0 = p0;
       this.val$webView = p1;
       super();
    }
    public void onReceiveValue(Object p0){
       IpChange $ipChange = WVUCClient$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("c60988b0", objArray);
          return;
       }else if(p0 instanceof Integer){
          int i = p0.intValue();
          if (trw.getWvMonitorInterface() != null) {
             trw.getWvMonitorInterface().commitWebMultiType(this.val$webView.getUrl(), WVCore.getInstance().getUsedWebMulti(), i);
          }
          v7t.i("sandbox", "process mode: "+i);
       }
       return;
    }
}
