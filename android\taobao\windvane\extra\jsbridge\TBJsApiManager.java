package android.taobao.windvane.extra.jsbridge.TBJsApiManager;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.jsbridge.TBUploadService;
import java.lang.Class;
import android.taobao.windvane.jsbridge.api.WVCamera;
import android.taobao.windvane.extra.jsbridge.WVApplication;
import tb.fsw;
import android.taobao.windvane.extra.jsbridge.WVReporterExtra;
import android.taobao.windvane.extra.performance2.WVPageTrackerAPI;

public class TBJsApiManager	// class@00019a from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000cf);
    }
    public void TBJsApiManager(){
       super();
    }
    public static void initJsApi(){
       IpChange $ipChange = TBJsApiManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("9e8784e8", objArray);
          return;
       }else {
          WVCamera.registerUploadService(TBUploadService.class);
          fsw.h("WVApplication", WVApplication.class);
          fsw.h("WVReporter", WVReporterExtra.class);
          fsw.h("WVPageTracker", WVPageTrackerAPI.class);
          return;
       }
    }
}
