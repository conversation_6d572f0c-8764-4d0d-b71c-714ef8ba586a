package android.taobao.windvane.extra.jsbridge.CommonAsyncJSAPIBridge$sam$java_lang_Runnable$0;
import java.lang.Runnable;
import tb.d1a;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ckf;

public final class CommonAsyncJSAPIBridge$sam$java_lang_Runnable$0 implements Runnable	// class@000195 from classes.dex
{
    private final d1a function;
    public static IpChange $ipChange;

    public void CommonAsyncJSAPIBridge$sam$java_lang_Runnable$0(d1a p0){
       super();
       this.function = p0;
    }
    public final void run(){
       IpChange $ipChange = CommonAsyncJSAPIBridge$sam$java_lang_Runnable$0.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          ckf.f(this.function.invoke(), "invoke\(...\)");
          return;
       }
    }
}
