package android.taobao.windvane.extra.uc.WVUCWebChromeClient$1;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.WVUCWebChromeClient;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.v7t;

public class WVUCWebChromeClient$1 implements Runnable	// class@000243 from classes.dex
{
    public final WVUCWebChromeClient this$0;
    public static IpChange $ipChange;

    public void WVUCWebChromeClient$1(WVUCWebChromeClient p0){
       this.this$0 = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = WVUCWebChromeClient$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          v7t.a("WVUCWebChromeClient", " openFileChooser permission denied");
          return;
       }
    }
}
