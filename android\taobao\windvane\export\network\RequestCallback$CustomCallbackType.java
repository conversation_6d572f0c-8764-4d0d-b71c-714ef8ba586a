package android.taobao.windvane.export.network.RequestCallback$CustomCallbackType;
import java.lang.annotation.Annotation;

public interface abstract RequestCallback$CustomCallbackType implements Annotation	// class@00016d from classes.dex
{
    public static final int ON_FIRST_CHUNK_RECEIVED = 2;
    public static final int ON_REPORT_HIT_LOCAL_SNAPSHOT = 1;
    public static final int ON_REPORT_SNAPSHOT_HIT_TYPE = 0;
    public static final int ON_SNAPSHOT_FAILOVER = 3;

}
