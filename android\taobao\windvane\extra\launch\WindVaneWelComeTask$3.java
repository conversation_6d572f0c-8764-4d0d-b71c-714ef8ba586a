package android.taobao.windvane.extra.launch.WindVaneWelComeTask$3;
import java.lang.Runnable;
import android.taobao.windvane.extra.launch.WindVaneWelComeTask;
import android.app.Application;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.uc.UCSetupServiceUtil;
import tb.vpw;
import tb.wpw;
import android.os.Build$VERSION;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.v7t;

public class WindVaneWelComeTask$3 implements Runnable	// class@0001c5 from classes.dex
{
    public final WindVaneWelComeTask this$0;
    public final Application val$application;
    public static IpChange $ipChange;

    public void WindVaneWelComeTask$3(WindVaneWelComeTask p0,Application p1){
       this.this$0 = p0;
       this.val$application = p1;
       super();
    }
    public void run(){
       Object[] objArray;
       int sDK_INT;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = WindVaneWelComeTask$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i1];
          objArray[i] = this;
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          UCSetupServiceUtil.preloadUCNecessaryConfigSync();
          wpw commonConfig = vpw.commonConfig;
          if (commonConfig.k3 != null) {
             if ((sDK_INT = Build$VERSION.SDK_INT) == 35 && (WindVaneWelComeTask.access$000() && commonConfig.l3 == null)) {
                objArray = 0;
             }
             if (sDK_INT != 35 || (!WindVaneWelComeTask.access$100() || commonConfig.y3 != null)) {
                i = objArray;
             }
             if (i) {
                WindVaneWelComeTask.access$200(this.this$0, this.val$application);
             }
          }
          UCSetupServiceUtil.configUCSettingsBeforeInit();
          return;
       }
    }
}
