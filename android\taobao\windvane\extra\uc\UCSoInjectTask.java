package android.taobao.windvane.extra.uc.UCSoInjectTask;
import java.io.Serializable;
import tb.t2o;
import java.lang.Object;
import android.app.Application;
import java.util.HashMap;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.content.Context;
import tb.h8s;
import com.taobao.themis.kernel.basic.TMSLogger;
import tb.v7t;
import tb.dpw;
import tb.x7j;
import tb.lqw;
import tb.xrw;
import tb.jqw;

public class UCSoInjectTask implements Serializable	// class@00022c from classes.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x3d800161);
    }
    public void UCSoInjectTask(){
       super();
    }
    public void init(Application p0,HashMap p1){
       IpChange $ipChange = UCSoInjectTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("dddb138b", objArray);
          return;
       }else if(h8s.a(p0.getApplicationContext())){
          TMSLogger.b("TMSEarlyInitializer", "skip UCSoInjectTask");
          return;
       }else {
          v7t.d("UCSoInjectTask", "init");
          dpw.a();
          x7j.a();
          lqw.d().c(xrw.getInstance(), lqw.e);
          return;
       }
    }
}
