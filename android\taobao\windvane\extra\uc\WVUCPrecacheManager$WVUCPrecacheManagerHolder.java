package android.taobao.windvane.extra.uc.WVUCPrecacheManager$WVUCPrecacheManagerHolder;
import tb.t2o;
import android.taobao.windvane.extra.uc.WVUCPrecacheManager;
import android.taobao.windvane.extra.uc.WVUCPrecacheManager$1;
import java.lang.Object;

public class WVUCPrecacheManager$WVUCPrecacheManagerHolder	// class@00023c from classes.dex
{
    public static final WVUCPrecacheManager sInstance;

    static {
       t2o.a(0x3d800172);
       WVUCPrecacheManager$WVUCPrecacheManagerHolder.sInstance = new WVUCPrecacheManager(null);
    }
    private void WVUCPrecacheManager$WVUCPrecacheManagerHolder(){
       super();
    }
}
