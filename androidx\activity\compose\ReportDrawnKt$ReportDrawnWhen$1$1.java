package androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$1$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.FullyDrawnReporter;
import tb.d1a;
import java.lang.Object;
import tb.hi20;
import tb.gi20;
import androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$1;
import androidx.activity.compose.ReportDrawnComposition;
import androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$2;

public final class ReportDrawnKt$ReportDrawnWhen$1$1 extends Lambda implements g1a	// class@00049e from classes.dex
{
    public final FullyDrawnReporter $fullyDrawnReporter;
    public final d1a $predicate;

    public void ReportDrawnKt$ReportDrawnWhen$1$1(FullyDrawnReporter p0,d1a p1){
       this.$fullyDrawnReporter = p0;
       this.$predicate = p1;
       super(1);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final gi20 invoke(hi20 p0){
       ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$1 reportDrawnW = (this.$fullyDrawnReporter.isFullyDrawnReported())? new ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$1(): new ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$2(new ReportDrawnComposition(this.$fullyDrawnReporter, this.$predicate));
       return reportDrawnW;
    }
}
