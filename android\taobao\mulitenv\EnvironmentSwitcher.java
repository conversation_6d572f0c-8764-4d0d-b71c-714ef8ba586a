package android.taobao.mulitenv.EnvironmentSwitcher;
import tb.t2o;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import java.lang.Number;
import android.app.Application;
import com.taobao.tao.Globals;
import android.content.Context;
import android.content.SharedPreferences;
import android.preference.PreferenceManager;
import android.taobao.mulitenv.EnvironmentSwitcher$HttpsValidationStrategy;
import java.lang.Enum;
import android.taobao.mulitenv.EnvironmentSwitcher$SpdySSLStrategy;
import com.taobao.taobao.R$string;
import java.lang.Integer;
import android.taobao.mulitenv.EnvironmentSwitcher$EnvType;
import android.content.SharedPreferences$Editor;

public class EnvironmentSwitcher	// class@000143 from classes.dex
{
    public static IpChange $ipChange;
    public static final String HTTP_VALIDATION;
    public static final String SPKEY_ENV;
    public static final String SPKEY_PROJECT_ID;
    public static final String SPKEY_SPDYSSLS;
    public static final String SPKEY_SS;
    public static final String TLOG_LEVEL;
    public static final String TLOG_TAG;
    public static final String TLOG_VERSION;

    static {
       t2o.a(0x30c00003);
    }
    public static int a(){
       IpChange $ipChange = EnvironmentSwitcher.$ipChange;
       int i = 0;
       if (!$ipChange instanceof IpChange) {
          return PreferenceManager.getDefaultSharedPreferences(Globals.getApplication()).getInt("env_taobao", i);
       }
       Object[] objArray = new Object[i];
       return $ipChange.ipc$dispatch("98d4c6c2", objArray).intValue();
    }
    public static EnvironmentSwitcher$HttpsValidationStrategy b(){
       IpChange $ipChange = EnvironmentSwitcher.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return EnvironmentSwitcher$HttpsValidationStrategy.values()[PreferenceManager.getDefaultSharedPreferences(Globals.getApplication()).getInt("http_validation", EnvironmentSwitcher$HttpsValidationStrategy.DISABLE_DEGRADE.ordinal())];
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("da5dbe02", objArray);
    }
    public static String c(){
       IpChange $ipChange = EnvironmentSwitcher.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return PreferenceManager.getDefaultSharedPreferences(Globals.getApplication()).getString("projectid", "");
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("dda325b", objArray);
    }
    public static EnvironmentSwitcher$SpdySSLStrategy d(){
       IpChange $ipChange = EnvironmentSwitcher.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return EnvironmentSwitcher$SpdySSLStrategy.values()[PreferenceManager.getDefaultSharedPreferences(Globals.getApplication()).getInt("spdyssls", EnvironmentSwitcher$SpdySSLStrategy.DISABLE_DEGRADE.ordinal())];
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("99bdce2", objArray);
    }
    public static void e(){
       int intx;
       IpChange $ipChange = EnvironmentSwitcher.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          $ipChange.ipc$dispatch("efcd2fc4", objArray);
          return;
       }else if((intx = PreferenceManager.getDefaultSharedPreferences(Globals.getApplication()).getInt("env_taobao", -1)) == -1){
          if ((intx = Integer.parseInt(Globals.getApplication().getString(R$string.env_default))) >= 0 && intx < 4) {
             i = intx;
          }
          intx = i;
       }
       EnvironmentSwitcher.f(EnvironmentSwitcher$EnvType.values()[intx]);
       return;
    }
    public static void f(EnvironmentSwitcher$EnvType p0){
       IpChange $ipChange = EnvironmentSwitcher.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("9ce18f47", objArray);
          return;
       }else {
          SharedPreferences$Editor uEditor = PreferenceManager.getDefaultSharedPreferences(Globals.getApplication()).edit();
          uEditor.putInt("env_taobao", EnvironmentSwitcher$EnvType.access$000(p0));
          uEditor.commit();
          return;
       }
    }
}
