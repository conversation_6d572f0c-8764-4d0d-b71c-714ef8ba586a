package android.taobao.windvane.extra.uc.MTopSSRRequest;
import android.taobao.windvane.extra.uc.interfaces.IRequest;
import tb.t2o;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import java.lang.String;
import java.util.Map;
import java.lang.Object;
import tb.vpw;
import tb.wpw;
import tb.x74;
import android.taobao.windvane.extra.uc.APIContextHelper;
import java.net.URLEncoder;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import java.lang.Throwable;
import tb.bgq$b;
import tb.bgq;
import com.android.alibaba.ip.runtime.IpChange;
import android.taobao.windvane.extra.uc.interfaces.INetwork;
import android.taobao.windvane.extra.uc.interfaces.IRequestTiming;
import java.lang.Number;

public class MTopSSRRequest implements IRequest	// class@000215 from classes.dex
{
    private EventHandler mEventHandler;
    private final Map mHeaders;
    private final boolean mIsUCProxyReq;
    public int mLoadType;
    private final String mMethod;
    private IRequestTiming mRequestTiming;
    public int mRequestType;
    private final bgq mSsrRequest;
    private final Map mUcHeaders;
    public Map mUploadDataMap;
    public Map mUploadFileMap;
    public long mUploadFileTotalLen;
    private final String mUrl;
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x3d80014a);
       t2o.a(0x3d8001a9);
    }
    public void MTopSSRRequest(EventHandler p0,String p1,String p2,boolean p3,Map p4,Map p5,Map p6,Map p7,long p8,int p9,int p10){
       String aPICallRecor;
       int i = this;
       String str = p1;
       String str1 = p2;
       Map map = p4;
       super();
       wpw commonConfig = vpw.commonConfig;
       if (commonConfig.w1 != null && map) {
          p4.put("tb-client-context", x74.d());
          if ((aPICallRecor = APIContextHelper.getAPICallRecords(p1)) != null) {
             if (commonConfig.X1 != null) {
                try{
                   aPICallRecor = URLEncoder.encode(aPICallRecor, "UTF-8");
                }catch(java.lang.Exception e0){
                   lcn.a(RVLLevel.Error, "WindVaneMTopSSRRequest").j("encodeAPIContext").a("msg", e0.getMessage()).f();
                }
             }
             p4.put("api-context", aPICallRecor);
          }
       }
       i.mEventHandler = p0;
       i.mUrl = str;
       i.mMethod = str1;
       i.mIsUCProxyReq = p3;
       i.mHeaders = map;
       i.mUcHeaders = p5;
       i.mUploadFileMap = p6;
       i.mUploadDataMap = p7;
       i.mUploadFileTotalLen = p8;
       i.mRequestType = p9;
       i.mLoadType = p10;
       i.mSsrRequest = new bgq$b().d(p1).b(p4).c(p2).a();
       return;
    }
    public void cancel(){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("707fe601", objArray);
       }
       return;
    }
    public IRequest copyRequest(INetwork p0){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.formatRequest(this.mEventHandler, this.mUrl, this.mMethod, this.mIsUCProxyReq, this.mHeaders, this.mUcHeaders, this.mUploadFileMap, this.mUploadDataMap, this.mUploadFileTotalLen, this.mRequestType, this.mLoadType);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("5b0fbdb4", objArray);
    }
    public EventHandler getEventHandler(){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mEventHandler;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ccb6ed91", objArray);
    }
    public Map getHeaders(){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mHeaders;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("cf4415cc", objArray);
    }
    public String getMethod(){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mMethod;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5e63d782", objArray);
    }
    public IRequestTiming getRequestTiming(){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mRequestTiming;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1f4a73b4", objArray);
    }
    public bgq getSsrRequest(){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mSsrRequest;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("a9b928fa", objArray);
    }
    public Map getUploadDataMap(){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mUploadDataMap;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("e57f3617", objArray);
    }
    public Map getUploadFileMap(){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mUploadFileMap;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("96766785", objArray);
    }
    public long getUploadFileTotalLen(){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mUploadFileTotalLen;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6542f2df", objArray).longValue();
    }
    public String getUrl(){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mUrl;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("de8f0660", objArray);
    }
    public void setEventHandler(EventHandler p0){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("31cc001", objArray);
          return;
       }else {
          this.mEventHandler = p0;
          return;
       }
    }
    public void setRequestTiming(IRequestTiming p0){
       IpChange $ipChange = MTopSSRRequest.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d11bbce6", objArray);
          return;
       }else {
          this.mRequestTiming = p0;
          return;
       }
    }
}
