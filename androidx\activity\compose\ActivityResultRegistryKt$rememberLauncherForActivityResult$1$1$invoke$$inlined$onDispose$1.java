package androidx.activity.compose.ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1$invoke$$inlined$onDispose$1;
import tb.gi20;
import androidx.activity.compose.ActivityResultLauncherHolder;
import java.lang.Object;

public final class ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1$invoke$$inlined$onDispose$1 implements gi20	// class@000478 from classes.dex
{
    public final ActivityResultLauncherHolder $realLauncher$inlined;

    public void ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1$invoke$$inlined$onDispose$1(ActivityResultLauncherHolder p0){
       this.$realLauncher$inlined = p0;
       super();
    }
    public void dispose(){
       this.$realLauncher$inlined.unregister();
    }
}
