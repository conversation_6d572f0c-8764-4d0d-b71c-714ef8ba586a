package android.support.v4.app.INotificationSideChannel$Stub$Proxy;
import android.support.v4.app.INotificationSideChannel;
import android.os.IBinder;
import java.lang.Object;
import java.lang.String;
import android.os.Parcel;
import android.app.Notification;
import android.os.Parcelable;
import android.support.v4.app.INotificationSideChannel$_Parcel;

class INotificationSideChannel$Stub$Proxy implements INotificationSideChannel	// class@000128 from classes.dex
{
    private IBinder mRemote;

    public void INotificationSideChannel$Stub$Proxy(IBinder p0){
       super();
       this.mRemote = p0;
    }
    public IBinder asBinder(){
       return this.mRemote;
    }
    public void cancel(String p0,int p1,String p2){
       Parcel parcel = Parcel.obtain();
       parcel.writeInterfaceToken(INotificationSideChannel.DESCRIPTOR);
       parcel.writeString(p0);
       parcel.writeInt(p1);
       parcel.writeString(p2);
       this.mRemote.transact(2, parcel, null, 1);
       parcel.recycle();
    }
    public void cancelAll(String p0){
       Parcel parcel = Parcel.obtain();
       parcel.writeInterfaceToken(INotificationSideChannel.DESCRIPTOR);
       parcel.writeString(p0);
       this.mRemote.transact(3, parcel, null, 1);
       parcel.recycle();
    }
    public String getInterfaceDescriptor(){
       return INotificationSideChannel.DESCRIPTOR;
    }
    public void notify(String p0,int p1,String p2,Notification p3){
       Parcel parcel = Parcel.obtain();
       parcel.writeInterfaceToken(INotificationSideChannel.DESCRIPTOR);
       parcel.writeString(p0);
       parcel.writeInt(p1);
       parcel.writeString(p2);
       INotificationSideChannel$_Parcel.access$100(parcel, p3, 0);
       this.mRemote.transact(1, parcel, null, 1);
       parcel.recycle();
    }
}
