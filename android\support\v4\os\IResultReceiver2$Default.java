package android.support.v4.os.IResultReceiver2$Default;
import android.support.v4.os.IResultReceiver2;
import java.lang.Object;
import android.os.IBinder;
import android.os.Bundle;

public class IResultReceiver2$Default implements IResultReceiver2	// class@000132 from classes.dex
{

    public void IResultReceiver2$Default(){
       super();
    }
    public IBinder asBinder(){
       return null;
    }
    public void send(int p0,Bundle p1){
    }
}
