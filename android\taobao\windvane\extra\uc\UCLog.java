package android.taobao.windvane.extra.uc.UCLog;
import com.uc.webview.base.klog.ILogger;
import tb.t2o;
import tb.og8;
import tb.abq;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import android.taobao.windvane.extra.uc.UCLog$Holder;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.vpw;
import tb.wpw;
import com.taobao.tao.log.TLog;
import java.lang.Boolean;
import com.uc.webview.export.extension.SettingKeys;
import com.uc.webview.export.extension.GlobalSettings;
import com.uc.webview.base.klog.ILogger$Instance;
import tb.v7t;

public class UCLog extends ILogger	// class@000221 from classes.dex
{
    private final abq mSpanWrapper;
    public static IpChange $ipChange;
    private static final boolean DEBUG;
    private static final String MODULE;
    private static final String TAG;
    private static boolean isInit;

    static {
       t2o.a(0x3d800155);
       UCLog.DEBUG = og8.b();
       UCLog.isInit = false;
    }
    public void UCLog(){
       super();
       this.mSpanWrapper = new abq();
    }
    public static final UCLog getInstance(){
       IpChange $ipChange = UCLog.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return UCLog$Holder.sInstance;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("9a6d2e3b", objArray);
    }
    public static Object ipc$super(UCLog p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/UCLog");
    }
    private void log(String p0,String p1,Throwable p2){
       IpChange $ipChange = UCLog.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("29cf83f6", objArray);
          return;
       }else if(vpw.commonConfig.h2 == null){
          return;
       }else if(p2 == null){
          TLog.loge("WindVane", p0, p1);
       }else {
          TLog.loge("WindVane", p0, p1, p2);
       }
       return;
    }
    public void d(String p0,String p1,Throwable p2){
       IpChange $ipChange = UCLog.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("80ec3ad6", objArray);
          return;
       }else {
          this.log(p0, p1, p2);
          return;
       }
    }
    public void e(String p0,String p1,Throwable p2){
       IpChange $ipChange = UCLog.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("152aaa75", objArray);
          return;
       }else {
          this.log(p0, p1, p2);
          return;
       }
    }
    public abq getSpanWrapper(){
       IpChange $ipChange = UCLog.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mSpanWrapper;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5b230494", objArray);
    }
    public void i(String p0,String p1,Throwable p2){
       IpChange $ipChange = UCLog.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("662468f1", objArray);
          return;
       }else {
          this.log(p0, p1, p2);
          return;
       }
    }
    public void init(boolean p0){
       int i = 1;
       IpChange $ipChange = UCLog.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("edcf041d", objArray);
          return;
       }else if(!p0 && !UCLog.DEBUG){
          p0 = false;
       }else {
          p0 = true;
       }
       GlobalSettings.set(SettingKeys.EnableKLog, p0);
       GlobalSettings.set(SettingKeys.SdkEnableLogToLogCat, UCLog.DEBUG);
       ILogger iLogger = (p0)? this: null;
       ILogger$Instance.set(iLogger);
       UCLog.isInit = i;
       TLog.loge("WindVane", "UCLog", "init success");
       return;
    }
    public boolean isInited(){
       IpChange $ipChange = UCLog.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return UCLog.isInit;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ae907f84", objArray).booleanValue();
    }
    public void w(String p0,String p1,Throwable p2){
       IpChange $ipChange = UCLog.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("818e83a3", objArray);
          return;
       }else {
          this.log(p0, p1, p2);
          return;
       }
    }
}
