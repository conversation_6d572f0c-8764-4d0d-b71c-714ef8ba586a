package androidx.activity.compose.LocalFullyDrawnReporterOwner;
import androidx.activity.compose.LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1;
import tb.le40;
import tb.d1a;
import java.lang.Object;
import tb.v140;
import androidx.compose.runtime.CompositionLocalKt;
import androidx.compose.runtime.a;
import androidx.activity.FullyDrawnReporterOwner;
import tb.oa20;
import androidx.compose.ui.platform.AndroidCompositionLocals_androidKt;
import android.view.View;
import androidx.activity.ViewTreeFullyDrawnReporterOwner;
import android.content.Context;
import android.content.ContextWrapper;
import tb.x140;

public final class LocalFullyDrawnReporterOwner	// class@000486 from classes.dex
{
    public static final int $stable;
    public static final LocalFullyDrawnReporterOwner INSTANCE;
    private static final v140 LocalFullyDrawnReporterOwner;

    static {
       LocalFullyDrawnReporterOwner.INSTANCE = new LocalFullyDrawnReporterOwner();
       LocalFullyDrawnReporterOwner.LocalFullyDrawnReporterOwner = CompositionLocalKt.e(null, LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1.INSTANCE, 1, null);
    }
    private void LocalFullyDrawnReporterOwner(){
       super();
    }
    public final FullyDrawnReporterOwner getCurrent(a p0,int p1){
       p0.F(0x20329958);
       FullyDrawnReporterOwner uFullyDrawnR = p0.d(LocalFullyDrawnReporterOwner.LocalFullyDrawnReporterOwner);
       p0.F(0x5fc124c8);
       if (uFullyDrawnR == null) {
          uFullyDrawnR = ViewTreeFullyDrawnReporterOwner.get(p0.d(AndroidCompositionLocals_androidKt.i()));
       }
       p0.K();
       if (uFullyDrawnR == null) {
          uFullyDrawnR = p0.d(AndroidCompositionLocals_androidKt.g());
          while (true) {
             if (uFullyDrawnR instanceof ContextWrapper) {
                if (uFullyDrawnR instanceof FullyDrawnReporterOwner) {
                   break ;
                }else {
                   uFullyDrawnR = uFullyDrawnR.getBaseContext();
                }
             }else {
                uFullyDrawnR = null;
             }
          }
       }
       p0.K();
       return uFullyDrawnR;
    }
    public final x140 provides(FullyDrawnReporterOwner p0){
       return LocalFullyDrawnReporterOwner.LocalFullyDrawnReporterOwner.d(p0);
    }
}
