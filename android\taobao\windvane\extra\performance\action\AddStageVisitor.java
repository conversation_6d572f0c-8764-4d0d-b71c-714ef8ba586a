package android.taobao.windvane.extra.performance.action.AddStageVisitor;
import android.taobao.windvane.extra.performance.action.IPerformanceVisitor;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import tb.cce;
import com.android.alibaba.ip.runtime.IpChange;

public class AddStageVisitor implements IPerformanceVisitor	// class@0001d4 from classes.dex
{
    public final String name;
    public long upTime;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800109);
       t2o.a(0x3d80010a);
    }
    public void AddStageVisitor(String p0,long p1){
       super();
       this.name = p0;
       this.upTime = p1;
    }
    public void accept(cce p0){
       IpChange $ipChange = AddStageVisitor.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("987066e1", objArray);
          return;
       }else {
          p0.onStage(this.name, this.upTime);
          return;
       }
    }
}
