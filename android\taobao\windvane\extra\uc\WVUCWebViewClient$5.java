package android.taobao.windvane.extra.uc.WVUCWebViewClient$5;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.uc.WVUCWebViewClient;
import com.uc.webview.export.WebView;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;
import tb.v7t;
import android.taobao.windvane.jsbridge.WVBridgeEngine;

public class WVUCWebViewClient$5 implements ValueCallback	// class@000267 from classes.dex
{
    public final WVUCWebViewClient this$0;
    public final WebView val$view;
    public static IpChange $ipChange;

    public void WVUCWebViewClient$5(WVUCWebViewClient p0,WebView p1){
       this.this$0 = p0;
       this.val$view = p1;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       IpChange $ipChange = WVUCWebViewClient$5.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else {
          v7t.i("WVJsBridge", "has windvane :"+p0);
          if ("false".equals(p0)) {
             this.val$view.loadUrl("javascript:"+WVBridgeEngine.WINDVANE_CORE_JS);
          }
          return;
       }
    }
}
