package android.taobao.windvane.extra.jsbridge.WVMegaBridge$1$1;
import tb.s2d;
import android.taobao.windvane.extra.jsbridge.WVMegaBridge$1;
import java.lang.Object;
import com.alibaba.ability.result.ExecuteResult;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class WVMegaBridge$1$1 implements s2d	// class@0001a7 from classes.dex
{
    public final WVMegaBridge$1 this$1;
    public static IpChange $ipChange;

    public void WVMegaBridge$1$1(WVMegaBridge$1 p0){
       this.this$1 = p0;
       super();
    }
    public void onCallback(ExecuteResult p0){
       IpChange $ipChange = WVMegaBridge$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f183ed74", objArray);
       }
       return;
    }
}
