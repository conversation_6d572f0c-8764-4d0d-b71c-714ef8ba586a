package android.support.v4.os.IResultReceiver2$Stub$Proxy;
import android.support.v4.os.IResultReceiver2;
import android.os.IBinder;
import java.lang.Object;
import java.lang.String;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.support.v4.os.IResultReceiver2$_Parcel;

class IResultReceiver2$Stub$Proxy implements IResultReceiver2	// class@000133 from classes.dex
{
    private IBinder mRemote;

    public void IResultReceiver2$Stub$Proxy(IBinder p0){
       super();
       this.mRemote = p0;
    }
    public IBinder asBinder(){
       return this.mRemote;
    }
    public String getInterfaceDescriptor(){
       return IResultReceiver2.DESCRIPTOR;
    }
    public void send(int p0,Bundle p1){
       Parcel parcel = Parcel.obtain();
       parcel.writeInterfaceToken(IResultReceiver2.DESCRIPTOR);
       parcel.writeInt(p0);
       IResultReceiver2$_Parcel.access$100(parcel, p1, 0);
       this.mRemote.transact(1, parcel, null, 1);
       parcel.recycle();
    }
}
