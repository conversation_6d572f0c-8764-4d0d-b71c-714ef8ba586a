package android.taobao.windvane.extra.uc.WVPrefetchNetworkAdapter;
import tb.t2o;
import android.content.Context;
import anetwork.channel.Request;
import java.lang.Object;
import java.lang.String;
import java.lang.System;
import anetwork.channel.degrade.DegradableNetwork;
import anetwork.channel.http.HttpNetwork;
import com.android.alibaba.ip.runtime.IpChange;
import anetwork.channel.Network;
import java.lang.Number;
import java.lang.Boolean;
import java.lang.StringBuilder;
import tb.v7t;
import android.taobao.windvane.extra.uc.WVPrefetchNetworkAdapter$CallableThread;
import java.util.concurrent.FutureTask;
import java.util.concurrent.Callable;
import java.lang.Thread;
import java.lang.Runnable;
import tb.vpw;
import tb.wpw;
import java.util.concurrent.TimeUnit;
import anetwork.channel.Response;
import java.lang.Throwable;
import anetwork.channel.statist.StatisticData;
import tb.krw;
import java.util.Map;

public class WVPrefetchNetworkAdapter	// class@000233 from classes.dex
{
    private Network mAliNetwork;
    private Context mContext;
    private int mNetworkType;
    private Request request;
    private long startTime;
    private int state;
    private long timeout;
    private String urlString;
    public static IpChange $ipChange;
    public static final int NETWORK_TYPE_DEGRADABLE_NETWORK;
    public static final int NETWORK_TYPE_HTTP_NETWORK;
    public static final int NETWORK_TYPE_SPDY_NETWORK;
    public static final int STATE_ADDED;
    public static final int STATE_FAILED;
    public static final int STATE_RECEIVED;
    public static final int STATE_STARTED;
    public static final int STATE_TIMEOUT;
    private static final String TAG;

    static {
       t2o.a(0x3d800167);
    }
    public void WVPrefetchNetworkAdapter(Context p0,int p1,long p2,Request p3){
       WVPrefetchNetworkAdapter tmNetworkTyp;
       super();
       this.state = 0;
       this.mContext = p0;
       this.mNetworkType = p1;
       this.timeout = p2;
       this.request = p3;
       this.urlString = p3.getUrlString();
       this.startTime = System.currentTimeMillis();
       if ((tmNetworkTyp = this.mNetworkType) != null) {
          if (tmNetworkTyp == 1 || tmNetworkTyp == 2) {
             this.mAliNetwork = new DegradableNetwork(p0);
          }
       }else {
          this.mAliNetwork = new HttpNetwork(p0);
       }
       return;
    }
    public static Request access$000(WVPrefetchNetworkAdapter p0){
       IpChange $ipChange = WVPrefetchNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.request;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("f30450af", objArray);
    }
    public static Network access$100(WVPrefetchNetworkAdapter p0){
       IpChange $ipChange = WVPrefetchNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mAliNetwork;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("1ee965b1", objArray);
    }
    public static String access$200(WVPrefetchNetworkAdapter p0){
       IpChange $ipChange = WVPrefetchNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.urlString;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("5c69e0ad", objArray);
    }
    public static int access$300(WVPrefetchNetworkAdapter p0){
       IpChange $ipChange = WVPrefetchNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mNetworkType;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("afb06c65", objArray).intValue();
    }
    public long getStartTime(){
       IpChange $ipChange = WVPrefetchNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.startTime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("490f0b94", objArray).longValue();
    }
    public int getState(){
       IpChange $ipChange = WVPrefetchNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.state;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("830709d5", objArray).intValue();
    }
    public String getUrlString(){
       IpChange $ipChange = WVPrefetchNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.urlString;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6bb9898f", objArray);
    }
    public boolean sendRequest(){
       Response response;
       Throwable error;
       byte[] bytedata;
       int i = 0;
       int i1 = 1;
       String str = "status code=";
       String str1 = " failed ";
       String str2 = "response get success:";
       String str3 = "url=";
       IpChange $ipChange = WVPrefetchNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("9fa6eaf2", objArray).booleanValue();
       }else {
          _monitor_enter(this);
          this.state = i1;
          v7t.a("WVPrefetchNetworkAdapter", str3+this.request.getUrlString());
          FutureTask uFutureTask = new FutureTask(new WVPrefetchNetworkAdapter$CallableThread(this));
          Thread thread = new Thread(uFutureTask, "WVPrefetchNetworkAdapter");
          try{
             thread.start();
             vpw.b();
             response = uFutureTask.get((long)vpw.commonConfig.a0, TimeUnit.MILLISECONDS);
             v7t.a("WVPrefetchNetworkAdapter", str2+response);
          }catch(java.lang.Exception e4){
             v7t.d("WVPrefetchNetworkAdapter", "task.get error");
             e4.printStackTrace();
             response = null;
          }
          if (response == null) {
             this.state = 5;
             _monitor_exit(this);
             return i;
          }else {
             this.state = 2;
             if ((error = response.getError()) != null) {
                this.state = 4;
                v7t.d("WVPrefetchNetworkAdapter", str1+error);
             }else {
                i = response.getStatusCode();
                v7t.a("WVPrefetchNetworkAdapter", str+i);
                if (i >= 0 && (i != 301 && (i != 302 && (i != 303 && i != 307)))) {
                   StatisticData oneWayTime_A = (response.getStatisticData() != null)? response.getStatisticData().oneWayTime_ANet: 0;
                   StatisticData statisticDat = oneWayTime_A;
                   if ((bytedata = response.getBytedata()) != null && bytedata.length > 0) {
                      krw.d().b(this.request.getUrlString(), response.getConnHeadFields(), bytedata, this.timeout, statisticDat);
                      this.state = 3;
                   }
                }else {
                   v7t.a("WVPrefetchNetworkAdapter", "abort");
                }
             }
             _monitor_exit(this);
             return i1;
          }
       }
    }
}
