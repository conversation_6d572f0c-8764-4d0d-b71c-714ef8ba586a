package android.taobao.windvane.export.prerender.PrerenderManager$acquirePrerenderWebView$1$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.lang.Boolean;
import tb.xhv;
import com.android.alibaba.ip.runtime.IpChange;

public final class PrerenderManager$acquirePrerenderWebView$1$1 extends Lambda implements g1a	// class@000175 from classes.dex
{
    public static IpChange $ipChange;
    public static final PrerenderManager$acquirePrerenderWebView$1$1 INSTANCE;

    static {
       PrerenderManager$acquirePrerenderWebView$1$1.INSTANCE = new PrerenderManager$acquirePrerenderWebView$1$1();
    }
    public void PrerenderManager$acquirePrerenderWebView$1$1(){
       super(1);
    }
    public static Object ipc$super(PrerenderManager$acquirePrerenderWebView$1$1 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/export/prerender/PrerenderManager$acquirePrerenderWebView$1$1");
    }
    public Object invoke(Object p0){
       this.invoke(p0.booleanValue());
       return xhv.INSTANCE;
    }
    public final void invoke(boolean p0){
       IpChange $ipChange = PrerenderManager$acquirePrerenderWebView$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("36b985b5", objArray);
       }
       return;
    }
}
