package android.taobao.windvane.connect.HttpConnector;
import tb.t2o;
import java.lang.String;
import java.lang.System;
import java.lang.Object;
import tb.v7b;
import tb.b8b;
import com.android.alibaba.ip.runtime.IpChange;
import android.net.Uri;
import tb.h7b;
import java.io.ByteArrayOutputStream;
import java.net.URL;
import tb.v7t;
import tb.yaa;
import android.content.Context;
import org.apache.http.HttpHost;
import tb.ssj;
import tb.doo;
import javax.net.ssl.SSLSocketFactory;
import java.net.URLConnection;
import javax.net.ssl.HttpsURLConnection;
import org.apache.http.conn.ssl.StrictHostnameVerifier;
import javax.net.ssl.HostnameVerifier;
import java.net.HttpURLConnection;
import java.io.OutputStream;
import java.lang.StringBuilder;
import java.lang.Throwable;
import android.taobao.windvane.connect.HttpConnector$RedirectException;
import java.io.InputStream;
import java.util.zip.GZIPInputStream;
import java.io.DataInputStream;
import android.taobao.windvane.connect.HttpConnector$HttpOverFlowException;
import tb.zpw;
import android.taobao.windvane.connect.HttpConnector$NetWorkErrorException;
import android.taobao.windvane.connect.HttpConnector$HttpsErrorException;
import java.util.Map;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import java.lang.Thread;
import java.lang.NullPointerException;

public class HttpConnector	// class@000152 from classes.dex
{
    public int a;
    public h7b b;
    public static IpChange $ipChange;
    public static final String CACHE_CONTROL;
    public static final String CONTENT_LENGTH;
    public static final String CONTENT_TYPE;
    public static final String DATE;
    public static final String ETAG;
    public static final String EXPIRES;
    public static final String IF_MODIFY_SINCE;
    public static final String IF_NONE_MATCH;
    public static final String LAST_MODIFIED;
    public static final String REDIRECT_LOCATION;
    public static final String RESPONSE_CODE;
    public static final String SET_COOKIE;
    public static final String URL;
    public static final String c;

    static {
       t2o.a(0x3d80003b);
       HttpConnector.c = "HttpConnector";
       System.setProperty("http.keepAlive", "false");
    }
    public void HttpConnector(){
       super();
       this.a = 0;
       this.b = null;
    }
    public final b8b a(v7b p0){
       HttpConnector b;
       int i2;
       int i3;
       HttpConnector b3;
       Throwable throwable;
       GZIPInputStream gZIPInputStr;
       HttpURLConnection httpURLConne;
       InputStream inputStream;
       HttpConnector b4;
       HttpHost httpHost;
       Uri uri1;
       doo uodoo;
       HttpsURLConnection httpsURLConn;
       HttpConnector b5;
       HttpConnector a;
       b8b uob8b2;
       DataInputStream uDataInputSt;
       int i8;
       HttpConnector b6;
       DataInputStream uDataInputSt1;
       HttpConnector b7;
       object oobject = this;
       object oobject1 = p0;
       int i = 0;
       int i1 = 1;
       String str = "http";
       String str1 = "too many redirect";
       String str2 = "responeCode:";
       String str3 = "post data: ";
       IpChange $ipChange = HttpConnector.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,oobject1};
          return $ipChange.ipc$dispatch("6bd9c257", objArray);
       }else {
          Uri uri = p0.g();
          if ((b = oobject.b) != null) {
             b.d();
          }
          b8b uob8b = new b8b();
          int b1 = "https".equalsIgnoreCase(uri.getScheme());
          ByteArrayOutputStream uByteArrayOu = new ByteArrayOutputStream(128);
          try{
             i2 = 0;
             URL uRL = new URL(uri.toString());
             String host = uRL.getHost();
             if (b1) {
                String c = HttpConnector.c;
                v7t.i(c, "proxy or https");
                if ((httpHost = ssj.a(yaa.n)) != null) {
                   uri1 = uri;
                   uodoo = new doo(httpHost.getHostName(), httpHost.getPort(), i2, "taobao_hybrid_8.5.0");
                }else {
                   uri1 = uri;
                   v7t.a(c, "https:proxy: none");
                   uodoo = i2;
                }
                httpsURLConn = uRL.openConnection();
                if (uodoo != null) {
                   httpsURLConn.setSSLSocketFactory(uodoo);
                }
                StrictHostnameVerifier strictHostna = new StrictHostnameVerifier();
                try{
                   httpsURLConn.setHostnameVerifier(strictHostna);
                   httpsURLConn.setRequestProperty("Connection", "Keep-Alive");
                }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                   i3 = i2;
                }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                   i3 = i2;
                }catch(javax.net.ssl.SSLHandshakeException e0){
                   throwable = e0;
                   gZIPInputStr = i2;
                   inputStream = gZIPInputStr;
                }catch(java.lang.OutOfMemoryError e0){
                   throwable = e0;
                   gZIPInputStr = i2;
                   inputStream = gZIPInputStr;
                }
             label_0327 :
                if ((b3 = oobject.b) != null) {
                   b3.a(-5, "out of memory error");
                }
                throwable.printStackTrace();
                uByteArrayOu.reset();
                if (i2) {
                   try{
                      i2.close();
                   }catch(java.lang.Exception e0){
                      e0.printStackTrace();
                   }
                }
                if (inputStream) {
                   try{
                      inputStream.close();
                   }catch(java.lang.Exception e0){
                      e0.printStackTrace();
                   }
                }
                if (gZIPInputStr) {
                   try{
                      gZIPInputStr.close();
                   }catch(java.lang.Exception e0){
                      e0.printStackTrace();
                   }
                }
                try{
                   uByteArrayOu.close();
                }catch(java.lang.Exception e0){
                   e0.printStackTrace();
                }
                if (httpURLConne) {
                   httpURLConne.disconnect();
                }
                if ((b4 = oobject.b) != null) {
                   b4.b(new b8b(), 0);
                }
                return new b8b();
             }else {
                uri1 = uri;
                httpsURLConn = uRL.openConnection();
             }
             try{
                oobject.c(httpsURLConn, oobject1);
                if ((b5 = oobject.b) != null) {
                   b5.c(0);
                }
                try{
                   if ("post".equalsIgnoreCase(p0.c())) {
                      v7t.a(HttpConnector.c, str3.concat(new String(p0.d())));
                      httpsURLConn.setDoOutput(true);
                      httpsURLConn.setDoInput(true);
                      httpsURLConn.setRequestMethod("POST");
                      httpsURLConn.connect();
                      OutputStream outputStream = httpsURLConn.getOutputStream();
                      outputStream.write(p0.d());
                      outputStream.flush();
                      outputStream.close();
                   }else {
                      httpsURLConn.connect();
                   }
                   try{
                      i1 = httpsURLConn.getResponseCode();
                      v7t.a(HttpConnector.c, str2.append(i1).toString());
                      HttpConnector b2 = 300;
                      if (i1 >= b2 && (i1 < 400 && (i1 != 304 && p0.h()))) {
                         if ((a = oobject.a) <= 5) {
                            int i4 = a + 1;
                            oobject.a = i4;
                            if ((str3 = httpsURLConn.getHeaderField("location")) != null) {
                               if (!str3.toLowerCase().startsWith(str)) {
                                  str3 = new URL(str, host, str3).toString();
                               }
                               if (i1 != 305) {
                                  oobject1.k(Uri.parse(str3));
                                  uob8b2 = this.a(p0);
                                  try{
                                     uByteArrayOu.close();
                                  }catch(java.lang.Exception e0){
                                     e0.printStackTrace();
                                  }
                                  httpsURLConn.disconnect();
                                  return uob8b2;
                               }else {
                                  uob8b2 = oobject.a(new v7b(str3));
                                  try{
                                     uByteArrayOu.close();
                                  }catch(java.lang.Exception e0){
                                     e0.printStackTrace();
                                  }
                                  httpsURLConn.disconnect();
                                  return uob8b2;
                               }
                            }
                         }else {
                            throw new HttpConnector$RedirectException(oobject, str1);
                         }
                      }
                      uob8b.h(i1);
                      int i5 = 1;
                      while ((str = httpsURLConn.getHeaderFieldKey(i5)) != null) {
                         i5 = i5 + 1;
                         String headerField = httpsURLConn.getHeaderField(str);
                         uob8b.a(str, headerField);
                         if ("Set-Cookie".equals(str)) {
                            zpw.c(uri1.toString(), headerField);
                         }
                      }
                      try{
                         if (i1 >= 200 && i1 < b2) {
                            if ((i5 = httpsURLConn.getContentLength()) <= 0x500000) {
                               inputStream = httpsURLConn.getInputStream();
                               try{
                                  if ((str = httpsURLConn.getContentEncoding()) != null && "gzip".equals(str)) {
                                     try{
                                        GZIPInputStream str4 = new GZIPInputStream(inputStream);
                                        i2 = str4;
                                        uDataInputSt = new DataInputStream(str4);
                                     }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                                        i5 = uDataInputSt;
                                     }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                                        i5 = uDataInputSt;
                                     }catch(javax.net.ssl.SSLHandshakeException e0){
                                        gZIPInputStr = uDataInputSt;
                                     }catch(java.lang.OutOfMemoryError e0){
                                        gZIPInputStr = uDataInputSt;
                                     }
                                  }else {
                                     uDataInputSt = new DataInputStream(inputStream);
                                  }
                                  int i6 = 2048;
                                  try{
                                     byte[] uobyteArray = new byte[i6];
                                     int i7 = 0;
                                     while (true) {
                                        b1 = 0;
                                        if ((i8 = uDataInputSt.read(uobyteArray, b1, i6)) != -1) {
                                           uByteArrayOu.write(uobyteArray, b1, i8);
                                           if ((b6 = oobject.b) != null) {
                                              if ((i7 = i7 + i8) > i5) {
                                                 i5 = i7;
                                              }
                                              float f = (float)i7 / (float)i5;
                                              f = f * 100.00f;
                                              b6.c((int)f);
                                           }
                                        }else {
                                           break ;
                                        }
                                     }
                                     uob8b.f(uByteArrayOu.toByteArray());
                                     gZIPInputStr = i2;
                                     uDataInputSt1 = uDataInputSt;
                                  }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                                     i5 = i2;
                                  }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                                     i5 = i2;
                                  }catch(javax.net.ssl.SSLHandshakeException e0){
                                     gZIPInputStr = i2;
                                     InputStream inputStream1 = uDataInputSt;
                                  }catch(java.lang.OutOfMemoryError e0){
                                     gZIPInputStr = i2;
                                     inputStream1 = uDataInputSt;
                                  }
                               }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                                  i3 = i2;
                               }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                                  i3 = i2;
                               }catch(javax.net.ssl.SSLHandshakeException e0){
                                  throwable = e0;
                                  gZIPInputStr = i2;
                               }catch(java.lang.OutOfMemoryError e0){
                                  throwable = e0;
                                  gZIPInputStr = i2;
                                  goto label_0327 ;
                               }
                            }else {
                               throw new HttpConnector$HttpOverFlowException(oobject, "The Content-Length is too large:"+i5);
                            }
                         }else {
                            gZIPInputStr = i2;
                            inputStream = gZIPInputStr;
                         }
                         try{
                            if ((b7 = oobject.b) != null) {
                               b7.b(uob8b, 0);
                            }
                            if (uDataInputSt1 != null) {
                               try{
                                  uDataInputSt1.close();
                               }catch(java.lang.Exception e0){
                                  e0.printStackTrace();
                               }
                            }
                            if (inputStream != null) {
                               try{
                                  inputStream.close();
                               }catch(java.lang.Exception e0){
                                  e0.printStackTrace();
                               }
                            }
                            if (gZIPInputStr) {
                               try{
                                  gZIPInputStr.close();
                               }catch(java.lang.Exception e0){
                                  e0.printStackTrace();
                               }
                            }
                            try{
                               uByteArrayOu.close();
                            }catch(java.lang.Exception e0){
                               e0.printStackTrace();
                            }
                            httpsURLConn.disconnect();
                            return uob8b;
                         }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                         }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                         }catch(javax.net.ssl.SSLHandshakeException e0){
                         }catch(java.lang.OutOfMemoryError e0){
                            throwable = e0;
                            goto label_0327 ;
                         }
                      }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                      }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                      }catch(javax.net.ssl.SSLHandshakeException e0){
                      }catch(java.lang.OutOfMemoryError e0){
                      }
                   }catch(javax.net.ssl.SSLHandshakeException e0){
                   }catch(java.lang.OutOfMemoryError e0){
                   }catch( e0){
                   }
                }catch(java.lang.AssertionError e0){
                   throw new HttpConnector$NetWorkErrorException(oobject, e0.getMessage());
                }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                }catch(javax.net.ssl.SSLHandshakeException e0){
                }catch(java.lang.OutOfMemoryError e0){
                }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                }
             }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
             }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
             }catch(javax.net.ssl.SSLHandshakeException e0){
             }catch(java.lang.OutOfMemoryError e0){
             }
          }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
             b8b uob8b1 = e0;
             i3 = i2;
          }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
             uob8b1 = e0;
             i3 = i2;
          }catch(javax.net.ssl.SSLHandshakeException e0){
             throwable = e0;
             gZIPInputStr = i2;
             httpURLConne = gZIPInputStr;
             inputStream = httpURLConne;
          }catch(java.lang.OutOfMemoryError e0){
             throwable = e0;
             gZIPInputStr = i2;
             httpURLConne = gZIPInputStr;
             inputStream = httpURLConne;
             goto label_0327 ;
          }
       }
    }
    public final b8b b(v7b p0){
       HttpConnector b;
       int i2;
       int i3;
       HttpConnector b3;
       Throwable throwable;
       GZIPInputStream gZIPInputStr;
       HttpURLConnection httpURLConne;
       InputStream inputStream;
       HttpConnector b4;
       HttpHost httpHost;
       Uri uri1;
       doo uodoo;
       HttpsURLConnection httpsURLConn;
       HttpConnector b5;
       HttpConnector a;
       b8b uob8b2;
       DataInputStream uDataInputSt;
       int i8;
       HttpConnector b6;
       DataInputStream uDataInputSt1;
       HttpConnector b7;
       object oobject = this;
       object oobject1 = p0;
       int i = 0;
       int i1 = 1;
       String str = "http";
       String str1 = "too many redirect";
       String str2 = "responeCode:";
       String str3 = "post data: ";
       IpChange $ipChange = HttpConnector.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,oobject1};
          return $ipChange.ipc$dispatch("6921daf8", objArray);
       }else {
          Uri uri = p0.g();
          if ((b = oobject.b) != null) {
             b.d();
          }
          b8b uob8b = new b8b();
          int b1 = "https".equalsIgnoreCase(uri.getScheme());
          ByteArrayOutputStream uByteArrayOu = new ByteArrayOutputStream(128);
          try{
             i2 = 0;
             URL uRL = new URL(uri.toString());
             String host = uRL.getHost();
             if (b1) {
                String c = HttpConnector.c;
                v7t.i(c, "proxy or https");
                if ((httpHost = ssj.a(yaa.n)) != null) {
                   uri1 = uri;
                   uodoo = new doo(httpHost.getHostName(), httpHost.getPort(), i2, "taobao_hybrid_8.5.0");
                }else {
                   uri1 = uri;
                   v7t.a(c, "https:proxy: none");
                   uodoo = i2;
                }
                httpsURLConn = uRL.openConnection();
                if (uodoo != null) {
                   httpsURLConn.setSSLSocketFactory(uodoo);
                }
                StrictHostnameVerifier strictHostna = new StrictHostnameVerifier();
                try{
                   httpsURLConn.setHostnameVerifier(strictHostna);
                   httpsURLConn.setRequestProperty("Connection", "Keep-Alive");
                }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                   i3 = i2;
                }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                   i3 = i2;
                }catch(javax.net.ssl.SSLHandshakeException e0){
                   throwable = e0;
                   gZIPInputStr = i2;
                   inputStream = gZIPInputStr;
                }catch(java.lang.OutOfMemoryError e0){
                   throwable = e0;
                   gZIPInputStr = i2;
                   inputStream = gZIPInputStr;
                }
             label_0327 :
                if ((b3 = oobject.b) != null) {
                   b3.a(-5, "out of memory error");
                }
                throwable.printStackTrace();
                uByteArrayOu.reset();
                if (i2) {
                   try{
                      i2.close();
                   }catch(java.lang.Exception e0){
                      e0.printStackTrace();
                   }
                }
                if (inputStream) {
                   try{
                      inputStream.close();
                   }catch(java.lang.Exception e0){
                      e0.printStackTrace();
                   }
                }
                if (gZIPInputStr) {
                   try{
                      gZIPInputStr.close();
                   }catch(java.lang.Exception e0){
                      e0.printStackTrace();
                   }
                }
                try{
                   uByteArrayOu.close();
                }catch(java.lang.Exception e0){
                   e0.printStackTrace();
                }
                if (httpURLConne) {
                   httpURLConne.disconnect();
                }
                if ((b4 = oobject.b) != null) {
                   b4.b(new b8b(), 0);
                }
                return new b8b();
             }else {
                uri1 = uri;
                httpsURLConn = uRL.openConnection();
             }
             try{
                oobject.c(httpsURLConn, oobject1);
                if ((b5 = oobject.b) != null) {
                   b5.c(0);
                }
                try{
                   if ("post".equalsIgnoreCase(p0.c())) {
                      v7t.a(HttpConnector.c, str3.concat(new String(p0.d())));
                      httpsURLConn.setDoOutput(true);
                      httpsURLConn.setDoInput(true);
                      httpsURLConn.setRequestMethod("POST");
                      httpsURLConn.connect();
                      OutputStream outputStream = httpsURLConn.getOutputStream();
                      outputStream.write(p0.d());
                      outputStream.flush();
                      outputStream.close();
                   }else {
                      httpsURLConn.connect();
                   }
                   try{
                      i1 = httpsURLConn.getResponseCode();
                      v7t.a(HttpConnector.c, str2.append(i1).toString());
                      HttpConnector b2 = 300;
                      if (i1 >= b2 && (i1 < 400 && (i1 != 304 && p0.h()))) {
                         if ((a = oobject.a) <= 5) {
                            int i4 = a + 1;
                            oobject.a = i4;
                            if ((str3 = httpsURLConn.getHeaderField("location")) != null) {
                               if (!str3.toLowerCase().startsWith(str)) {
                                  str3 = new URL(str, host, str3).toString();
                               }
                               if (i1 != 305) {
                                  oobject1.k(Uri.parse(str3));
                                  uob8b2 = this.a(p0);
                                  try{
                                     uByteArrayOu.close();
                                  }catch(java.lang.Exception e0){
                                     e0.printStackTrace();
                                  }
                                  httpsURLConn.disconnect();
                                  return uob8b2;
                               }else {
                                  uob8b2 = oobject.a(new v7b(str3));
                                  try{
                                     uByteArrayOu.close();
                                  }catch(java.lang.Exception e0){
                                     e0.printStackTrace();
                                  }
                                  httpsURLConn.disconnect();
                                  return uob8b2;
                               }
                            }
                         }else {
                            throw new HttpConnector$RedirectException(oobject, str1);
                         }
                      }
                      uob8b.h(i1);
                      int i5 = 1;
                      while ((str = httpsURLConn.getHeaderFieldKey(i5)) != null) {
                         i5 = i5 + 1;
                         String headerField = httpsURLConn.getHeaderField(str);
                         uob8b.a(str, headerField);
                         if ("Set-Cookie".equals(str)) {
                            zpw.c(uri1.toString(), headerField);
                         }
                      }
                      try{
                         if (i1 >= 200 && i1 < b2) {
                            if ((i5 = httpsURLConn.getContentLength()) <= 0x6400000) {
                               inputStream = httpsURLConn.getInputStream();
                               try{
                                  if ((str = httpsURLConn.getContentEncoding()) != null && "gzip".equals(str)) {
                                     try{
                                        GZIPInputStream str4 = new GZIPInputStream(inputStream);
                                        i2 = str4;
                                        uDataInputSt = new DataInputStream(str4);
                                     }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                                        i5 = uDataInputSt;
                                     }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                                        i5 = uDataInputSt;
                                     }catch(javax.net.ssl.SSLHandshakeException e0){
                                        gZIPInputStr = uDataInputSt;
                                     }catch(java.lang.OutOfMemoryError e0){
                                        gZIPInputStr = uDataInputSt;
                                     }
                                  }else {
                                     uDataInputSt = new DataInputStream(inputStream);
                                  }
                                  int i6 = 2048;
                                  try{
                                     byte[] uobyteArray = new byte[i6];
                                     int i7 = 0;
                                     while (true) {
                                        b1 = 0;
                                        if ((i8 = uDataInputSt.read(uobyteArray, b1, i6)) != -1) {
                                           uByteArrayOu.write(uobyteArray, b1, i8);
                                           if ((b6 = oobject.b) != null) {
                                              if ((i7 = i7 + i8) > i5) {
                                                 i5 = i7;
                                              }
                                              float f = (float)i7 / (float)i5;
                                              f = f * 100.00f;
                                              b6.c((int)f);
                                           }
                                        }else {
                                           break ;
                                        }
                                     }
                                     uob8b.f(uByteArrayOu.toByteArray());
                                     gZIPInputStr = i2;
                                     uDataInputSt1 = uDataInputSt;
                                  }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                                     i5 = i2;
                                  }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                                     i5 = i2;
                                  }catch(javax.net.ssl.SSLHandshakeException e0){
                                     gZIPInputStr = i2;
                                     InputStream inputStream1 = uDataInputSt;
                                  }catch(java.lang.OutOfMemoryError e0){
                                     gZIPInputStr = i2;
                                     inputStream1 = uDataInputSt;
                                  }
                               }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                                  i3 = i2;
                               }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                                  i3 = i2;
                               }catch(javax.net.ssl.SSLHandshakeException e0){
                                  throwable = e0;
                                  gZIPInputStr = i2;
                               }catch(java.lang.OutOfMemoryError e0){
                                  throwable = e0;
                                  gZIPInputStr = i2;
                                  goto label_0327 ;
                               }
                            }else {
                               throw new HttpConnector$HttpOverFlowException(oobject, "The Content-Length is too large:"+i5);
                            }
                         }else {
                            gZIPInputStr = i2;
                            inputStream = gZIPInputStr;
                         }
                         try{
                            if ((b7 = oobject.b) != null) {
                               b7.b(uob8b, 0);
                            }
                            if (uDataInputSt1 != null) {
                               try{
                                  uDataInputSt1.close();
                               }catch(java.lang.Exception e0){
                                  e0.printStackTrace();
                               }
                            }
                            if (inputStream != null) {
                               try{
                                  inputStream.close();
                               }catch(java.lang.Exception e0){
                                  e0.printStackTrace();
                               }
                            }
                            if (gZIPInputStr) {
                               try{
                                  gZIPInputStr.close();
                               }catch(java.lang.Exception e0){
                                  e0.printStackTrace();
                               }
                            }
                            try{
                               uByteArrayOu.close();
                            }catch(java.lang.Exception e0){
                               e0.printStackTrace();
                            }
                            httpsURLConn.disconnect();
                            return uob8b;
                         }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                         }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                         }catch(javax.net.ssl.SSLHandshakeException e0){
                         }catch(java.lang.OutOfMemoryError e0){
                            throwable = e0;
                            goto label_0327 ;
                         }
                      }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                      }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                      }catch(javax.net.ssl.SSLHandshakeException e0){
                      }catch(java.lang.OutOfMemoryError e0){
                      }
                   }catch(javax.net.ssl.SSLHandshakeException e0){
                   }catch(java.lang.OutOfMemoryError e0){
                   }catch( e0){
                   }
                }catch(java.lang.AssertionError e0){
                   throw new HttpConnector$NetWorkErrorException(oobject, e0.getMessage());
                }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
                }catch(javax.net.ssl.SSLHandshakeException e0){
                }catch(java.lang.OutOfMemoryError e0){
                }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
                }
             }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
             }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
             }catch(javax.net.ssl.SSLHandshakeException e0){
             }catch(java.lang.OutOfMemoryError e0){
             }
          }catch(android.taobao.windvane.connect.HttpConnector$RedirectException e0){
             b8b uob8b1 = e0;
             i3 = i2;
          }catch(android.taobao.windvane.connect.HttpConnector$HttpOverFlowException e0){
             uob8b1 = e0;
             i3 = i2;
          }catch(javax.net.ssl.SSLHandshakeException e0){
             throwable = e0;
             gZIPInputStr = i2;
             httpURLConne = gZIPInputStr;
             inputStream = httpURLConne;
          }catch(java.lang.OutOfMemoryError e0){
             throwable = e0;
             gZIPInputStr = i2;
             httpURLConne = gZIPInputStr;
             inputStream = httpURLConne;
             goto label_0327 ;
          }
       }
    }
    public final void c(HttpURLConnection p0,v7b p1){
       String str;
       Map map;
       int i = 1;
       IpChange $ipChange = HttpConnector.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("d10ba028", objArray);
          return;
       }else {
          int i1 = p1.f() + i;
          p0.setConnectTimeout((p1.a() * i1));
          p0.setReadTimeout((p1.e() * i1));
          p0.setInstanceFollowRedirects(0);
          p0.setRequestProperty("Host", p1.g().getHost());
          p0.setRequestProperty("Connection", "close");
          p0.setRequestProperty("Accept-Encoding", "gzip");
          if ((str = zpw.a(p0.getURL().toString())) != null) {
             p0.setRequestProperty("Cookie", str);
          }
          if ((map = p1.b()) != null) {
             Iterator iterator = map.entrySet().iterator();
             while (iterator.hasNext()) {
                Map$Entry uEntry = iterator.next();
                String key = uEntry.getKey();
                p0.setRequestProperty(key, uEntry.getValue());
             }
          }
          p0.setUseCaches(0);
          return;
       }
    }
    public b8b d(v7b p0){
       IpChange $ipChange = HttpConnector.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.e(p0, null);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("7ed55206", objArray);
    }
    public b8b e(v7b p0,h7b p1){
       int i1;
       int i2;
       long l;
       b8b uob8b;
       IpChange $ipChange = HttpConnector.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("bee9762c", objArray);
       }else if(p0 != null){
          this.b = p1;
          this.a = 0;
          int i = p0.f();
          String str = null;
          if (0 < i) {
             return this.a(p0);
          }
       }else {
          throw new NullPointerException("Http connect error, request is null");
       }
    }
    public b8b f(v7b p0,h7b p1){
       int i1;
       int i2;
       long l;
       b8b uob8b;
       IpChange $ipChange = HttpConnector.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("dd1657e9", objArray);
       }else if(p0 != null){
          this.b = p1;
          this.a = 0;
          int i = p0.f();
          String str = null;
          if (0 < i) {
             return this.b(p0);
          }
       }else {
          throw new NullPointerException("Http connect error, request is null");
       }
    }
}
