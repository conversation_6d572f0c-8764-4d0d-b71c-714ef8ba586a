package android.taobao.windvane.extra.launch.WindVaneIdleTask;
import android.taobao.windvane.extra.launch.InitOnceTask;
import tb.t2o;
import android.app.Application;
import java.util.HashMap;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import java.io.File;
import java.lang.Boolean;
import com.uc.webview.export.extension.IRunningCoreInfo;
import com.uc.webview.export.extension.IRunningCoreInfo$Instance;
import tb.v7t;
import com.uc.webview.base.io.PathUtils;
import java.lang.StringBuilder;
import tb.ck7;
import android.taobao.windvane.extra.launch.WindVaneIdleTask$3;
import android.content.Context;
import tb.bk7;
import tb.wae;
import tb.mrt;
import android.taobao.windvane.extra.launch.WindVaneIdleTask$2;
import java.lang.Runnable;
import java.util.concurrent.ScheduledFuture;
import tb.vpw;
import tb.wpw;
import tb.om2;
import java.lang.Class;
import java.lang.reflect.Method;
import java.lang.reflect.AccessibleObject;
import java.lang.Throwable;
import com.android.alibaba.ip.runtime.InstantReloadException;
import tb.rsa;
import android.os.Handler;
import android.taobao.windvane.extra.launch.WindVaneIdleTask$4;
import android.taobao.windvane.extra.jsbridge.JSAPIManager;
import android.taobao.windvane.export.prerender.TMSPrerenderService;
import tb.jca;
import android.taobao.windvane.extra.uc.WVCoreSettings;
import android.taobao.windvane.extra.launch.WindVaneIdleTask$1;
import tb.yt4;

public class WindVaneIdleTask extends InitOnceTask	// class@0001bf from classes.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x3d8000f0);
    }
    public void WindVaneIdleTask(){
       super();
    }
    public static void access$000(WindVaneIdleTask p0,Application p1,HashMap p2){
       IpChange $ipChange = WindVaneIdleTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2};
          $ipChange.ipc$dispatch("74b2c782", objArray);
          return;
       }else {
          p0.doDexOptimizeAsync(p1, p2);
          return;
       }
    }
    public static void access$100(WindVaneIdleTask p0,Application p1){
       IpChange $ipChange = WindVaneIdleTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("397787d2", objArray);
          return;
       }else {
          p0.doDexOptimizationImpl(p1);
          return;
       }
    }
    public static void access$200(WindVaneIdleTask p0,File p1,boolean p2){
       IpChange $ipChange = WindVaneIdleTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Boolean(p2)};
          $ipChange.ipc$dispatch("1e144b71", objArray);
          return;
       }else {
          p0.onDexOptimizeFinish(p1, p2);
          return;
       }
    }
    private void doDexOptimizationImpl(Application p0){
       IpChange $ipChange = WindVaneIdleTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f9211dff", objArray);
          return;
       }else {
          IRunningCoreInfo iRunningCore = IRunningCoreInfo$Instance.get();
          String str = "DexOptimizer";
          if (iRunningCore == null) {
             v7t.d(str, "runningInfo is null");
             return;
          }else {
             File fileCoreDex = PathUtils.getFileCoreDex(new File(iRunningCore.path()));
             v7t.i(str, "optimize file: "+fileCoreDex.getAbsolutePath());
             ck7.b().c(p0, fileCoreDex, new WindVaneIdleTask$3(this));
             return;
          }
       }
    }
    private void doDexOptimizeAsync(Application p0,HashMap p1){
       IpChange $ipChange = WindVaneIdleTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("8c8c61ee", objArray);
          return;
       }else {
          mrt.b().a(new WindVaneIdleTask$2(this, p0), 3000);
          return;
       }
    }
    private void initBrowserIdleTask(Application p0,HashMap p1){
       int i = 0;
       int i1 = 2;
       if (vpw.commonConfig.g3 != null) {
          v7t.i("WindVaneIdleTask", "initBrowserIdleTask v2 started");
          om2.c(p0, p1);
       }else {
          v7t.i("WindVaneIdleTask", "initBrowserIdleTask v1 started");
          Class[] uClassArray = new Class[i1];
          uClassArray[i] = Application.class;
          uClassArray[1] = HashMap.class;
          Method declaredMeth = om2.class.getDeclaredMethod("initImpl", uClassArray);
          declaredMeth.setAccessible(1);
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          objArray[1] = p1;
          declaredMeth.invoke(null, objArray);
       }
       return;
    }
    public static Object ipc$super(WindVaneIdleTask p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/launch/WindVaneIdleTask");
    }
    private void onDexOptimizeFinish(File p0,boolean p1){
       Handler handler;
       IpChange $ipChange = WindVaneIdleTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("a9bfca2e", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if((handler = rsa.b().a()) != null){
          handler.postDelayed(new WindVaneIdleTask$4(this, p0, p1), 3000);
       }
       return;
    }
    public String getName(){
       IpChange $ipChange = WindVaneIdleTask.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "WindVaneIdleTask";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7c09e698", objArray);
    }
    public void initImpl(Application p0,HashMap p1){
       String str = "WindVaneIdleTask";
       IpChange $ipChange = WindVaneIdleTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("73f761cb", objArray);
          return;
       }else {
          v7t.i(str, "init started");
          JSAPIManager.getInstance().register();
          TMSPrerenderService.INSTANCE.e();
          jca.b();
          if (p0 != null && vpw.commonConfig.a3 != null) {
             WVCoreSettings.getInstance().setCoreEventCallback2(new WindVaneIdleTask$1(this, p0, p1));
          }else {
             v7t.i(str, "doDexOptimizeAsync not enabled");
          }
          this.initBrowserIdleTask(p0, p1);
          v7t.i(str, "init finished");
          return;
       }
    }
}
