package android.taobao.windvane.extra.jsbridge.JSAPIManager;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.os.SystemClock;
import com.taobao.tao.alipay.export.PayPasswrdValidateBridge;
import java.lang.Class;
import tb.fsw;
import tb.ivf;
import com.taobao.android.riverlogger.RVLLevel;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.lcn;
import com.alibaba.triver.cannal_engine.scene.TRWidgetJsPlugin;
import android.taobao.windvane.jsbridge.api.WVDevelopTool;
import tb.icn;
import java.lang.Long;

public class JSAPIManager	// class@000199 from classes.dex
{
    public static IpChange $ipChange;
    private static final JSAPIManager INSTANCE;
    private static boolean sInitialized;

    static {
       t2o.a(0x3d8000ce);
       JSAPIManager.INSTANCE = new JSAPIManager();
       JSAPIManager.sInitialized = false;
    }
    private void JSAPIManager(){
       super();
    }
    public static JSAPIManager getInstance(){
       IpChange $ipChange = JSAPIManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return JSAPIManager.INSTANCE;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("29837bcd", objArray);
    }
    public void register(){
       int i = 1;
       IpChange $ipChange = JSAPIManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("9a23a9aa", objArray);
          return;
       }else {
          _monitor_enter(JSAPIManager.class);
          if (JSAPIManager.sInitialized) {
             _monitor_exit(JSAPIManager.class);
             return;
          }else {
             JSAPIManager.sInitialized = i;
             long l = SystemClock.uptimeMillis();
             String str = "TBWVPayPasswrdValidateHandler";
             try{
                fsw.i(str, PayPasswrdValidateBridge.class, i);
                ivf.a();
             }catch(java.lang.Exception e0){
                lcn.f(RVLLevel.Error, "WindVane/JSBridge", "registerPlugin error: "+e0.getMessage());
             }
             fsw.h("triver-widget", TRWidgetJsPlugin.class);
             fsw.h("WVDevelopTool", WVDevelopTool.class);
             icn oicn = lcn.a(RVLLevel.Info, "WindVane/JSBridge").j("register").a("cost", Long.valueOf((SystemClock.uptimeMillis() - l)));
             oicn.f();
             _monitor_exit(JSAPIManager.class);
             return;
          }
       }
    }
}
