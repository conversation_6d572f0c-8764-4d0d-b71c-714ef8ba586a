package android.taobao.windvane.extra.uc.AliNetworkAdapter$1;
import anetwork.channel.IBodyHandler;
import android.taobao.windvane.extra.uc.AliNetworkAdapter;
import java.util.Map;
import java.lang.Object;
import java.util.ArrayList;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.v7t;
import java.lang.StringBuilder;
import android.net.Uri;
import android.content.Context;
import android.content.ContentResolver;
import java.io.InputStream;
import java.util.List;
import java.io.FileInputStream;
import java.io.ByteArrayInputStream;
import java.lang.Boolean;
import java.util.Iterator;
import java.lang.Throwable;
import java.lang.Number;
import java.lang.System;

public class AliNetworkAdapter$1 implements IBodyHandler	// class@0001fa from classes.dex
{
    public byte[] buffer;
    public int curFilenum;
    public byte[] dataValue;
    public String fileNameValue;
    public boolean hasInitilized;
    public List instream;
    public boolean isCompleted;
    public int postedLen;
    public final AliNetworkAdapter this$0;
    public final Map val$dataMap;
    public final Map val$fileMap;
    public final int val$totalFileNum;
    public static IpChange $ipChange;

    public void AliNetworkAdapter$1(AliNetworkAdapter p0,int p1,Map p2,Map p3){
       this.this$0 = p0;
       this.val$totalFileNum = p1;
       this.val$fileMap = p2;
       this.val$dataMap = p3;
       super();
       this.isCompleted = false;
       this.postedLen = 0;
       this.curFilenum = 0;
       this.fileNameValue = null;
       this.dataValue = null;
       byte[] uobyteArray = new byte[AliNetworkAdapter.access$500(p0)];
       this.buffer = uobyteArray;
       this.instream = new ArrayList(p1);
       this.hasInitilized = false;
    }
    public void initStream(){
       AliNetworkAdapter$1 tcurFilenum;
       int i = 1;
       IpChange $ipChange = AliNetworkAdapter$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("ef4b4137", objArray);
          return;
       }else {
          try{
             this.curFilenum = 0;
             while ((tcurFilenum = this.curFilenum) < this.val$totalFileNum) {
                this.fileNameValue = this.val$fileMap.get(String.valueOf(tcurFilenum));
                this.dataValue = this.val$dataMap.get(String.valueOf(this.curFilenum));
                if (v7t.h() && this.dataValue != null) {
                   AliNetworkAdapter$1 tdataValue = this.dataValue;
                   v7t.a("AliNetwork", "".append("len =").append(this.dataValue.length).append(",datavalue=").append(new String(tdataValue, 0, tdataValue.length)).toString());
                }
                if ((tcurFilenum = this.fileNameValue) != null) {
                   if (tcurFilenum.toLowerCase().startsWith("content://")) {
                      this.instream.add(this.curFilenum, AliNetworkAdapter.access$600(this.this$0).getContentResolver().openInputStream(Uri.parse(this.fileNameValue)));
                   }else {
                      this.instream.add(this.curFilenum, new FileInputStream(this.fileNameValue));
                   }
                }else {
                   this.instream.add(this.curFilenum, new ByteArrayInputStream(this.dataValue));
                }
                int i1 = this.curFilenum + i;
                this.curFilenum = i1;
             }
             return;
          }catch(java.lang.Exception e0){
          }
       }
    }
    public boolean isCompleted(){
       AliNetworkAdapter$1 tisCompleted;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = AliNetworkAdapter$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("8c6bb44c", objArray).booleanValue();
       }else if((tisCompleted = this.isCompleted) != null){
          this.isCompleted = i;
          this.hasInitilized = i;
          try{
             Iterator iterator = this.instream.iterator();
             while (iterator.hasNext()) {
                iterator.next().close();
             }
             this.instream.clear();
          }catch(java.io.IOException e0){
             e0.printStackTrace();
          }
          return i1;
       }else {
          return tisCompleted;
       }
    }
    public int read(byte[] p0){
       AliNetworkAdapter$1 tinstream;
       int len;
       int i;
       IpChange $ipChange = AliNetworkAdapter$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("9ed24497", objArray).intValue();
       }else if(this.hasInitilized == null){
          this.initStream();
          if ((tinstream = this.instream) != null && tinstream.size()) {
             this.hasInitilized = true;
          }else {
             this.isCompleted = true;
             return 0;
          }
       }
       Iterator iterator = this.instream.iterator();
       while (true) {
          if (iterator.hasNext()) {
             InputStream inputStream = iterator.next();
             try{
                len = (AliNetworkAdapter.access$500(this.this$0) > p0.length)? p0.length: AliNetworkAdapter.access$500(this.this$0);
                if ((i = inputStream.read(this.buffer, 0, len)) != -1) {
                   break ;
                }
             }catch(java.lang.Exception e8){
                v7t.i("AliNetwork", "read exception"+e8.getMessage());
                this.isCompleted = true;
                return 0;
             }
          }else {
             v7t.i("AliNetwork", "read finish");
             this.isCompleted = true;
             return 0;
          }
       }
       System.arraycopy(this.buffer, 0, p0, 0, i);
       this.postedLen = this.postedLen + i;
       v7t.i("AliNetwork", "read len="+i);
       return i;
    }
}
