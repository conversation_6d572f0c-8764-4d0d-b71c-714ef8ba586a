package android.taobao.windvane.extra.uc.interfaces.IRequestTiming;
import tb.c9v;
import anetwork.channel.statist.StatisticData;

public interface abstract IRequestTiming	// class@000275 from classes.dex
{
    public static final String REQUEST_TIMING_KEY = "Landroidx/compose/foundation/text/LongPressTextDragObserverKt;";

    long getH5RequestFetchStartTime();
    long getH5RequestResponseEndTime();
    long getH5RequestResponseStartTime();
    long getH5RequestSendStartTime();
    long getNativeDelegateBeforeSendRequestEnd();
    long getNativeDelegateBeforeSendRequestStart();
    long getNativeRequestEndTime();
    long getNativeRequestFirstDataTime();
    long getNativeRequestInitTime();
    long getNativeRequestResponseTime();
    long getNativeRequestSendTime();
    long getNativeRequestStartTime();
    long getNativeShouldInterceptEnd();
    long getNativeShouldInterceptStart();
    boolean getRequestIntercepted();
    c9v getSSRRequestStatics();
    StatisticData getTNetRequestStatics();
    void markNativeDelegateBeforeSendRequestEnd();
    void markNativeDelegateBeforeSendRequestStart();
    void markNativeRequestEndTime();
    void markNativeRequestFirstDataTime();
    void markNativeRequestInitTime();
    void markNativeRequestResponseTime();
    void markNativeRequestSendTime();
    void markNativeRequestStartTime();
    void markNativeShouldInterceptEnd();
    void markNativeShouldInterceptStart();
    void setH5RequestFetchStartTime(long p0);
    void setH5RequestResponseEndTime(long p0);
    void setH5RequestResponseStartTime(long p0);
    void setH5RequestSendStartTime(long p0);
    void setRequestIntercepted(boolean p0);
    void setSSRRequestStatics(c9v p0);
    void setTNetRequestStatics(StatisticData p0);
}
