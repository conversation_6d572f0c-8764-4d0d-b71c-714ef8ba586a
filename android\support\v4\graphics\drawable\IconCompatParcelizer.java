package android.support.v4.graphics.drawable.IconCompatParcelizer;
import androidx.core.graphics.drawable.IconCompatParcelizer;
import androidx.versionedparcelable.VersionedParcel;
import androidx.core.graphics.drawable.IconCompat;

public final class IconCompatParcelizer extends IconCompatParcelizer	// class@00012d from classes.dex
{

    public void IconCompatParcelizer(){
       super();
    }
    public static IconCompat read(VersionedParcel p0){
       return IconCompatParcelizer.read(p0);
    }
    public static void write(IconCompat p0,VersionedParcel p1){
       IconCompatParcelizer.write(p0, p1);
    }
}
