package android.taobao.windvane.extra.uc.WVUCCoreProvider;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.uc.WVCoreSettings;
import tb.yt4;
import android.content.Context;
import android.taobao.windvane.extra.uc.WVUCCoreProvider$WVUCCoreProviderCallback;
import tb.au4;
import android.taobao.windvane.extra.uc.WVUCCoreProvider$1;
import android.taobao.windvane.extra.core.WVCore;

public class WVUCCoreProvider	// class@00023a from classes.dex
{
    private yt4 coreCallback;
    private boolean hasCalled;
    public static IpChange $ipChange;
    public static final int UC_CORE_FAILED_HAS_CALLED;

    static {
       t2o.a(0x3d80016d);
    }
    public void WVUCCoreProvider(){
       super();
       this.hasCalled = false;
    }
    public void release(){
       IpChange $ipChange = WVUCCoreProvider.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("ca5510e", objArray);
          return;
       }else if(this.coreCallback != null){
          WVCoreSettings.getInstance().removeEventCallback2(this.coreCallback);
          this.coreCallback = null;
       }
       return;
    }
    public void request(Context p0,WVUCCoreProvider$WVUCCoreProviderCallback p1){
       int i = 1;
       IpChange $ipChange = WVUCCoreProvider.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("be30e7f6", objArray);
          return;
       }else if(p0 == null){
          if (p1 != null) {
             p1.onUCCoreFailed(au4.b(i, "context is null"));
          }
          return;
       }else if(this.hasCalled != null){
          if (p1 != null) {
             p1.onUCCoreFailed(au4.b(1001, "already has called"));
          }
          return;
       }else if(this.coreCallback == null){
          this.coreCallback = new WVUCCoreProvider$1(this, p1);
       }
       this.hasCalled = i;
       WVCore.getInstance().initUCCore2(p0, this.coreCallback);
       return;
    }
}
