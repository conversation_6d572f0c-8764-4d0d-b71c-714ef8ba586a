package android.taobao.windvane.extra.uc.AliRequestAdapter;
import android.taobao.windvane.extra.uc.interfaces.IRequest;
import tb.t2o;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import java.lang.String;
import java.util.Map;
import android.content.Context;
import java.lang.Object;
import android.taobao.windvane.extra.uc.ExtImgDecoder;
import anetwork.channel.Request;
import android.app.Application;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import tb.vpw;
import tb.wpw;
import java.lang.CharSequence;
import anetwork.channel.entity.RequestImpl;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.v7t;
import java.lang.Long;
import java.lang.Integer;
import tb.x74;
import tb.yvj;
import com.taobao.tao.util.ImageStrategyDecider;
import android.text.TextUtils;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import tb.csw;
import tb.trw;
import java.lang.System;
import android.net.Uri;
import java.util.concurrent.Future;
import anetwork.channel.Response;
import java.util.List;
import android.taobao.windvane.extra.uc.UCNetworkDelegate;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import android.taobao.windvane.extra.uc.interfaces.IRequestTiming;
import java.lang.Number;

public class AliRequestAdapter implements IRequest	// class@00020a from classes.dex
{
    public String TAG;
    private String bizCode;
    public String cancelPhase;
    private final ExtImgDecoder extImgDecoder;
    public Request mAliRequest;
    private final Object mClientResource;
    private Context mContext;
    private String mCurId;
    private EventHandler mEventHandler;
    public Future mFutureResponse;
    private Map mHeaders;
    private boolean mIsUCProxy;
    private boolean mIsUseWebP;
    private int mLoadType;
    private String mMethod;
    private String mParentId;
    private IRequestTiming mRequestTiming;
    private int mRequestType;
    private Map mUCHeaders;
    private Map mUploadDataMap;
    private Map mUploadFileMap;
    private long mUploadFileTotalLen;
    private String mUrl;
    public String originalUrl;
    public static IpChange $ipChange;
    public static final String PHASE_ENDDATA;
    private static final String PHASE_NORMAL;
    public static final String PHASE_RELOAD;
    public static final String PHASE_STOP;
    public static int connectTimeout;
    public static int readTimeout;
    public static int retryTimes;

    static {
       t2o.a(0x3d80013f);
       t2o.a(0x3d8001a9);
       AliRequestAdapter.retryTimes = 1;
       AliRequestAdapter.connectTimeout = 0x2710;
       AliRequestAdapter.readTimeout = 0x2710;
    }
    public void AliRequestAdapter(EventHandler p0,String p1,String p2,boolean p3,Map p4,Map p5,Map p6,Map p7,long p8,int p9,int p10,boolean p11,String p12,Context p13){
       int i = this;
       super();
       i.TAG = "alinetwork";
       i.mMethod = "GET";
       i.mCurId = "";
       i.mParentId = "";
       i.mClientResource = new Object();
       i.extImgDecoder = ExtImgDecoder.getInstance();
       i.cancelPhase = "normal";
       i.mIsUseWebP = p11;
       i.mEventHandler = p0;
       i.mUrl = p1;
       i.mMethod = p2;
       i.mIsUCProxy = p3;
       i.mHeaders = p4;
       i.mUCHeaders = p5;
       i.mUploadFileMap = p6;
       i.mUploadDataMap = p7;
       i.mUploadFileTotalLen = p8;
       i.mRequestType = p9;
       i.mLoadType = p10;
       i.bizCode = p12;
       i.mAliRequest = this.formatAliRequest();
       Context uContext = p13;
       if (!uContext instanceof Application) {
          uContext = p13.getApplicationContext();
       }
       i.mContext = uContext;
       return;
    }
    private boolean bizCodeLimit(String p0){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("b4d8be50", objArray).booleanValue();
       }else if(vpw.commonConfig.R0 != null){
          return 1;
       }else if(p0.contains("-yinhe.")){
          return 1;
       }else {
          return 0;
       }
    }
    private Request createRequest(String p0,String p1){
       int i = 0;
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("6ce155d6", objArray);
       }else {
          try{
             RequestImpl requestImpl = new RequestImpl(p0);
             requestImpl.setFollowRedirects(i);
             requestImpl.setBizId(this.bizCode);
             requestImpl.setRetryTime(AliRequestAdapter.retryTimes);
             requestImpl.setConnectTimeout(AliRequestAdapter.connectTimeout);
             requestImpl.setReadTimeout(AliRequestAdapter.readTimeout);
             requestImpl.setCookieEnabled(WVUCWebView.isNeedCookie(p0));
             requestImpl.setMethod(p1);
             return requestImpl;
          }catch(java.lang.Exception e5){
             v7t.d(this.TAG, " AliRequestAdapter formatAliRequest Exception"+e5.getMessage());
             return null;
          }
       }
    }
    private Request formatAliRequest(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.formatAliRequest(this.mUrl, this.mMethod, this.mIsUCProxy, this.mHeaders, this.mUCHeaders, this.mUploadFileMap, this.mUploadDataMap, this.mUploadFileTotalLen, this.mRequestType, this.mLoadType, this.mIsUseWebP);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("4b92ef15", objArray);
    }
    private Request formatAliRequest(String p0,String p1,boolean p2,Map p3,Map p4,Map p5,Map p6,long p7,int p8,int p9,boolean p10){
       object oobject4;
       Request request;
       object oobject = this;
       object oobject1 = p0;
       object oobject2 = p1;
       object oobject3 = p3;
       int b = p10;
       int i = 5;
       int i1 = 1;
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[12];
          objArray[0] = oobject;
          objArray[i1] = oobject1;
          objArray[2] = oobject2;
          objArray[3] = new Boolean(p2);
          objArray[4] = oobject3;
          objArray[i] = p4;
          objArray[6] = p5;
          objArray[7] = p6;
          objArray[8] = new Long(p7);
          objArray[9] = new Integer(p8);
          objArray[10] = new Integer(p9);
          objArray[11] = new Boolean(b);
          return $ipChange.ipc$dispatch("c74a03a3", objArray);
       }else {
          oobject.originalUrl = null;
          boolean b1 = oobject.extImgDecoder.canExtImgDecoder();
          if (b && x74.m(p0)) {
             if (yvj.a(p3)) {
                v7t.i(oobject.TAG, p0+" is main document request, skip request modification");
             }else if(b1 && (oobject.extImgDecoder.isExchangeImgUrlEnable() && this.bizCodeLimit(p0))){
                oobject4 = 1;
             }else {
                oobject4 = 0;
             }
             if (oobject4 && vpw.commonConfig.t3 != null) {
                if (ExtImgDecoder.getDecodeErrorCount() > i) {
                   i1 = 0;
                }
                b = i1;
             }
             if (b) {
                oobject4 = (vpw.commonConfig.S0 != null)? ImageStrategyDecider.convergeAndHeif(p0): ImageStrategyDecider.decideUrl(p0, Integer.valueOf(-1), Integer.valueOf(-1), null);
             }else {
                oobject4 = ImageStrategyDecider.justConvergeAndWebP(p0);
             }
             if (!TextUtils.isEmpty(oobject4) && !oobject4.equals(p0)) {
                lcn.a(RVLLevel.Info, "WindVane/Image").j("changeImageUrl").a("originalUrl", p0).a("decideUrl", oobject4).f();
                oobject.originalUrl = oobject1;
             label_0107 :
                if ((request = this.createRequest(oobject4, oobject2, oobject3)) == null) {
                   return null;
                }else if(trw.getPerformanceMonitor() != null){
                   trw.getPerformanceMonitor().didResourceStartLoadAtTime(oobject.mUrl, System.currentTimeMillis());
                }
                return request;
             }
          }
          oobject4 = oobject1;
          goto label_0107 ;
       }
    }
    private static boolean shouldModifyAcceptHeader(String p0){
       Uri uri;
       int i = 1;
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("f122c3b7", objArray).booleanValue();
       }else if(p0 == null){
          return 0;
       }else if(p0.contains("/O1CN")){
          return i;
       }else if((uri = Uri.parse(p0)) != null){
          uri = uri.getHost();
          if (TextUtils.equals("gw.alicdn.com", uri) || TextUtils.equals("img.alicdn.com", uri)) {
             return i;
          }
       }
       return e0;
    }
    public void cancel(){
       AliRequestAdapter tmFutureResp;
       int i = 1;
       String str = "AliRequestAdapter cancel desc url=";
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("707fe601", objArray);
          return;
       }else if(WVUCWebView.isStop){
          this.cancelPhase = "stop";
       }
       AliRequestAdapter tTAG = this.TAG;
       String str1 = "cancel id= "+this.mEventHandler.hashCode()+", phase:["+this.cancelPhase+"]";
       try{
          v7t.d(tTAG, str1);
          if (v7t.h() && ((tTAG = this.mFutureResponse) != null && tTAG.get() != null)) {
             v7t.a(this.TAG, str+this.mFutureResponse.get().getDesc());
          label_0079 :
             this.complete();
          }else {
             goto label_0079 ;
          }
       }catch(java.lang.InterruptedException e2){
          e2.printStackTrace();
          v7t.a(this.TAG, "AliRequestAdapter cancel ="+e2.getMessage());
       }catch(java.util.concurrent.ExecutionException e2){
          e2.printStackTrace();
          v7t.a(this.TAG, "AliRequestAdapter cancel ="+e2.getMessage());
       }
       if ((tmFutureResp = this.mFutureResponse) != null) {
          tmFutureResp.cancel(i);
       }
       return;
    }
    public void complete(){
       int i = 0;
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("e77d7140", objArray);
          return;
       }else {
          WVUCWebView.isStop = i;
          if (this.mEventHandler.isSynchronous()) {
             AliRequestAdapter tmClientReso = this.mClientResource;
             _monitor_enter(tmClientReso);
             v7t.a(this.TAG, "AliRequestAdapter complete will notify");
             this.mClientResource.notifyAll();
             _monitor_exit(tmClientReso);
          }
          return;
       }
    }
    public Request createRequest(String p0,String p1,List p2){
       Request request;
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("8f97e605", objArray);
       }else if((request = this.createRequest(p0, p1)) == null){
          return null;
       }else if(p2 != null){
          request.setHeaders(p2);
       }
       return request;
    }
    public Request createRequest(String p0,String p1,Map p2){
       Request request;
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("b2210a4d", objArray);
       }else if((request = this.createRequest(p0, p1)) == null){
          return null;
       }else if(p2 != null){
          request.addHeader("f-refer", "wv_h5");
          UCNetworkDelegate.getInstance().onSendRequest(p2, p0);
          Iterator iterator = p2.entrySet().iterator();
          while (iterator.hasNext()) {
             Map$Entry uEntry = iterator.next();
             String key = uEntry.getKey();
             String value = uEntry.getValue();
             request.addHeader(key, value);
             StringBuilder str = "AliRequestAdapter from uc header key=".append(key);
             v7t.a(this.TAG, str.append(",value=").append(value).toString());
          }
       }
       return request;
    }
    public Request getAliRequest(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mAliRequest;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1a01dad6", objArray);
    }
    public String getCurId(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mCurId;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("24aaca54", objArray);
    }
    public EventHandler getEventHandler(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mEventHandler;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ccb6ed91", objArray);
    }
    public Map getHeaders(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mHeaders;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("cf4415cc", objArray);
    }
    public String getMethod(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mMethod;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5e63d782", objArray);
    }
    public String getPId(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mParentId;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("dc67dba4", objArray);
    }
    public IRequestTiming getRequestTiming(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mRequestTiming;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1f4a73b4", objArray);
    }
    public Map getUploadDataMap(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mUploadDataMap;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("e57f3617", objArray);
    }
    public Map getUploadFileMap(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mUploadFileMap;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("96766785", objArray);
    }
    public long getUploadFileTotalLen(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mUploadFileTotalLen;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6542f2df", objArray).longValue();
    }
    public String getUrl(){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mUrl;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("de8f0660", objArray);
    }
    public void setBizCode(String p0){
       AliRequestAdapter tmAliRequest;
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d29306ef", objArray);
          return;
       }else {
          this.bizCode = p0;
          if ((tmAliRequest = this.mAliRequest) != null) {
             tmAliRequest.setBizId(p0);
          }
          return;
       }
    }
    public void setCurId(String p0){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("1d87160a", objArray);
          return;
       }else {
          this.mCurId = p0;
          return;
       }
    }
    public void setEventHandler(EventHandler p0){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("31cc001", objArray);
          return;
       }else {
          this.mEventHandler = p0;
          return;
       }
    }
    public void setFutureResponse(Future p0){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("7dc7f4d9", objArray);
          return;
       }else {
          this.mFutureResponse = p0;
          return;
       }
    }
    public void setId(String p0){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("eb80bee", objArray);
          return;
       }else {
          this.mParentId = p0;
          this.mAliRequest.setExtProperty("f-pTraceId", p0);
          return;
       }
    }
    public void setRequestTiming(IRequestTiming p0){
       IpChange $ipChange = AliRequestAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d11bbce6", objArray);
          return;
       }else {
          this.mRequestTiming = p0;
          return;
       }
    }
}
