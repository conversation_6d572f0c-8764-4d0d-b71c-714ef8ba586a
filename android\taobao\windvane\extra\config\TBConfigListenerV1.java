package android.taobao.windvane.extra.config.TBConfigListenerV1;
import tb.z8l;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.StringBuilder;
import tb.v7t;
import com.taobao.orange.OrangeConfig;
import android.taobao.windvane.jsbridge.WVJsBridge;

public class TBConfigListenerV1 implements z8l	// class@00018a from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000bf);
       t2o.a(0x26a00040);
    }
    public void TBConfigListenerV1(){
       super();
    }
    public void onConfigUpdate(String p0,boolean p1){
       IpChange $ipChange = TBConfigListenerV1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("9458c0f9", objArray);
          return;
       }else if(TextUtils.isEmpty(p0)){
          return;
       }else {
          v7t.a("TBConfigReceiver", "ConfigName: "+p0+" isFromLocal:"+p1);
          String str = "WindVane";
          if (p0.equalsIgnoreCase(str)) {
             WVJsBridge.f = OrangeConfig.getInstance().getConfig(str, "enableGetParamByJs", "0").equals("1");
          }
          return;
       }
    }
}
