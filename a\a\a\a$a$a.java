package a.a.a.a$a$a;
import a.a.a.a;
import tb.t2o;
import android.os.IBinder;
import java.lang.Object;
import java.lang.String;
import android.os.Parcel;

public class a$a$a implements a	// class@000006 from classes.dex
{
    public final IBinder a;

    static {
       t2o.a(0xa900003);
       t2o.a(0xa900001);
    }
    public void a$a$a(IBinder p0){
       super();
       this.a = p0;
    }
    public String a(String p0,String p1,String p2){
       Parcel parcel = Parcel.obtain();
       Parcel parcel1 = Parcel.obtain();
       parcel.writeInterfaceToken("com.heytap.openid.IOpenID");
       parcel.writeString(p0);
       parcel.writeString(p1);
       parcel.writeString(p2);
       this.a.transact(1, parcel, parcel1, 0);
       parcel1.readException();
       parcel1.recycle();
       parcel.recycle();
       return parcel1.readString();
    }
    public IBinder asBinder(){
       return this.a;
    }
}
