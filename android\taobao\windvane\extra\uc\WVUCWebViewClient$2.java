package android.taobao.windvane.extra.uc.WVUCWebViewClient$2;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.uc.WVUCWebViewClient;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import org.json.JSONObject;
import tb.jpw;
import tb.lab;
import java.lang.Class;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.view.View;
import java.lang.StringBuilder;
import android.os.SystemClock;
import tb.v7t;
import java.lang.Throwable;

public class WVUCWebViewClient$2 implements ValueCallback	// class@000264 from classes.dex
{
    public final WVUCWebViewClient this$0;
    public final WVUCWebView val$webview;
    public static IpChange $ipChange;

    public void WVUCWebViewClient$2(WVUCWebViewClient p0,WVUCWebView p1){
       this.this$0 = p0;
       this.val$webview = p1;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       JSONObject metaObject;
       lab olab;
       String str = "WV.Meta.Performance.JSFSP";
       String str1 = "no JSFSP setTag ";
       String str2 = "no version setTag ";
       IpChange $ipChange = WVUCWebViewClient$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else if((metaObject = WVUCWebViewClient.getMetaObject(p0)) != null && this.val$webview != null){
          if ((olab = jpw.c().a(lab.class)) != null && metaObject.has(str)) {
             if (!TextUtils.isEmpty(metaObject.optString(str))) {
                if (!this.val$webview.isReportedFSP()) {
                   this.val$webview.setTag(olab.d(), olab.f());
                }
             }else {
                this.val$webview.setTag(olab.d(), olab.a());
                v7t.a("WVUCWebViewClient", str2+SystemClock.uptimeMillis());
             }
          }else if(olab != null){
             this.val$webview.setTag(olab.d(), olab.a());
          }
          v7t.a("WVUCWebViewClient", str1+SystemClock.uptimeMillis());
          return;
       }else {
          return;
       }
    }
}
