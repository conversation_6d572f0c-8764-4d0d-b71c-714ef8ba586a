package android.taobao.windvane.config.EnvEnum;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;
import java.lang.Number;
import java.lang.Integer;

public final class EnvEnum extends Enum	// class@00014b from classes.dex
{
    private int key;
    private String value;
    private static final EnvEnum[] $VALUES;
    public static IpChange $ipChange;
    public static final EnvEnum DAILY;
    public static final EnvEnum ONLINE;
    public static final EnvEnum PRE;

    static {
       EnvEnum uEnvEnum = new EnvEnum("ONLINE", 0, 0, "m");
       EnvEnum.ONLINE = uEnvEnum;
       EnvEnum uEnvEnum1 = new EnvEnum("PRE", 1, 1, "wapa");
       EnvEnum.PRE = uEnvEnum1;
       EnvEnum uEnvEnum2 = new EnvEnum("DAILY", 2, 2, "waptest");
       EnvEnum.DAILY = uEnvEnum2;
       EnvEnum[] uEnvEnumArra = new EnvEnum[]{uEnvEnum,uEnvEnum1,uEnvEnum2};
       EnvEnum.$VALUES = uEnvEnumArra;
    }
    private void EnvEnum(String p0,int p1,int p2,String p3){
       super(p0, p1);
       this.key = p2;
       this.value = p3;
    }
    public static Object ipc$super(EnvEnum p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/config/EnvEnum");
    }
    public static EnvEnum valueOf(String p0){
       IpChange $ipChange = EnvEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(EnvEnum.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("e4241be7", objArray);
    }
    public static EnvEnum[] values(){
       IpChange $ipChange = EnvEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return EnvEnum.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("b5c4bf58", objArray);
    }
    public int getKey(){
       IpChange $ipChange = EnvEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.key;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("d201723", objArray).intValue();
    }
    public String getValue(){
       IpChange $ipChange = EnvEnum.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.value;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5308aa1e", objArray);
    }
    public void setKey(int p0){
       IpChange $ipChange = EnvEnum.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("919ba2df", objArray);
          return;
       }else {
          this.key = p0;
          return;
       }
    }
    public void setValue(String p0){
       IpChange $ipChange = EnvEnum.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("bae52f80", objArray);
          return;
       }else {
          this.value = p0;
          return;
       }
    }
}
