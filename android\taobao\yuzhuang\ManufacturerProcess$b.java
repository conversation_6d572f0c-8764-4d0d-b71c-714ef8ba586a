package android.taobao.yuzhuang.ManufacturerProcess$b;
import tb.t2o;
import android.content.Context;
import java.lang.Object;
import android.os.Build;
import java.lang.String;
import android.content.SharedPreferences;
import android.content.SharedPreferences$Editor;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.StringBuilder;
import android.util.Log;
import java.io.File;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.util.zip.ZipFile;
import java.util.zip.ZipEntry;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.BufferedReader;
import java.io.Reader;
import android.taobao.yuzhuang.ManufacturerProcess$a;
import java.lang.Throwable;
import android.taobao.yuzhuang.ManufacturerProcess$b$a;
import java.io.FilenameFilter;
import android.os.Build$VERSION;
import android.taobao.yuzhuang.ManufacturerProcess$b$b;
import android.taobao.yuzhuang.ManufacturerProcess$b$c;
import java.lang.Boolean;
import android.taobao.yuzhuang.ManufacturerProcess$Config;
import java.util.Arrays;
import java.util.Objects;

public class ManufacturerProcess$b	// class@000321 from classes.dex
{
    public final Context a;
    public final SharedPreferences b;
    public final SharedPreferences$Editor c;
    public static IpChange $ipChange;
    public static final int FROM_APK;
    public static final int FROM_CONFIG;
    public static String d;
    public static ManufacturerProcess$b e;

    static {
       t2o.a(0x30c00026);
       ManufacturerProcess$b.d = "";
       ManufacturerProcess$b.e = null;
    }
    public void ManufacturerProcess$b(Context p0){
       super();
       this.b = null;
       ManufacturerProcess$b.d = Build.MANUFACTURER;
       SharedPreferences sharedPrefer = p0.getSharedPreferences("manufacture_config", 0);
       this.b = sharedPrefer;
       this.c = sharedPrefer.edit();
       this.a = p0;
    }
    public static ManufacturerProcess$b e(Context p0){
       IpChange $ipChange = ManufacturerProcess$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("c21daac1", objArray);
       }else if(ManufacturerProcess$b.e == null){
          _monitor_enter(ManufacturerProcess$b.class);
          if (ManufacturerProcess$b.e == null) {
             ManufacturerProcess$b.e = new ManufacturerProcess$b(p0);
          }
          _monitor_exit(ManufacturerProcess$b.class);
       }
       return ManufacturerProcess$b.e;
    }
    public final void a(String[] p0,int p1){
       int i = 3;
       IpChange $ipChange = ManufacturerProcess$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          objArray[1] = p0;
          objArray[2] = new Integer(p1);
          $ipChange.ipc$dispatch("21438102", objArray);
          return;
       }else {
          StringBuilder str = "into commit : "+p0+";from : "+p1;
          String str1 = "preLoad";
          if (p0.length == i && p0[2].equals(this.a.getPackageName())) {
             Log.e("config_file", "results.length == 3");
             Log.e("config_file", "results[0]:"+p0[0]);
             Log.e("config_file", "results[1]:"+p0[1]);
             this.c.putBoolean(str1, 1);
             this.c.putString("preLoad_VersionName", p0[1]);
             if (p1 != 1) {
                if (p1 == 2) {
                   this.c.putString("preLoad_Channel1", p0[0]);
                }
             }else {
                this.c.putString("preLoad_Channel2", p0[0]);
             }
             this.c.commit();
          }else if(p0.length == 2 && p0[1].equals(this.a.getPackageName())){
             Log.e("config_file", "results.length == 2");
             Log.e("config_file", "results[0]:"+p0[0]);
             Log.e("config_file", "results[1]:"+p0[1]);
             this.c.putBoolean(str1, 1);
             if (p1 != 1) {
                if (p1 == 2) {
                   this.c.putString("preLoad_Channel1", p0[0]);
                }
             }else {
                this.c.putString("preLoad_Channel2", p0[0]);
             }
             this.c.commit();
          }
          return;
       }
    }
    public final void b(File p0){
       int i = 0;
       int i1 = 2;
       IpChange $ipChange = ManufacturerProcess$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          objArray[1] = p0;
          $ipChange.ipc$dispatch("7a5fd4e4", objArray);
          return;
       }else if(this.b.getBoolean("preLoad", i) && !TextUtils.isEmpty(this.b.getString("preLoad_Channel1", ""))){
          Log.e("config_file", "skipped");
          return;
       }else {
          ZipFile zipFile = new ZipFile(p0);
          String str = "assets/aconfig.xml";
          String str1 = "assets/aconfig.ini";
          if (zipFile.getEntry(str) != null || zipFile.getEntry(str1) != null) {
             StringBuilder str2 = "";
             ZipEntry entry = (zipFile.getEntry(str) == null)? zipFile.getEntry(str1): zipFile.getEntry(str);
             BufferedReader uBufferedRea = new BufferedReader(new InputStreamReader(zipFile.getInputStream(entry)));
             while ((str1 = uBufferedRea.readLine()) != null) {
                str2 = str2.append(str1);
             }
             this.a(str2.split("\\|"), i1);
             uBufferedRea.close();
          }
          zipFile.close();
          return;
       }
    }
    public final void c(File p0){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = ManufacturerProcess$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("94c9c2d2", objArray);
          return;
       }else if(this.b.getBoolean("preLoad", i1) && !TextUtils.isEmpty(this.b.getString("preLoad_Channel2", ""))){
          Log.e("config_file", "skipped");
          return;
       }else {
          try{
             this.a(new ManufacturerProcess$a("channel").a(p0.getAbsolutePath()).split("\\|"), i);
          }catch(java.lang.Exception e6){
             e6.printStackTrace();
          }
          return;
       }
    }
    public final File d(String p0){
       File[] uFileArray;
       int i = 0;
       IpChange $ipChange = ManufacturerProcess$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("8f3d4d5a", objArray);
       }else if((uFileArray = new File(p0).listFiles(new ManufacturerProcess$b$a(this))) != null && uFileArray.length > 0){
          return uFileArray[i];
       }else if(ManufacturerProcess$b.d.equalsIgnoreCase("vivo") && (Build$VERSION.SDK_INT >= 23 && ((uFileArray = new File("/apps").listFiles(new ManufacturerProcess$b$b(this))) != null && uFileArray.length > 0))){
          return uFileArray[i];
       }else if(ManufacturerProcess$b.d.equalsIgnoreCase("zte")){
          File uFile = new File(p0);
          if (uFile.isDirectory()) {
             return new ManufacturerProcess$b$c().a(uFile);
          }
       }
       return null;
    }
    public final boolean f(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = ManufacturerProcess$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("8f3b4fd", objArray).booleanValue();
       }else {
          boolean booleanx = this.b.getBoolean("preLoad", i);
          String str = "";
          boolean b = (TextUtils.isEmpty(this.b.getString("preLoad_Channel1", str)) && TextUtils.isEmpty(this.b.getString("preLoad_Channel2", str)))? false: true;
          Log.e("config_file", "preLoadCommitted = "+booleanx+" ,  anyChannelNotEmpty = "+b);
          if (booleanx && b) {
             i = true;
          }
          return i;
       }
    }
    public void g(){
       String[] config;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = ManufacturerProcess$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          $ipChange.ipc$dispatch("bffd376", objArray);
          return;
       }else {
          String str = "config_file";
          Log.e(str, "manufacturer: "+ManufacturerProcess$b.d);
          if (this.f()) {
             Log.e(str, "skip process, isCommited == true");
             return;
          }else if((config = ManufacturerProcess$Config.getConfig(ManufacturerProcess$b.d)) == null){
             Log.e(str, "Config.getConfig\(manufacturer\) == null ");
             return;
          }else {
             Arrays.toString(config);
             this.h(config[i]);
             this.i(ManufacturerProcess$Config.transformSpecificChannelConfig(ManufacturerProcess$b.d, this.a, config[i1]));
             return;
          }
       }
    }
    public final void h(String p0){
       IpChange $ipChange = ManufacturerProcess$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f083cb5d", objArray);
          return;
       }else if(!TextUtils.isEmpty(p0)){
          File uFile = this.d(p0);
          Objects.toString(uFile);
          if (uFile != null && uFile.exists()) {
             try{
                uFile.toString();
                this.b(uFile);
             }catch(java.io.IOException e5){
                e5.printStackTrace();
             }
          }
       }else {
          Log.e("config_file", ManufacturerProcess$b.d+" in-apk config is empty ");
       }
       return;
    }
    public final void i(String p0){
       StringBuilder str;
       IpChange $ipChange = ManufacturerProcess$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("44f5e6c4", objArray);
          return;
       }else if(!(str = ManufacturerProcess$b.d+" channel romChannelConfig = "+p0)){
          File uFile = new File(p0);
          File uFile1 = new File(p0.replace("xml", "ini"));
          p0 = ManufacturerProcess$b.d+" configFile.abs_path = "+uFile.getAbsolutePath();
          if (p0 = ManufacturerProcess$b.d+" configFile1.abs_path = "+uFile1.getAbsolutePath()) {
             this.c(uFile);
             Log.e("config_file", "file is exist , path : ");
          }else if(uFile1.exists()){
             this.c(uFile1);
             Log.e("config_file", "file is exist , path1 : ");
          }else {
             Log.e("config_file", "config file is not exist");
          }
       }else {
          Log.e("config_file", "TextUtils.isEmpty\(romChannelConfig\)");
       }
       return;
    }
}
