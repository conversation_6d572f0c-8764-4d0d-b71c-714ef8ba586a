package android.taobao.windvane.extra.uc.MtopSsrServiceFactory;
import tb.t2o;
import java.lang.Object;
import tb.nnf;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.extra.uc.FirstTruckCacheSSRService;
import tb.zeq;

public class MtopSsrServiceFactory	// class@00021a from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d80014f);
    }
    public void MtopSsrServiceFactory(){
       super();
    }
    public static nnf createSsrService(){
       IpChange $ipChange = MtopSsrServiceFactory.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("de5680df", objArray);
       }else if(vpw.commonConfig.P1 != null){
          return new FirstTruckCacheSSRService();
       }else {
          return zeq.a();
       }
    }
}
