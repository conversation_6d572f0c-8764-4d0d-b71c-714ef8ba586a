package android.taobao.windvane.extra.uc.SSRPrerenderService$1;
import tb.mnf;
import android.taobao.windvane.extra.uc.SSRPrerenderService;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicBoolean;
import java.io.ByteArrayOutputStream;
import java.lang.Object;
import tb.bgq;
import tb.egq;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.io.OutputStream;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;
import java.nio.charset.StandardCharsets;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Map;
import java.lang.Integer;
import java.util.List;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.icn;
import java.lang.Boolean;

public class SSRPrerenderService$1 implements mnf	// class@00021b from classes.dex
{
    public final SSRPrerenderService this$0;
    public final ByteArrayOutputStream val$byteArrayOutputStream;
    public final AtomicBoolean val$hasSkippedFirstPart;
    public final AtomicInteger val$preRenderAssetsLength;
    public final mnf val$ssrRequestCallback;
    public static IpChange $ipChange;

    public void SSRPrerenderService$1(SSRPrerenderService p0,AtomicInteger p1,mnf p2,AtomicBoolean p3,ByteArrayOutputStream p4){
       this.this$0 = p0;
       this.val$preRenderAssetsLength = p1;
       this.val$ssrRequestCallback = p2;
       this.val$hasSkippedFirstPart = p3;
       this.val$byteArrayOutputStream = p4;
       super();
    }
    public void onError(bgq p0,egq p1){
       IpChange $ipChange = SSRPrerenderService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("7a95834", objArray);
          return;
       }else {
          this.val$ssrRequestCallback.onError(p0, p1);
          return;
       }
    }
    public void onFinish(bgq p0){
       IpChange $ipChange = SSRPrerenderService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4d53f97", objArray);
          return;
       }else {
          this.val$ssrRequestCallback.onFinish(p0);
          return;
       }
    }
    public void onReceiveData(bgq p0,byte[] p1){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = SSRPrerenderService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("150b5e1a", objArray);
          return;
       }else if(p1 == null){
          return;
       }else if(this.val$preRenderAssetsLength.get() > 0 && !this.val$hasSkippedFirstPart.get()){
          try{
             this.val$byteArrayOutputStream.write(p1);
          }catch(java.io.IOException e0){
             lcn.f(RVLLevel.Error, "WindVane/NetworkSSRPrerender", "failed to write bytes");
          }
          try{
             Charset uTF_8 = StandardCharsets.UTF_8;
             String str = this.val$byteArrayOutputStream.toString(uTF_8.name());
             if (str.length() >= this.val$preRenderAssetsLength.get()) {
                str = str.substring(i1, this.val$preRenderAssetsLength.get());
                SSRPrerenderService.access$000(this.this$0, p0.a, str);
                str = str.getBytes(uTF_8);
                byte[] uobyteArray = this.val$byteArrayOutputStream.toByteArray();
                if (str.length < uobyteArray.length) {
                   this.val$ssrRequestCallback.onReceiveData(p0, Arrays.copyOfRange(uobyteArray, str.length, uobyteArray.length));
                }
                this.val$hasSkippedFirstPart.set(e0);
             }
          }catch(java.lang.Exception e0){
             lcn.f(RVLLevel.Error, "WindVane/NetworkSSRPrerender", "failed to parse resource list.");
          }
       }else {
          this.val$ssrRequestCallback.onReceiveData(p0, p1);
       }
       return;
    }
    public void onResponse(bgq p0,int p1,Map p2){
       String str;
       List list;
       int i2;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = SSRPrerenderService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
          $ipChange.ipc$dispatch("1e09d3a7", objArray);
          return;
       }else {
          try{
             str = "WindVane/NetworkSSRPrerender";
             if (p2 != null && (list = p2.get("x-s-prerender-assets-length")) != null) {
                String str1 = list.get(i1);
                if (!TextUtils.isEmpty(str1) && (i2 = Integer.parseInt(str1)) >= 0) {
                   this.val$preRenderAssetsLength.set(i2);
                }
             }
          }catch(java.lang.Exception e0){
             lcn.f(RVLLevel.Error, str, "failed to parse x-s-prerender-assets-length");
          }
          if (this.val$preRenderAssetsLength.get() <= 0) {
             i = false;
          }
          lcn.a(RVLLevel.Info, str).j("response").a("preRenderAssetsLength", Integer.valueOf(this.val$preRenderAssetsLength.get())).a("isPrerenderRequest", Boolean.valueOf(e0)).f();
          this.val$ssrRequestCallback.onResponse(p0, p1, p2);
          return;
       }
    }
}
