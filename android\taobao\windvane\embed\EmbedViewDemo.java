package android.taobao.windvane.embed.EmbedViewDemo;
import android.taobao.windvane.embed.BaseEmbedView;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import org.json.JSONObject;
import java.lang.CharSequence;
import android.widget.TextView;
import java.lang.Float;
import android.graphics.Color;
import android.view.View;
import java.lang.Throwable;
import android.content.Context;
import android.widget.RelativeLayout;
import android.widget.RelativeLayout$LayoutParams;
import android.view.ViewGroup$LayoutParams;
import com.uc.webview.export.extension.EmbedViewConfig;
import android.app.Activity;
import android.view.WindowManager;
import android.view.Display;
import android.util.DisplayMetrics;
import android.view.ViewGroup;

public class EmbedViewDemo extends BaseEmbedView	// class@000154 from classes.dex
{
    public TextView tv;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800051);
    }
    public void EmbedViewDemo(){
       super();
    }
    public static Object ipc$super(EmbedViewDemo p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/embed/EmbedViewDemo");
    }
    public boolean execute(String p0,String p1,WVCallBackContext p2){
       EmbedViewDemo ttv;
       IpChange $ipChange = EmbedViewDemo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("bcd41fd1", objArray).booleanValue();
       }else if(p1 != null){
          try{
             JSONObject jSONObject = new JSONObject(p1);
             if ("setText".equals(p0)) {
                if ((ttv = this.tv) != null) {
                   ttv.setText(jSONObject.getString("text"));
                   p2.success();
                }
                return 1;
             }else if("setTextSize".equals(p0)){
                if ((ttv = this.tv) != null) {
                   ttv.setTextSize(Float.valueOf(jSONObject.getString("size")).floatValue());
                   p2.success();
                }
                return 1;
             }else if("setBackground".equals(p0)){
                if ((ttv = this.tv) != null) {
                   ttv.setBackgroundColor(Color.parseColor(jSONObject.getString("color")));
                   p2.success();
                }
                return 1;
             }else if("setTextColor".equals(p0)){
                if ((ttv = this.tv) != null) {
                   ttv.setTextColor(Color.parseColor(jSONObject.getString("color")));
                   p2.success();
                }
                return 1;
             }
          }catch(org.json.JSONException e6){
             e6.printStackTrace();
          }
       }
       return 0;
    }
    public View generateView(Context p0){
       int i = 0;
       IpChange $ipChange = EmbedViewDemo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("7334ca29", objArray);
       }else {
          RelativeLayout relativeLayo = new RelativeLayout(p0);
          relativeLayo.setLayoutParams(new RelativeLayout$LayoutParams(-1, -2));
          BaseEmbedView tparams = this.params;
          EmbedViewConfig mWidth = tparams.mWidth;
          EmbedViewConfig mHeight = tparams.mHeight;
          if (p0 instanceof Activity) {
             DisplayMetrics uDisplayMetr = new DisplayMetrics();
             p0.getWindowManager().getDefaultDisplay().getMetrics(uDisplayMetr);
             i = uDisplayMetr.widthPixels;
          }
          RelativeLayout$LayoutParams layoutParams = new RelativeLayout$LayoutParams(-1, (int)((float)mHeight * ((float)i / (float)mWidth)));
          layoutParams.addRule(13);
          TextView i1 = new TextView(p0);
          this.tv = i1;
          i1.setBackgroundColor(-7829368);
          this.tv.setText("EmbedView DEMO");
          this.tv.setTextColor(0xffff0000);
          this.tv.setTextSize(30.00f);
          this.tv.setGravity(17);
          relativeLayo.addView(this.tv, layoutParams);
          return relativeLayo;
       }
    }
    public String getViewType(){
       IpChange $ipChange = EmbedViewDemo.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "demo";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("35692924", objArray);
    }
}
