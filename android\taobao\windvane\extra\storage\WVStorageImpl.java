package android.taobao.windvane.extra.storage.WVStorageImpl;
import android.taobao.windvane.extra.storage.IStorage;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.taobao.alivfssdk.cache.AVFSCacheManager;
import tb.np;
import tb.fdb;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;

public class WVStorageImpl implements IStorage	// class@0001ed from classes.dex
{
    private fdb mAvfsCache;
    private String mNamespace;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800122);
       t2o.a(0x3d80011e);
    }
    public void WVStorageImpl(String p0){
       AVFSCacheManager instance;
       np onp;
       super();
       this.mNamespace = p0;
       if ((instance = AVFSCacheManager.getInstance()) != null && (onp = instance.cacheForModule(p0)) != null) {
          this.mAvfsCache = onp.C();
       }
       return;
    }
    public String read(String p0){
       WVStorageImpl tmAvfsCache;
       IpChange $ipChange = WVStorageImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("5a0eb7d9", objArray);
       }else if((tmAvfsCache = this.mAvfsCache) != null){
          return tmAvfsCache.d0(p0);
       }else {
          return null;
       }
    }
    public boolean remove(String p0){
       WVStorageImpl tmAvfsCache;
       int i = 0;
       IpChange $ipChange = WVStorageImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("39af3819", objArray).booleanValue();
       }else if((tmAvfsCache = this.mAvfsCache) != null){
          return tmAvfsCache.A0(p0);
       }else {
          return i;
       }
    }
    public boolean write(String p0,String p1){
       WVStorageImpl tmAvfsCache;
       int i = 0;
       IpChange $ipChange = WVStorageImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("1b6c0efe", objArray).booleanValue();
       }else if((tmAvfsCache = this.mAvfsCache) != null){
          return tmAvfsCache.N(p0, p1);
       }else {
          return i;
       }
    }
}
