package android.taobao.windvane.webview.WVWebChromeClient;
import android.webkit.WebChromeClient;
import tb.t2o;
import android.content.Context;
import java.lang.String;
import java.lang.Object;
import android.webkit.WebView;
import java.lang.Number;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.webkit.GeolocationPermissions$Callback;
import android.webkit.ConsoleMessage;
import java.lang.Boolean;
import com.android.alibaba.ip.runtime.IpChange;
import tb.lqw;
import tb.kqw;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.v7t;
import android.taobao.windvane.jsbridge.WVJsBridge;
import android.taobao.windvane.webview.IWVWebView;
import android.webkit.ValueCallback;
import tb.urw;
import android.taobao.windvane.webview.WVWebChromeClient$a;
import android.webkit.ConsoleMessage$MessageLevel;
import java.lang.Enum;
import tb.vqw;
import tb.trw;
import android.webkit.WebStorage$QuotaUpdater;
import java.lang.Long;
import android.webkit.JsPromptResult;
import android.taobao.windvane.webview.WVWebView;
import java.lang.Integer;

public class WVWebChromeClient extends WebChromeClient	// class@00030e from classes.dex
{
    public WebChromeClient extraWebChromeClient;
    public Context mContext;
    public IWVWebView mWebView;
    public static IpChange $ipChange;
    private static final long MAX_QUOTA;
    private static final String TAG;

    static {
       t2o.a(0x3d8002f2);
    }
    public void WVWebChromeClient(){
       super();
       this.extraWebChromeClient = null;
       this.mWebView = null;
    }
    public void WVWebChromeClient(Context p0){
       super();
       this.extraWebChromeClient = null;
       this.mWebView = null;
       this.mContext = p0;
    }
    public static Object ipc$super(WVWebChromeClient p0,String p1,Object[] p2){
       int i;
       if ((i = p1.hashCode()) == -634514222) {
          return new Boolean(super.onConsoleMessage(p2[0]));
       }
       if (i != 0xd6bfe0) {
          if (i != 0x16eaf531) {
             throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/webview/WVWebChromeClient");
          }
          super.onProgressChanged(p2[0], p2[1].intValue());
          return null;
       }else {
          super.onGeolocationPermissionsShowPrompt(p2[0], p2[1]);
          return null;
       }
    }
    public boolean onConsoleMessage(ConsoleMessage p0){
       ValueCallback valueCallbac;
       int i5;
       Object[] objArray1;
       WVWebChromeClient textraWebChr;
       int i = 3;
       int i1 = 1;
       int i2 = 0;
       int i3 = 2;
       IpChange $ipChange = WVWebChromeClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i3];
          objArray[i2] = this;
          objArray[i1] = p0;
          return $ipChange.ipc$dispatch("da2e14d2", objArray).booleanValue();
       }else if(lqw.d().e(2001).a != null){
          return i1;
       }else {
          String str = p0.message();
          if (!TextUtils.isEmpty(str) && str.startsWith("hybrid://")) {
             v7t.d("WVWebChromeClient", "Call from console.log");
             if (this.mWebView != null) {
                WVJsBridge.g().b(this.mWebView, str);
                return i1;
             }
          }
          if (str != null && str.startsWith("wvNativeCallback")) {
             String str1 = str.substring((str.indexOf("/") + i1));
             int i4 = str1.indexOf("/");
             String str2 = str1.substring(i2, i4);
             String str3 = str1.substring((i4 + i1));
             if ((valueCallbac = urw.b(str2)) != null) {
                valueCallbac.onReceiveValue(str3);
                urw.a(str2);
             }else {
                v7t.d("WVWebChromeClient", "NativeCallback failed: "+str3);
             }
             return i1;
          }else if(v7t.h()){
             if ((i5 = WVWebChromeClient$a.$SwitchMap$android$webkit$ConsoleMessage$MessageLevel[p0.messageLevel().ordinal()]) != i1) {
                if (i5 != i3) {
                   objArray1 = new Object[i];
                   objArray1[i2] = p0.message();
                   objArray1[i1] = p0.sourceId();
                   objArray1[i3] = String.valueOf(p0.lineNumber());
                   v7t.c("WVWebChromeClient", "onConsoleMessage: %s at %s: %s", objArray1);
                }else {
                   objArray1 = new Object[i];
                   objArray1[i2] = p0.message();
                   objArray1[i1] = p0.sourceId();
                   objArray1[i3] = String.valueOf(p0.lineNumber());
                   v7t.f("WVWebChromeClient", "onConsoleMessage: %s at %s: %s", objArray1);
                   if (trw.getWvJsErrorMonitorInterface() != null) {
                      trw.getWvJsErrorMonitorInterface().onOccurJsError(p0.sourceId(), p0.message(), null);
                   }
                }
             }else {
                objArray1 = new Object[i];
                objArray1[i2] = p0.message();
                objArray1[i1] = p0.sourceId();
                objArray1[i3] = String.valueOf(p0.lineNumber());
                v7t.p("WVWebChromeClient", "onConsoleMessage: %s at %s: %s", objArray1);
             }
          }
          if ((textraWebChr = this.extraWebChromeClient) != null) {
             return textraWebChr.onConsoleMessage(p0);
          }else {
             return super.onConsoleMessage(p0);
          }
       }
    }
    public void onExceededDatabaseQuota(String p0,String p1,long p2,long p3,long p4,WebStorage$QuotaUpdater p5){
       IpChange $ipChange = WVWebChromeClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Long(p2),new Long(p3),new Long(p4),p5};
          $ipChange.ipc$dispatch("8e57272c", objArray);
          return;
       }else if((p3 - 0x1400000) < 0){
          p5.updateQuota(p3);
       }else {
          p5.updateQuota(p2);
       }
       return;
    }
    public void onGeolocationPermissionsShowPrompt(String p0,GeolocationPermissions$Callback p1){
       IpChange $ipChange = WVWebChromeClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("d6bfe0", objArray);
          return;
       }else {
          super.onGeolocationPermissionsShowPrompt(p0, p1);
          p1.invoke(p0, 1, 0);
          return;
       }
    }
    public boolean onJsPrompt(WebView p0,String p1,String p2,String p3,JsPromptResult p4){
       Object[] objArray;
       WVWebChromeClient textraWebChr;
       int i = 3;
       int i1 = 2;
       int i2 = 0;
       IpChange $ipChange = WVWebChromeClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[]{this,p0,p1,p2,p3,p4};
          return $ipChange.ipc$dispatch("cfc92fda", objArray).booleanValue();
       }else {
          String str = "WVWebChromeClient";
          if (v7t.h()) {
             objArray = new Object[i];
             objArray[i2] = p2;
             objArray[1] = p3;
             objArray[i1] = p1;
             v7t.k(str, "onJsPrompt: %s; defaultValue: %s; url: %s", objArray);
          }
          if (p0 instanceof IWVWebView) {
             Object[] objArray1 = new Object[i];
             objArray1[i2] = p2;
             objArray1[1] = p3;
             if ((objArray1[i1] = p4) != null) {
                return 1;
             }
          }
          if (p3 != null && p3.equals("wv_hybrid:")) {
             v7t.d(str, "Call from console.prompt");
             WVJsBridge.g().b(p0, p2);
             p4.confirm("");
             return 1;
          }else if((textraWebChr = this.extraWebChromeClient) != null){
             return textraWebChr.onJsPrompt(p0, p1, p2, p3, p4);
          }else {
             return i2;
          }
       }
    }
    public void onProgressChanged(WebView p0,int p1){
       IpChange $ipChange = WVWebChromeClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1)};
          $ipChange.ipc$dispatch("16eaf531", objArray);
          return;
       }else {
          super.onProgressChanged(p0, p1);
          return;
       }
    }
}
