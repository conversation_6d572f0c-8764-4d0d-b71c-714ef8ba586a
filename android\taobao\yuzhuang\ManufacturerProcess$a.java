package android.taobao.yuzhuang.ManufacturerProcess$a;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import java.io.InputStream;
import com.android.alibaba.ip.runtime.IpChange;
import java.io.ByteArrayOutputStream;
import java.lang.StringBuilder;
import java.io.FileInputStream;
import java.io.ByteArrayInputStream;
import java.io.InputStreamReader;
import java.io.BufferedReader;
import java.io.Reader;
import javax.crypto.Cipher;
import javax.crypto.spec.SecretKeySpec;
import java.security.Key;

public class ManufacturerProcess$a	// class@00031c from classes.dex
{
    public Cipher a;
    public static IpChange $ipChange;

    static {
       t2o.a(0x30c00025);
    }
    public void ManufacturerProcess$a(String p0){
       super();
       this.a = null;
       this.d(p0);
       this.e(p0);
    }
    public static final byte[] f(InputStream p0){
       int i;
       IpChange $ipChange = ManufacturerProcess$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("78a8d0a1", objArray);
       }else {
          ByteArrayOutputStream uByteArrayOu = new ByteArrayOutputStream();
          byte[] uobyteArray = new byte[1024];
          while ((i = p0.read(uobyteArray, 0, 1024)) > 0) {
             uByteArrayOu.write(uobyteArray, 0, i);
          }
          return uByteArrayOu.toByteArray();
       }
    }
    public String a(String p0){
       IpChange $ipChange = ManufacturerProcess$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("72c54002", objArray);
       }else {
          StringBuilder str = "";
          FileInputStream uFileInputSt = new FileInputStream(p0);
          BufferedReader uBufferedRea = new BufferedReader(new InputStreamReader(new ByteArrayInputStream(this.b(ManufacturerProcess$a.f(uFileInputSt)))));
          while ((p0 = uBufferedRea.readLine()) != null) {
             str = str.append(p0);
          }
          uFileInputSt.close();
          return str;
       }
    }
    public byte[] b(byte[] p0){
       IpChange $ipChange = ManufacturerProcess$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a.doFinal(p0);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("cd4fbce2", objArray);
    }
    public final SecretKeySpec c(byte[] p0){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = ManufacturerProcess$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("5e52993d", objArray);
       }else {
          byte[] uobyteArray = new byte[8];
          for (; i1 < p0.length && i1 < 8; i1 = i1 + i) {
             uobyteArray[i1] = p0[i1];
          }
          return new SecretKeySpec(uobyteArray, "DES");
       }
    }
    public final void d(String p0){
       int i = 2;
       IpChange $ipChange = ManufacturerProcess$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          objArray[1] = p0;
          $ipChange.ipc$dispatch("269858c9", objArray);
          return;
       }else {
          Cipher instance = Cipher.getInstance("DES");
          this.a = instance;
          instance.init(i, this.c(p0.getBytes()));
          return;
       }
    }
    public final void e(String p0){
       IpChange $ipChange = ManufacturerProcess$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a61e1b9a", objArray);
          return;
       }else {
          Cipher.getInstance("DES").init(1, this.c(p0.getBytes()));
          return;
       }
    }
}
