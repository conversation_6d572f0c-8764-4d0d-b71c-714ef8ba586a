package android.taobao.windvane.export.network.b$b;
import java.lang.Runnable;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import android.taobao.windvane.export.network.b;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import java.lang.Integer;

public final class b$b implements Runnable	// class@000172 from classes.dex
{
    public final int a;
    public final String b;
    public static IpChange $ipChange;

    public void b$b(int p0,String p1){
       this.a = p0;
       this.b = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = b$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(b.j(this.a)){
          lcn.a(RVLLevel.Error, "Themis/Performance/RequestPrefetch").j("prefetchExpired").a("requestId", Integer.valueOf(this.a)).f();
          b.a(this.b);
       }
       return;
    }
}
