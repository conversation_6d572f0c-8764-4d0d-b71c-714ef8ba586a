package android.taobao.windvane.extra.storage.ProtoDBStorageImpl;
import android.taobao.windvane.extra.storage.IStorage;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.taobao.android.protodb.Config;
import com.taobao.android.protodb.LSDB;
import com.taobao.android.riverlogger.RVLLevel;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.lcn;
import com.android.alibaba.ip.runtime.IpChange;
import tb.k3g;
import java.lang.Boolean;

public class ProtoDBStorageImpl implements IStorage	// class@0001ea from classes.dex
{
    private final LSDB mLSDB;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d80011f);
       t2o.a(0x3d80011e);
    }
    public void ProtoDBStorageImpl(String p0){
       LSDB lSDB;
       super();
       Config uConfig = new Config();
       try{
          uConfig.walSize = 0x200000;
          lSDB = LSDB.open(p0, uConfig);
       }catch(java.lang.Exception e4){
          lcn.f(RVLLevel.Error, "WindVane/Storage", "open error: "+e4.getMessage());
          lSDB = null;
       }
       this.mLSDB = lSDB;
       return;
    }
    public String read(String p0){
       ProtoDBStorageImpl tmLSDB;
       IpChange $ipChange = ProtoDBStorageImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("5a0eb7d9", objArray);
       }else if((tmLSDB = this.mLSDB) != null){
          return tmLSDB.getString(new k3g(p0));
       }else {
          return null;
       }
    }
    public boolean remove(String p0){
       ProtoDBStorageImpl tmLSDB;
       int i = 0;
       IpChange $ipChange = ProtoDBStorageImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("39af3819", objArray).booleanValue();
       }else if((tmLSDB = this.mLSDB) != null){
          return tmLSDB.delete(new k3g(p0));
       }else {
          return i;
       }
    }
    public boolean write(String p0,String p1){
       ProtoDBStorageImpl tmLSDB;
       int i = 0;
       IpChange $ipChange = ProtoDBStorageImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("1b6c0efe", objArray).booleanValue();
       }else if((tmLSDB = this.mLSDB) != null){
          return tmLSDB.insertString(new k3g(p0), p1);
       }else {
          return i;
       }
    }
}
