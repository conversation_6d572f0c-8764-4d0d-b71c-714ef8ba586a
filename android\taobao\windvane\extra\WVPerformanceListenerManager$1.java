package android.taobao.windvane.extra.WVPerformanceListenerManager$1;
import java.lang.Runnable;
import android.taobao.windvane.extra.WVPerformanceListenerManager;
import java.util.Map;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.util.List;
import java.util.Iterator;
import android.taobao.windvane.extra.IPerformanceListener;
import java.lang.Throwable;

public class WVPerformanceListenerManager$1 implements Runnable	// class@000187 from classes.dex
{
    public final WVPerformanceListenerManager this$0;
    public final Map val$map;
    public static IpChange $ipChange;

    public void WVPerformanceListenerManager$1(WVPerformanceListenerManager p0,Map p1){
       this.this$0 = p0;
       this.val$map = p1;
       super();
    }
    public void run(){
       int i1;
       int i = 1;
       IpChange $ipChange = WVPerformanceListenerManager$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          Iterator iterator = WVPerformanceListenerManager.access$000(this.this$0).iterator();
          while (iterator.hasNext()) {
             IPerformanceListener iPerformance = iterator.next();
             if ((i1 = iPerformance.getFlag() & i) == i) {
                iPerformance.onPerformanceEventOccur(i, this.val$map);
             }
          }
          return;
       }
    }
}
