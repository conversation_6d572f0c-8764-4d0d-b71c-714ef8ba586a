package android.taobao.windvane.extra.uc.WVUCWebView$6$1;
import android.taobao.windvane.extra.performance2.WVFSPManager$CompletionHandler;
import android.taobao.windvane.extra.uc.WVUCWebView$6;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.uc.WVUCWebView;
import android.taobao.windvane.extra.performance2.WVFSPManager;
import java.lang.Long;
import android.taobao.windvane.extra.performance.WVH5PPManager;

public class WVUCWebView$6$1 implements WVFSPManager$CompletionHandler	// class@000258 from classes.dex
{
    public final WVUCWebView$6 this$1;
    public static IpChange $ipChange;

    public void WVUCWebView$6$1(WVUCWebView$6 p0){
       this.this$1 = p0;
       super();
    }
    public void stopObserving(){
       IpChange $ipChange = WVUCWebView$6$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("65e141fe", objArray);
          return;
       }else {
          WVUCWebView$6 this$0 = this.this$1.this$0;
          this$0.fireEvent(this$0.wvfspManager.eventForFSPStop());
          return;
       }
    }
    public void uploadFSPInfo(String p0,long p1){
       IpChange $ipChange = WVUCWebView$6$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("2c485503", objArray);
          return;
       }else {
          this.this$1.this$0.wvh5PPManager.recordFSP(p1);
          return;
       }
    }
}
