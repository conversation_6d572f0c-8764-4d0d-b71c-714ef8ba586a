package android.taobao.windvane.extra.uc.WVUCWebViewClient$6;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.uc.WVUCWebViewClient;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.util.Map;
import tb.srw;
import tb.trw;
import android.taobao.windvane.extra.core.WVCore;

public class WVUCWebViewClient$6 implements ValueCallback	// class@000268 from classes.dex
{
    public final WVUCWebViewClient this$0;
    public static IpChange $ipChange;

    public void WVUCWebViewClient$6(WVUCWebViewClient p0){
       this.this$0 = p0;
       super();
    }
    public void onReceiveValue(Object p0){
       IpChange $ipChange = WVUCWebViewClient$6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("c60988b0", objArray);
          return;
       }else if(p0 instanceof Map && trw.getWvMonitorInterface() != null){
          trw.getWvMonitorInterface().commitWebMultiTypeByPV(String.valueOf(WVCore.getInstance().getUsedWebMulti()), p0.get("rt"), p0.get("rtWhy"), String.valueOf(WVCore.getInstance().getUsedGpuMulti()), p0.get("gt"), p0.get("gtWhy"));
       }
       return;
    }
}
