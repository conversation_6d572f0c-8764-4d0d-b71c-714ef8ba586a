package android.taobao.yuzhuang.ManufacturerProcess$b$b;
import java.io.FilenameFilter;
import android.taobao.yuzhuang.ManufacturerProcess$b;
import java.lang.Object;
import java.io.File;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.CharSequence;

public class ManufacturerProcess$b$b implements FilenameFilter	// class@00031e from classes.dex
{
    public static IpChange $ipChange;

    public void ManufacturerProcess$b$b(ManufacturerProcess$b p0){
       super();
    }
    public boolean accept(File p0,String p1){
       int i = 1;
       IpChange $ipChange = ManufacturerProcess$b$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("6696dd14", objArray).booleanValue();
       }else if(p1.endsWith(".apk") && p1.toLowerCase().contains("taobao")){
          i = false;
       }
       return i;
    }
}
