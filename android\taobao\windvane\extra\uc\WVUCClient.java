package android.taobao.windvane.extra.uc.WVUCClient;
import com.uc.webview.export.extension.UCClient;
import tb.t2o;
import android.taobao.windvane.webview.IWVWebView;
import java.lang.String;
import java.lang.Object;
import com.uc.webview.export.WebView;
import java.lang.Number;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.uc.webview.export.extension.EmbedViewConfig;
import com.uc.webview.export.extension.IEmbedViewContainer;
import com.uc.webview.export.extension.IEmbedView;
import com.android.alibaba.ip.runtime.IpChange;
import java.util.Map;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.embed.BaseEmbedView;
import tb.fqw;
import tb.v7t;
import com.uc.webview.export.extension.IEmbedViewContainer$OnParamChangedListener;
import com.uc.webview.export.extension.IEmbedViewContainer$OnStateChangedListener;
import com.uc.webview.export.extension.IEmbedViewContainer$OnVisibilityChangedListener;
import android.taobao.windvane.embed.Empty;
import java.lang.Integer;
import android.net.Uri;
import tb.yaa;
import tb.y71;
import tb.psu;
import java.util.ArrayList;
import android.taobao.windvane.extra.uc.WVUCWebView;
import android.taobao.windvane.extra.uc.WVUCWebViewClient;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.taobao.windvane.extra.uc.preRender.PreRenderWebView;
import java.lang.Throwable;
import android.taobao.windvane.startup.UCInitializerInfo;
import com.uc.webview.export.extension.UCExtension;
import android.taobao.windvane.extra.uc.WVUCClient$1;
import android.webkit.ValueCallback;
import java.lang.Long;
import android.taobao.windvane.extra.performance.WVH5PPManager;
import android.taobao.windvane.extra.performance2.WVPageTracker;
import android.taobao.windvane.extra.performance2.WVWPData;
import tb.avt;
import android.taobao.windvane.extra.performance2.IPerformance;
import tb.acd;
import tb.ace;
import tb.cce;
import tb.x74;
import org.json.JSONObject;
import tb.xru;
import tb.srw;
import tb.trw;
import android.taobao.windvane.extra.uc.ExtImgDecoder;

public class WVUCClient extends UCClient	// class@000237 from classes.dex
{
    public String tStart;
    public IWVWebView webView;
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x3d80016b);
    }
    public void WVUCClient(){
       super();
       this.webView = null;
       this.tStart = "0";
    }
    public void WVUCClient(IWVWebView p0){
       super();
       this.tStart = "0";
       this.webView = p0;
    }
    public static Object ipc$super(WVUCClient p0,String p1,Object[] p2){
       int i;
       if ((i = p1.hashCode()) != -1380575401) {
          if (i != 0x59d6aa8c) {
             throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/WVUCClient");
          }
          super.onWebViewEvent(p2[0], p2[1].intValue(), p2[2]);
          return null;
       }else {
          super.onPageStartedEx(p2[0], p2[1]);
          return null;
       }
    }
    public IEmbedView getEmbedView(EmbedViewConfig p0,IEmbedViewContainer p1){
       IpChange $ipChange = WVUCClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("5415cbeb", objArray);
       }else {
          EmbedViewConfig mObjectParam = p0.mObjectParam;
          String str = "viewType";
          if (mObjectParam.containsKey(str)) {
             str = mObjectParam.get(str);
             String str1 = "bridgeId";
             String str2 = (mObjectParam.containsKey(str1))? mObjectParam.get(str1): "";
             BaseEmbedView uBaseEmbedVi = (vpw.commonConfig.k1 != null)? fqw.d(str2, str, this.webView, p0, p1): fqw.c(str2, str, this.webView, p0);
             if (uBaseEmbedVi == null) {
                v7t.d("EmbedView", "failed to create embedView");
             }else {
                p1.setOnParamChangedListener(uBaseEmbedVi);
                p1.setOnStateChangedListener(uBaseEmbedVi);
                p1.setOnVisibilityChangedListener(uBaseEmbedVi);
                return uBaseEmbedVi;
             }
          }else {
             v7t.d("EmbedView", "viewType should not be lost");
          }
          Empty uEmpty = new Empty();
          uEmpty.init("", "empty", this.webView, null);
          return uEmpty;
       }
    }
    public void onGpuProcessGone(WebView p0,int p1){
       int i = 2;
       int i1 = 1;
       IpChange $ipChange = WVUCClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1)};
          $ipChange.ipc$dispatch("71eb23d1", objArray);
          return;
       }else {
          v7t.i("sandbox", "onGpuProcessGone");
          if ((p1 & 0x02)) {
             Uri uri = Uri.parse(this.webView.getUrl());
             String str = uri.getHost()+uri.getPath();
             if (!yaa.k) {
                v7t.d("GPU", "gpu process is killed, url = ["+str+"] , upload information!");
                y71.commitFail("GpuProcessGone", i1, null, str);
             }
             wpw commonConfig = vpw.commonConfig;
             if (commonConfig.i.g.contains(str)) {
                v7t.d("GPU", "gpu process error, need not reload page, url = ["+str+"]");
             }else {
                vpw.b();
                if (commonConfig.R != null) {
                   WVUCClient twebView = this.webView;
                   if (twebView instanceof WVUCWebView) {
                      twebView.refreshWhenForeground();
                   }else {
                      twebView.refresh();
                   }
                   v7t.d("GPU", "gpu process error, reload page, url = ["+str+"]");
                }
             }
          }
          return;
       }
    }
    public void onPageStartedEx(WebView p0,String p1){
       WVUCWebViewClient webViewClien;
       IpChange $ipChange = WVUCClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("adb61757", objArray);
          return;
       }else {
          super.onPageStartedEx(p0, p1);
          if (p0 instanceof WVUCWebView && (webViewClien = p0.getWebViewClient()) != null) {
             webViewClien.setPageStartUrl(p1);
          }
          return;
       }
    }
    public void onWebViewEvent(WebView p0,int p1,Object p2){
       Integer integer;
       String url;
       String str4;
       Map map;
       Map map1;
       long l;
       WVUCWebView wpData;
       String str6;
       int i = 3;
       int i1 = 1;
       int i2 = 0;
       int i3 = 4;
       String str = "H5_UC_T2 time:";
       String str1 = "url = [";
       String str2 = "error unknow o:";
       IpChange $ipChange = WVUCClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i3];
          objArray[i2] = this;
          objArray[i1] = p0;
          objArray[2] = new Integer(p1);
          objArray[i] = p2;
          $ipChange.ipc$dispatch("59d6aa8c", objArray);
          return;
       }else {
          int i4 = 9;
          String str3 = null;
          if (p1 == i4) {
             wpw commonConfig = vpw.commonConfig;
             if (commonConfig.N != null) {
                if (p2 instanceof String) {
                   integer = Integer.valueOf(p2);
                }else if(p2 instanceof Integer){
                   integer = p2;
                }else {
                   v7t.d("WVUCClient", str2+p2);
                   integer = str3;
                }
             }else {
                integer = p2;
             }
             if (!integer.equals(Integer.valueOf(i))) {
                return;
             }else {
                url = p0.getUrl();
                str4 = "TYPEA_";
                if (commonConfig.M != null) {
                   if (TextUtils.isEmpty(url)) {
                      if (p0 instanceof WVUCWebView) {
                         url = p0.getCurrentUrl();
                      }
                   }else {
                      i1 = 0;
                   }
                   if (p0 instanceof PreRenderWebView) {
                      PreRenderWebView preRenderWeb = p0;
                      if (!TextUtils.isEmpty(url) && (url.contains("_wv_preload=true") && preRenderWeb.isPreRender())) {
                         v7t.d("WVUCClient", "preRenderWebView skip upload white page");
                      label_00af :
                         v7t.a("WVUCClient", str1+str3+"]");
                         if (!TextUtils.isEmpty(str3)) {
                            if (!i1) {
                               str4 = "TYPEB_"+integer.toString();
                            }
                            y71.commitEmptyPage(str3, str4);
                         }
                      }
                   }
                   str3 = url;
                   goto label_00af ;
                }else if(TextUtils.isEmpty(url)){
                   if (p0 instanceof WVUCWebView) {
                      url = p0.getCurrentUrl();
                      if (!TextUtils.isEmpty(url)) {
                         y71.commitEmptyPage(url, str4+integer.toString());
                      }
                   }
                }else {
                   y71.commitEmptyPage(url, "TYPEB_"+integer.toString());
                }
             }
          }else if(p1 == 107){
             UCInitializerInfo.a().c(i4);
             v7t.i("sandbox", "onRenderProcessReady");
             if (p0 instanceof WVUCWebView && p0.getUCExtension() != null) {
                p0.getUCExtension().getCoreStatus(i1, new WVUCClient$1(this, p0));
             }
          }else {
             url = "time";
             if (p1 == i3) {
                if (p2 instanceof Map) {
                   map = p2;
                   if (map.containsKey(url)) {
                      this.tStart = map.get(url);
                   }
                }
             }else {
                String str5 = "AIT";
                str1 = "0";
                str2 = "ts";
                if (p1 == 6) {
                   if (p2 instanceof Map) {
                      map1 = p2;
                      if (map1.containsKey(str2)) {
                         str1 = map1.get(str2);
                      }
                   }
                   l = Long.parseLong(str1);
                   p0.wvh5PPManager.recordUCT1(l);
                   p0.pageTracker.onPageReceivedT1(l);
                   v7t.i(str5, "UC_T1: "+l);
                }else {
                   str4 = "url";
                   if (p1 == 14) {
                      if (p2 instanceof Map) {
                         map = p2;
                         if (map.containsKey(str2)) {
                            str1 = map.get(str2);
                         }
                         if (map.containsKey(url)) {
                            url = map.get(url);
                            if (p0 instanceof WVUCWebView && (wpData = p0.wpData) != null) {
                               wpData.t2 = url;
                            }
                         }
                         if (map.containsKey(str4)) {
                            url = map.get(str4);
                         }
                      }
                      l = Long.parseLong(str1);
                      if (p0 instanceof WVUCWebView) {
                         p0.wpData.setPageCurrentStatus("UC_T2");
                      }
                      p0.wvh5PPManager.recordUCT2(l);
                      p0.pageTracker.onPageReceivedT2(l);
                      l = avt.a(l);
                      Long longx = Long.valueOf(l);
                      if (p0 instanceof IPerformance && p0.isPreInit()) {
                         return;
                      }else if(p0 instanceof acd && p0.isPreRender()){
                         return;
                      }else {
                         p0.getWebViewContext().getWebViewPageModel().onStageIfAbsent("H5_UC_T2", l);
                         v7t.d("WVUCClient", str+longx);
                         v7t.i(str5, "UC_T2: "+str1);
                      }
                   }else if(p1 == 20){
                      str6 = "";
                      String str7 = "linkId";
                      if (p2 instanceof String) {
                         JSONObject jSONObject = new JSONObject(String.valueOf(p2));
                         str5 = jSONObject.optString(str4);
                         str6 = jSONObject.optString(str7);
                      }else if(p2 instanceof Map){
                         map1 = p2;
                         str5 = (map1.containsKey(str4))? map1.get(str4): str6;
                         if (map1.containsKey(str7)) {
                            str6 = map1.get(str7);
                         }
                      }else {
                         url = str6;
                      label_02bf :
                         if (!TextUtils.isEmpty(str6) && !TextUtils.isEmpty(url)) {
                            xru.d().f(url, str6);
                         }
                         v7t.i("UCHA", "linkId: "+url+", url: "+str6);
                      }
                      url = str6;
                      str6 = str5;
                      goto label_02bf ;
                   }else if(p1 == 106){
                      v7t.d("GPU", "page use webgl, url = ["+p0.getUrl()+"]");
                      if (trw.getWvMonitorInterface() != null) {
                         trw.getWvMonitorInterface().commitUseWebgl(p0.getUrl());
                      }
                   }else if(p1 == 21){
                      str6 = "DECODER";
                      if (p2 instanceof String) {
                         v7t.d(str6, "no match img decoder url = ["+p2+"]");
                         y71.commitFail("DecodeImgFailURL", i2, str3, p2);
                      }else {
                         v7t.d(str6, "no match img decoder no param ,webview.url = ["+p0.getUrl()+"]");
                      }
                      ExtImgDecoder.markDecodeError();
                   }else if(p1 == 110){
                      UCInitializerInfo.a().c(10);
                      v7t.d("WVUCClient", "gpu process ready");
                   }
                }
             }
          }
          super.onWebViewEvent(p0, p1, p2);
          return;
       }
    }
}
