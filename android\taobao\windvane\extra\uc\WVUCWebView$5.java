package android.taobao.windvane.extra.uc.WVUCWebView$5;
import android.view.View$OnLongClickListener;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import android.view.View;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import com.uc.webview.export.WebView$HitTestResult;
import com.uc.webview.export.WebView;
import tb.v7t;
import java.lang.StringBuilder;
import android.taobao.windvane.view.PopupWindowController;
import android.content.Context;
import android.view.View$OnClickListener;
import java.lang.Throwable;

public class WVUCWebView$5 implements View$OnLongClickListener	// class@000257 from classes.dex
{
    public final WVUCWebView this$0;
    public static IpChange $ipChange;

    public void WVUCWebView$5(WVUCWebView p0){
       this.this$0 = p0;
       super();
    }
    public boolean onLongClick(View p0){
       WebView$HitTestResult hitTestResul;
       int i = 1;
       int i1 = 0;
       String str = "WVUCWebView";
       IpChange $ipChange = WVUCWebView$5.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("7edba102", objArray).booleanValue();
       }else if((hitTestResul = this.this$0.getHitTestResult()) == null){
          return i1;
       }else if(!WVUCWebView.access$800(this.this$0)){
          return i1;
       }else if(v7t.h()){
          v7t.a(str, "Long click on WebView, "+hitTestResul.getExtra());
       }
       if (hitTestResul.getType() != 8 && hitTestResul.getType() != 5) {
          return i1;
       }else {
          WVUCWebView.access$302(this.this$0, hitTestResul.getExtra());
          WVUCWebView$5 tthis$0 = this.this$0;
          WVUCWebView.access$402(this.this$0, new PopupWindowController(this.this$0._getContext(), tthis$0, WVUCWebView.access$200(tthis$0), WVUCWebView.access$900(this.this$0)));
          WVUCWebView.access$400(this.this$0).i();
          return i;
       }
    }
}
