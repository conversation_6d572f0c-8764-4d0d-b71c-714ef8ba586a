package android.taobao.windvane.extra.performance2.WVPerformance;
import java.lang.String;
import java.lang.Object;

public interface abstract WVPerformance	// class@0001e0 from classes.dex
{

    void end();
    void onResourceFinished(String p0,int p1);
    void onResourceReceivedStatusCode(String p0,int p1);
    void onResourceStarted(String p0);
    void recordProperties(String p0,Object p1);
    void recordStage(String p0,long p1);
    void recordStatistics(String p0,long p1);
    void start();
}
