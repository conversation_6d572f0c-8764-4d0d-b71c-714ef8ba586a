package android.taobao.windvane.jsbridge.WVCallBackContext;
import tb.t2o;
import android.taobao.windvane.webview.IWVWebView;
import java.lang.Object;
import java.lang.String;
import android.taobao.windvane.jsbridge.IJsApiSucceedCallBack;
import android.taobao.windvane.jsbridge.IJsApiFailedCallBack;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.System;
import tb.v7t;
import tb.og8;
import java.lang.CharSequence;
import android.text.TextUtils;
import org.json.JSONObject;
import java.lang.StringBuilder;
import android.taobao.windvane.jsbridge.WVCallBackContext$a;
import java.lang.Runnable;
import java.lang.Throwable;
import tb.lqw;
import tb.kqw;
import tb.yaa;
import tb.nsw;
import java.lang.Boolean;
import tb.r4c;
import tb.q4c;
import tb.vpw;
import tb.wpw;
import tb.urb;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import android.view.View;
import android.os.Looper;
import android.util.Log;
import tb.uqw;
import tb.trw;
import tb.jpw;
import tb.lab;
import java.lang.Class;
import tb.erw;
import java.lang.Integer;

public class WVCallBackContext	// class@0002a8 from classes.dex
{
    public String currentUrlFromAsyncChannel;
    private IJsApiFailedCallBack failedCallBack;
    private String instancename;
    private boolean isUpload;
    private String mAction;
    private String mCallBackContextUrl;
    private boolean mNotiNavtive;
    private String methodname;
    private String objectname;
    private String pid;
    private IJsApiSucceedCallBack succeedCallBack;
    private String token;
    private String uid;
    private IWVWebView webview;
    public WVMegaBridgeContext wvMegaBridgeContext;
    public static IpChange $ipChange;
    private static final String DEFALT_URL;
    private static final String TAG;

    static {
       t2o.a(0x3d8001f2);
    }
    public void WVCallBackContext(IWVWebView p0){
       super();
       this.mNotiNavtive = false;
       this.mAction = null;
       this.pid = "";
       this.uid = "";
       this.isUpload = false;
       this.webview = p0;
       this.safeSetUrl(p0);
    }
    public void WVCallBackContext(IWVWebView p0,String p1,String p2,String p3){
       super();
       this.mNotiNavtive = false;
       this.mAction = null;
       this.pid = "";
       this.uid = "";
       this.isUpload = false;
       this.webview = p0;
       this.token = p1;
       this.objectname = p2;
       this.methodname = p3;
       this.safeSetUrl(p0);
    }
    public void WVCallBackContext(IWVWebView p0,String p1,String p2,String p3,IJsApiSucceedCallBack p4,IJsApiFailedCallBack p5){
       super();
       this.mNotiNavtive = false;
       this.mAction = null;
       this.pid = "";
       this.uid = "";
       this.isUpload = false;
       this.webview = p0;
       this.token = p1;
       this.objectname = p2;
       this.methodname = p3;
       this.failedCallBack = p5;
       this.succeedCallBack = p4;
       this.safeSetUrl(p0);
    }
    private static void callback(IWVWebView p0,String p1,String p2){
       int i = 1;
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2};
          $ipChange.ipc$dispatch("e503504", objArray);
          return;
       }else {
          long l = System.currentTimeMillis();
          if (v7t.h() && (og8.d() && !TextUtils.isEmpty(p2))) {
             try{
                JSONObject jSONObject = new JSONObject(p2);
             }catch(org.json.JSONException e0){
                v7t.d("WVCallBackContext", "return param is not a valid json!\n"+p1+"\n"+p2);
             }
          }
          if (TextUtils.isEmpty(p2)) {
             p2 = "{}";
          }
          p2 = WVCallBackContext.formatJsonString(p2);
          try{
             Object[] objArray1 = new Object[e0];
             objArray1[0] = p2;
             p1 = String.format(p1, objArray1);
             try{
                WVCallBackContext.runOnUiThread(p0, new WVCallBackContext$a(p0, p1, l));
             }catch(java.lang.Exception e7){
                v7t.n("WVCallBackContext", e7.getMessage());
             }
          }catch(java.lang.Exception e7){
             v7t.d("WVCallBackContext", "callback error. "+e7.getMessage());
             return;
          }
       }
    }
    public static void fireEvent(IWVWebView p0,String p1,String p2){
       Object[] objArray;
       int i = 2;
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[]{p0,p1,p2};
          $ipChange.ipc$dispatch("8b044543", objArray);
          return;
       }else {
          v7t.a("WVCallBackContext", "call fireEvent ");
          objArray = new Object[]{p2};
          lqw.d().f(3013, null, p1, objArray);
          String str = (yaa.m)? "\(function\(d\){var n=\'%s\',t=\'%%s\';if\(window.WindVane\){window.WindVane.fireEvent\(n,t,%s\);}else{var e=d.createEvent\(\'HTMLEvents\'\);e.initEvent\(n,!1,!0\);try{e.data=e.param=JSON.parse\(t\)}catch\(i\){e.data=e.param={ret:\'HY_RESULT_PARSE_ERROR\',originValue:t}}d.dispatchEvent\(e\);}}\)\(window.document\)": "window.WindVane && window.WindVane.fireEvent\(\'%s\', \'%%s\', %s\);";
          WVCallBackContext.onLoggerJSEvent(p0, p1, p2);
          Object[] objArray1 = new Object[i];
          objArray1[0] = p1;
          objArray1[1] = null;
          WVCallBackContext.callback(p0, String.format(str, objArray1), p2);
          return;
       }
    }
    private static String formatJsonString(String p0){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("d6f939bf", objArray);
       }else {
          String str = "\x20\x02";
          if (p0.contains(str)) {
             try{
                p0 = p0.replace(str, "\\u2028");
             }catch(java.lang.Exception e0){
             }
          }
          try{
             str = "\x20\x02";
             if (p0.contains(str)) {
                p0 = p0.replace(str, "\\u2029");
             }
             return p0.replace("\\", "\\\\").replace("\'", "\\\'");
          }catch(java.lang.Exception e0){
          }
       }
    }
    private void onCallBack(nsw p0,boolean p1){
       WVCallBackContext tsucceedCall;
       WVCallBackContext tWVCallBackC;
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("2aed9553", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if(p1){
          p0.k();
       }
       boolean b = p0.f();
       String str = p0.m();
       if (p1 && (tsucceedCall = this.succeedCallBack) != null) {
          if (b && tsucceedCall instanceof r4c) {
             tsucceedCall.b(str);
          }else {
             tsucceedCall.succeed(str);
          }
       }
       if (!p1 && (tWVCallBackC = this.failedCallBack) != null) {
          if (b && tWVCallBackC instanceof q4c) {
             tWVCallBackC.a(str);
          }else {
             tWVCallBackC.fail(str);
          }
       }
       return;
    }
    private static void onLoggerJSEvent(IWVWebView p0,String p1,String p2){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2};
          $ipChange.ipc$dispatch("ee71c7e1", objArray);
          return;
       }else if(vpw.commonConfig.c1 != null){
          try{
             String str = "";
             if (p0 instanceof urb) {
                str = p0.getCurId();
             }
             lcn.a(RVLLevel.Info, "JS/Event").j("event").m(str).a("name", p1).a("data", new JSONObject(p2)).f();
          }catch(java.lang.Exception e0){
             v7t.d("WVCallBackContext", "JS Event JOSNObject error failed!");
          }
       }
       return;
    }
    private static void runOnUiThread(IWVWebView p0,Runnable p1){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("da77ba51", objArray);
          return;
       }else if(p0 != null && p0.getView() != null){
          if (Looper.myLooper() == Looper.getMainLooper()) {
             p1.run();
          }else {
             p0._post(p1);
          }
       }
       return;
    }
    private void safeSetUrl(IWVWebView p0){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5f98e545", objArray);
          return;
       }else if(Looper.getMainLooper() == Looper.myLooper() && p0 != null){
          this.mCallBackContextUrl = p0.getUrl();
       }
       return;
    }
    public void commitJsBridgeReturn(String p0){
       WVCallBackContext twebview;
       JSONObject jSONObject1;
       lab olab;
       int i = 0;
       String str = "";
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("79a3f8ea", objArray);
          return;
       }else if(vpw.commonConfig.r0 == null){
          return;
       }else {
          JSONObject jSONObject = new JSONObject(p0);
          String str1 = str+this.objectname+"."+this.methodname;
          WVCallBackContext tinstancenam = this.instancename;
          String str2 = jSONObject.optString("ret", "HY_FAILED_EMPTY");
          String str3 = jSONObject.optString("msg", str);
          String str4 = "UNKNOWN";
          if (Looper.getMainLooper() == Looper.myLooper()) {
             if ((twebview = this.webview) != null) {
                str4 = twebview.getUrl();
             }
          }else if((twebview = this.mCallBackContextUrl) == null){
             str4 = twebview;
          }
          String str5 = str4;
          if (TextUtils.equals(str1, "MtopWVPlugin.send")) {
             twebview = jSONObject.optString("api", "UNKNOWN_MTOP_API");
             if (trw.getWvJsBridgeMonitorInterface() != null) {
                trw.getWvJsBridgeMonitorInterface().onMtopJsBridgeReturn(twebview, str2, str3, str5);
             }
             if ((jSONObject1 = jSONObject.optJSONObject("stat")) == null) {
                return;
             }else if(jSONObject1.optBoolean("isPrefetch", i) && (olab = jpw.c().a(lab.class)) != null){
                olab.e(this.webview.getView(), "H5_Prefetch", Boolean.TRUE);
             }
             return;
          }else if(trw.getJsBridgeMonitor() != null){
             trw.getJsBridgeMonitor().onJsBridgeReturn(str1, tinstancenam, str2, str3, str5);
          }
          return;
       }
    }
    public void commitJsBridgeReturn(nsw p0){
       WVCallBackContext twebview;
       String str = "";
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("83fd954c", objArray);
          return;
       }else if(vpw.commonConfig.r0 == null){
          return;
       }else {
          String str1 = str+this.objectname+"."+this.methodname;
          WVCallBackContext tinstancenam = this.instancename;
          String str2 = p0.e("ret", "HY_FAILED_EMPTY");
          String str3 = p0.e("msg", str);
          String str4 = "UNKNOWN";
          if (Looper.getMainLooper() == Looper.myLooper()) {
             if ((twebview = this.webview) != null) {
                str4 = twebview.getUrl();
             }
          }else if((twebview = this.mCallBackContextUrl) == null){
             str4 = twebview;
          }
          String str5 = str4;
          if (TextUtils.equals(str1, "MtopWVPlugin.send")) {
             String str6 = p0.e("api", "UNKNOWN_MTOP_API");
             if (trw.getWvJsBridgeMonitorInterface() != null) {
                trw.getWvJsBridgeMonitorInterface().onMtopJsBridgeReturn(str6, str2, str3, str5);
             }
             return;
          }else if(trw.getJsBridgeMonitor() != null){
             trw.getJsBridgeMonitor().onJsBridgeReturn(str1, tinstancenam, str2, str3, str5);
          }
          return;
       }
    }
    public void error(){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("1392128f", objArray);
          return;
       }else {
          this.error("{}");
          return;
       }
    }
    public void error(String p0){
       Object[] objArray;
       WVCallBackContext tWVCallBackC;
       int i = 2;
       int i1 = 1;
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i];
          objArray[0] = this;
          objArray[i1] = p0;
          $ipChange.ipc$dispatch("72e35699", objArray);
          return;
       }else {
          v7t.d("WVCallBackContext", "call error, ret = ["+p0+"]");
          if ((tWVCallBackC = this.failedCallBack) != null) {
             tWVCallBackC.fail(p0);
             if (this.isUpload == null) {
                this.commitJsBridgeReturn(p0);
             }
             return;
          }else if(this.mNotiNavtive != null){
             String str = "UNKNOWN";
             if (Looper.getMainLooper() == Looper.myLooper()) {
                if ((tWVCallBackC = this.webview) != null) {
                   str = tWVCallBackC.getUrl();
                }
             }else if((tWVCallBackC = this.mCallBackContextUrl) == null){
                str = tWVCallBackC;
             }
             objArray = new Object[i];
             objArray[0] = this.mAction;
             objArray[i1] = p0;
             lqw.d().f(3012, null, str, objArray);
             this.mNotiNavtive = false;
             this.mAction = null;
          }
          Integer.valueOf(this.token);
          String str1 = "javascript:window.WindVane&&window.WindVane.onFailure\(%s,\'%%s\'\);";
          Object[] objArray1 = new Object[i1];
          objArray1[0] = this.token;
          WVCallBackContext.callback(this.webview, String.format(str1, objArray1), p0);
          if (this.isUpload == null) {
             this.commitJsBridgeReturn(p0);
          }
          return;
       }
    }
    public void error(String p0,String p1){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4a936c23", objArray);
          return;
       }else {
          nsw onsw = new nsw("HY_FAILED");
          onsw.b(p0, p1);
          this.error(onsw);
          return;
       }
    }
    public void error(nsw p0){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5660aa7d", objArray);
          return;
       }else if(p0 != null){
          this.error(p0.m());
          this.commitJsBridgeReturn(p0);
       }
       return;
    }
    public void fireEvent(String p0){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("35b21d95", objArray);
          return;
       }else {
          this.fireEvent(p0, "{}");
          return;
       }
    }
    public void fireEvent(String p0,String p1){
       Object[] objArray;
       int i = 3;
       int i1 = 2;
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i];
          objArray[0] = this;
          objArray[1] = p0;
          objArray[i1] = p1;
          $ipChange.ipc$dispatch("7134ec1f", objArray);
          return;
       }else {
          v7t.a("WVCallBackContext", "call fireEvent, eventName = ["+p0+"], eventParam = ["+p1+"]");
          objArray = new Object[i];
          objArray[0] = this.mAction;
          objArray[1] = p0;
          objArray[i1] = p1;
          lqw.d().g(3013, objArray);
          String str = (yaa.m)? "\(function\(d\){var n=\'%s\',t=\'%%s\';if\(window.WindVane\){window.WindVane.fireEvent\(n,t,%s\);}else{var e=d.createEvent\(\'HTMLEvents\'\);e.initEvent\(n,!1,!0\);try{e.data=e.param=JSON.parse\(t\)}catch\(i\){e.data=e.param={ret:\'HY_RESULT_PARSE_ERROR\',originValue:t}}d.dispatchEvent\(e\);}}\)\(window.document\)": "window.WindVane && window.WindVane.fireEvent\(\'%s\', \'%%s\', %s\);";
          WVCallBackContext.onLoggerJSEvent(this.getWebview(), p0, p1);
          Object[] objArray1 = new Object[i1];
          objArray1[0] = p0;
          objArray1[1] = null;
          WVCallBackContext.callback(this.webview, String.format(str, objArray1), p1);
          return;
       }
    }
    public String getPid(){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.pid;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("229b03c4", objArray);
    }
    public String getToken(){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.token;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("a592a696", objArray);
    }
    public String getUid(){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.uid;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("ea2ce1f", objArray);
    }
    public IWVWebView getWebview(){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.webview;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("166e5630", objArray);
    }
    public void onFailure(nsw p0){
       int i = 0;
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4c5c5b7a", objArray);
          return;
       }else {
          this.onCallBack(p0, i);
          return;
       }
    }
    public void onSuccess(nsw p0){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("43640fe1", objArray);
          return;
       }else {
          this.onCallBack(p0, 1);
          return;
       }
    }
    public void setInstancename(String p0){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("7dcd5f33", objArray);
          return;
       }else {
          this.instancename = p0;
          return;
       }
    }
    public void setNeedfireNativeEvent(String p0,boolean p1){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("511a412e", objArray);
          return;
       }else {
          this.mAction = p0;
          this.mNotiNavtive = p1;
          v7t.d("WVCallBackContext", "setNeedfireNativeEvent : "+p0);
          return;
       }
    }
    public void setPid(String p0){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("1f52579a", objArray);
          return;
       }else {
          this.pid = p0;
          return;
       }
    }
    public void setToken(String p0){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("b99ac208", objArray);
          return;
       }else {
          this.token = p0;
          return;
       }
    }
    public void setUid(String p0){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("b443d89f", objArray);
          return;
       }else {
          this.uid = p0;
          return;
       }
    }
    public void setWebview(IWVWebView p0){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4a5f70e2", objArray);
          return;
       }else {
          this.webview = p0;
          return;
       }
    }
    public void success(){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("b4550b0a", objArray);
          return;
       }else {
          this.success(nsw.RET_SUCCESS);
          return;
       }
    }
    public void success(String p0){
       Object[] objArray;
       WVCallBackContext tsucceedCall;
       int i = 2;
       int i1 = 1;
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i];
          objArray[0] = this;
          objArray[i1] = p0;
          $ipChange.ipc$dispatch("6b54654", objArray);
          return;
       }else {
          v7t.i("WVCallBackContext", "call method success, msg = "+p0);
          if ((tsucceedCall = this.succeedCallBack) != null) {
             tsucceedCall.succeed(p0);
             if (this.isUpload == null) {
                this.commitJsBridgeReturn(p0);
             }
             return;
          }else if(this.mNotiNavtive != null){
             String str = "UNKNOWN";
             if (Looper.getMainLooper() == Looper.myLooper()) {
                if ((tsucceedCall = this.webview) != null) {
                   str = tsucceedCall.getUrl();
                }
             }else if((tsucceedCall = this.mCallBackContextUrl) == null){
                str = tsucceedCall;
             }
             objArray = new Object[i];
             objArray[0] = this.mAction;
             objArray[i1] = p0;
             lqw.d().f(3011, null, str, objArray);
             this.mNotiNavtive = false;
             this.mAction = null;
          }
          Integer.valueOf(this.token);
          String str1 = "javascript:window.WindVane&&window.WindVane.onSuccess\(%s,\'%%s\'\);";
          Object[] objArray1 = new Object[i1];
          objArray1[0] = this.token;
          WVCallBackContext.callback(this.webview, String.format(str1, objArray1), p0);
          if (this.isUpload == null) {
             this.commitJsBridgeReturn(p0);
          }
          return;
       }
    }
    public void success(nsw p0){
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3e095322", objArray);
          return;
       }else if(p0 != null){
          p0.k();
          this.isUpload = true;
          this.success(p0.m());
          this.commitJsBridgeReturn(p0);
       }
       return;
    }
    public void successAndKeepAlive(String p0){
       WVCallBackContext tsucceedCall;
       IpChange $ipChange = WVCallBackContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("310f3b65", objArray);
          return;
       }else {
          v7t.i("WVCallBackContext", "call success and keep alive, msg = "+p0);
          if ((tsucceedCall = this.succeedCallBack) != null && tsucceedCall instanceof r4c) {
             tsucceedCall.b(p0);
             return;
          }else {
             WVCallBackContext.callback(this.webview, "javascript:window.WindVane&&window.WindVane.onSuccess\(\'"+this.token+"\',\'%s\', true\);", p0);
             return;
          }
       }
    }
}
