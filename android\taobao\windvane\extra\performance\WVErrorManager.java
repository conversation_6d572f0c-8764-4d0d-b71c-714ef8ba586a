package android.taobao.windvane.extra.performance.WVErrorManager;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.bka;
import tb.aka;
import java.lang.StringBuilder;
import tb.v7t;
import tb.bsw;
import tb.asw;
import java.lang.Math;
import java.lang.Double;
import tb.vpw;
import tb.wpw;
import tb.sqw;

public class WVErrorManager	// class@0001ca from classes.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x3d8000ff);
    }
    public void WVErrorManager(){
       super();
    }
    public void reportJSError(String p0,String p1,String p2,String p3,boolean p4){
       String str;
       IpChange $ipChange = WVErrorManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3,new Boolean(p4)};
          $ipChange.ipc$dispatch("37294e5a", objArray);
          return;
       }else if(!TextUtils.isEmpty(p0) && bka.c().b(p0) != null){
          v7t.a("WVErrorManager", "found grey page: "+p0);
       }
       if ((str = bsw.b().a().b()) != null && (Double.parseDouble(str) - Math.random()) >= 0) {
          return;
       }else if(vpw.commonConfig.h0 != null){
          if (p4) {
             if (!TextUtils.isEmpty(p1) || !TextUtils.isEmpty(p2)) {
                sqw.b("WINDVANE_JS_ERROR", p0, p2, p1, p3, p0);
                v7t.d("WVErrorManager", "isUCWebview upload jserror message:"+p1+" stack:"+p2);
             }
          }else {
             sqw.b("WINDVANE_JS_ERROR", p0, p2, p1, p3, p0);
             v7t.d("WVErrorManager", "notUCWebview upload jserror errorMessage:"+p1);
          }
       }else {
          sqw.b("WINDVANE_JS_ERROR", p0, p2, p1, p3, p0);
       }
       return;
    }
}
