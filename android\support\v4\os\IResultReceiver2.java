package android.support.v4.os.IResultReceiver2;
import android.os.IInterface;
import java.lang.String;
import android.os.Bundle;

public interface abstract IResultReceiver2 implements IInterface	// class@000136 from classes.dex
{
    public static final String DESCRIPTOR;

    static {
       IResultReceiver2.DESCRIPTOR = "android$support$v4$os$IResultReceiver2".replace('$', '.');
    }
    void send(int p0,Bundle p1);
}
