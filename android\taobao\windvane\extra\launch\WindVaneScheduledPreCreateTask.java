package android.taobao.windvane.extra.launch.WindVaneScheduledPreCreateTask;
import android.taobao.windvane.extra.launch.InitOnceTask;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.app.Application;
import java.util.HashMap;
import com.android.alibaba.ip.runtime.IpChange;
import android.taobao.windvane.extra.uc.pool.IWebViewPool;
import android.taobao.windvane.extra.uc.pool.WebViewPoolProvider;

public class WindVaneScheduledPreCreateTask extends InitOnceTask	// class@0001c2 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000f7);
    }
    public void WindVaneScheduledPreCreateTask(){
       super();
    }
    public static Object ipc$super(WindVaneScheduledPreCreateTask p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/launch/WindVaneScheduledPreCreateTask");
    }
    public void initImpl(Application p0,HashMap p1){
       int i = 0;
       IpChange $ipChange = WindVaneScheduledPreCreateTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("73f761cb", objArray);
          return;
       }else {
          WebViewPoolProvider.getWebViewPool().requestCreate(i);
          return;
       }
    }
}
