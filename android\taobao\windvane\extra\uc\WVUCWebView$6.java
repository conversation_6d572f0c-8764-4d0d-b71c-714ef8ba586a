package android.taobao.windvane.extra.uc.WVUCWebView$6;
import android.view.View$OnTouchListener;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import android.view.View;
import android.view.MotionEvent;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import android.taobao.windvane.extra.uc.WVUCWebView$6$1;
import android.taobao.windvane.extra.performance2.WVFSPManager$CompletionHandler;
import android.taobao.windvane.extra.performance2.WVFSPManager;

public class WVUCWebView$6 implements View$OnTouchListener	// class@000259 from classes.dex
{
    public final WVUCWebView this$0;
    public static IpChange $ipChange;

    public void WVUCWebView$6(WVUCWebView p0){
       this.this$0 = p0;
       super();
    }
    public boolean onTouch(View p0,MotionEvent p1){
       int i = 0;
       IpChange $ipChange = WVUCWebView$6.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("d4aa3aa4", objArray).booleanValue();
       }else if(p1.getAction()){
          this.this$0.wvfspManager.unitDidFinish(new WVUCWebView$6$1(this));
       }
       return i;
    }
}
