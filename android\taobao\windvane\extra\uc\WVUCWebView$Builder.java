package android.taobao.windvane.extra.uc.WVUCWebView$Builder;
import tb.t2o;
import java.lang.System;
import android.os.SystemClock;
import java.lang.Object;
import android.content.Context;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Number;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.util.concurrent.atomic.AtomicBoolean;
import tb.v7t;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.taobao.windvane.export.prerender.PrerenderManager;
import tb.uum;
import tb.r9u;
import tb.vpw;
import tb.wpw;
import tb.bl3;
import android.taobao.windvane.extra.uc.pool.IWebViewPool;
import android.taobao.windvane.extra.uc.pool.WebViewPoolProvider;
import android.taobao.windvane.extra.uc.pool.WebViewPool;
import android.taobao.windvane.extra.core.WVCore;
import android.taobao.windvane.extra.uc.WVUCWebView$TouchEventHandler;
import android.view.View;
import tb.tc;
import tb.ace;
import tb.cce;
import java.lang.Long;
import java.lang.Boolean;
import java.lang.IllegalStateException;
import java.lang.Integer;
import tb.c40;

public final class WVUCWebView$Builder	// class@00025e from classes.dex
{
    private boolean enablePrerender;
    private Context mContext;
    private int mCoreType;
    private boolean mEnableAsyncJSAPIChannel;
    private boolean mEnablePreCreate;
    private boolean mEnableReportAPM;
    private String mPid;
    private WVUCWebView$TouchEventHandler mTouchEventHandler;
    private cce mWebViewPageModel;
    private String realUrl;
    public static IpChange $ipChange;
    private static final long TIMESTAMP_DELTA;

    static {
       t2o.a(0x3d800193);
       WVUCWebView$Builder.TIMESTAMP_DELTA = System.currentTimeMillis() - SystemClock.uptimeMillis();
    }
    public void WVUCWebView$Builder(){
       super();
       this.mEnablePreCreate = false;
       this.mTouchEventHandler = null;
       this.enablePrerender = false;
       this.realUrl = null;
       this.mPid = null;
       this.mEnableReportAPM = true;
       this.mWebViewPageModel = null;
       this.mEnableAsyncJSAPIChannel = false;
    }
    public static Context access$000(WVUCWebView$Builder p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mContext;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("c1405f59", objArray);
    }
    public static int access$100(WVUCWebView$Builder p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mCoreType;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("52e573d5", objArray).intValue();
    }
    public WVUCWebView build(){
       WVUCWebView wVUCWebView;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("6f296a2", objArray);
       }else if(this.mContext != null){
          long l = SystemClock.uptimeMillis();
          String str = "WVUCWebView";
          if (WVUCWebView.access$1200() && (!WVUCWebView.access$1300().get() && this.mCoreType == 3)) {
             v7t.d(str, "call staticInitializeOnce in WVUCWebView.Builder.build");
             WVUCWebView.staticInitializeOnce();
          }
          if (this.enablePrerender != null && (!TextUtils.isEmpty(this.realUrl) && (wVUCWebView = PrerenderManager.INSTANCE.b(new uum(this.mContext, this.realUrl, i1))) != null)) {
             return wVUCWebView;
          }else {
             r9u.b("buildWebView");
             WVUCWebView wVUCWebView1 = null;
             if (vpw.commonConfig.V1 != null && (this.mEnablePreCreate != null && this.mCoreType == 3)) {
                if (this.mContext != null && this.realUrl != null) {
                   wVUCWebView1 = PrerenderManager.INSTANCE.b(new uum(this.mContext, this.realUrl, 2));
                }
                if (wVUCWebView1 == null) {
                   if (bl3.a(this.mContext)) {
                      wVUCWebView1 = WebViewPoolProvider.getWebViewPool().poll(this.mContext);
                   }else {
                      wVUCWebView1 = WebViewPool.acquirePreCreateWebView(this.mContext);
                   }
                }
             }
             if (wVUCWebView1 == null) {
                if (!WVCore.getInstance().isUCSupport() && this.mCoreType == 3) {
                   v7t.n(str, "webview is initializing during its creation");
                }
                r9u.b("createWebView");
                wVUCWebView1 = new WVUCWebView(this);
                r9u.d();
             }
             wVUCWebView1.setTouchEventHandler(this.mTouchEventHandler);
             if (this.mEnableReportAPM == null) {
                tc.c(wVUCWebView1, i);
             }
             if (this.mWebViewPageModel != null) {
                wVUCWebView1.getWebViewContext().addWebViewPageModel(this.mWebViewPageModel);
             }
             if (this.mEnableAsyncJSAPIChannel != null && wVUCWebView1.isCurrentU4()) {
                WVUCWebView.access$1400(wVUCWebView1);
                wVUCWebView1.getWebViewContext().setEnableAsyncJSAPIChannel(i1);
             }
             if (!TextUtils.isEmpty(this.mPid)) {
                wVUCWebView1.setPid(this.mPid);
             }
             long l1 = SystemClock.uptimeMillis();
             try{
                wVUCWebView1.getWebViewContext().getWebViewPageModel().onStage("H5_webViewCreateStart", l);
                wVUCWebView1.getWebViewContext().getWebViewPageModel().onStage("H5_webViewCreateEnd", l1);
                wVUCWebView1.getWebViewContext().getWebViewPageModel().onProperty("H5_timestampDelta", Long.valueOf(WVUCWebView$Builder.TIMESTAMP_DELTA));
                wVUCWebView1.getWebViewContext().getWebViewPageModel().onPropertyIfAbsent("wvEnableAsyncJSAPIChannel", Boolean.valueOf(this.mEnableAsyncJSAPIChannel));
                r9u.d();
                return wVUCWebView1;
             }catch(java.lang.Exception e0){
             }
          }
       }else {
          throw new IllegalStateException("create WVUCWebView with null context");
       }
    }
    public WVUCWebView$Builder setContext(Context p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("f43383ac", objArray);
       }else {
          this.mContext = p0;
          return this;
       }
    }
    public WVUCWebView$Builder setCoreType(int p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          return $ipChange.ipc$dispatch("dfc9e4df", objArray);
       }else {
          this.mCoreType = p0;
          return this;
       }
    }
    public WVUCWebView$Builder setEnableAsyncJSAPIChannel(boolean p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          return $ipChange.ipc$dispatch("5c3b85c", objArray);
       }else {
          this.mEnableAsyncJSAPIChannel = p0;
          return this;
       }
    }
    public WVUCWebView$Builder setEnablePreCreate(boolean p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          return $ipChange.ipc$dispatch("13eff16d", objArray);
       }else {
          this.mEnablePreCreate = p0;
          return this;
       }
    }
    public WVUCWebView$Builder setEnablePrerender(boolean p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          return $ipChange.ipc$dispatch("ce0b3967", objArray);
       }else {
          this.enablePrerender = p0;
          return this;
       }
    }
    public WVUCWebView$Builder setEnableReportAPM(boolean p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          return $ipChange.ipc$dispatch("6e508078", objArray);
       }else {
          this.mEnableReportAPM = p0;
          return this;
       }
    }
    public WVUCWebView$Builder setPid(String p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("3cddcd26", objArray);
       }else {
          this.mPid = p0;
          return this;
       }
    }
    public WVUCWebView$Builder setRealUrl(String p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ba10fb00", objArray);
       }else {
          this.realUrl = p0;
          return this;
       }
    }
    public WVUCWebView$Builder setTouchEventHandler(WVUCWebView$TouchEventHandler p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("7124f6f6", objArray);
       }else {
          this.mTouchEventHandler = p0;
          return this;
       }
    }
    public WVUCWebView$Builder setWebViewPageModel(c40 p0){
       IpChange $ipChange = WVUCWebView$Builder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("a0262359", objArray);
       }else {
          this.mWebViewPageModel = p0;
          return this;
       }
    }
}
