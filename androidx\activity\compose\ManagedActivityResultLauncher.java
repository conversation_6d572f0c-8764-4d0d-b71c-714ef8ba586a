package androidx.activity.compose.ManagedActivityResultLauncher;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.compose.ActivityResultLauncherHolder;
import tb.xf40;
import androidx.activity.result.contract.ActivityResultContract;
import java.lang.Object;
import androidx.core.app.ActivityOptionsCompat;
import java.lang.UnsupportedOperationException;
import java.lang.String;

public final class ManagedActivityResultLauncher extends ActivityResultLauncher	// class@000489 from classes.dex
{
    private final xf40 currentContract;
    private final ActivityResultLauncherHolder launcher;
    public static final int $stable = 8;

    public void ManagedActivityResultLauncher(ActivityResultLauncherHolder p0,xf40 p1){
       super();
       this.launcher = p0;
       this.currentContract = p1;
    }
    public ActivityResultContract getContract(){
       return this.currentContract.getValue();
    }
    public void launch(Object p0,ActivityOptionsCompat p1){
       this.launcher.launch(p0, p1);
    }
    public void unregister(){
       throw new UnsupportedOperationException("Registration is automatically handled by rememberLauncherForActivityResult");
    }
}
