package android.taobao.windvane.extra.uc.WVNetworkDelegate;
import com.uc.webview.export.extension.INetworkDelegate;
import tb.t2o;
import java.util.concurrent.atomic.AtomicBoolean;
import android.content.Context;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.extra.uc.ExtImgDecoder;
import tb.v7t;
import tb.y71;
import java.lang.CharSequence;
import android.net.Uri;
import android.text.TextUtils;
import com.uc.webview.export.WebView;
import com.uc.webview.export.extension.INetworkDelegate$IRequestData;
import tb.x74;
import android.view.View;
import android.taobao.windvane.extra.uc.interfaces.IRequestTiming;
import android.taobao.windvane.extra.performance.WVH5PPManager;
import java.util.Map;
import tb.yvj;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;
import java.util.HashMap;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;

public class WVNetworkDelegate extends INetworkDelegate	// class@000231 from classes.dex
{
    public static IpChange $ipChange;
    private static final String TAG;
    private static final AtomicBoolean sHasDowngraded;

    static {
       t2o.a(0x3d800166);
       WVNetworkDelegate.sHasDowngraded = new AtomicBoolean(false);
    }
    public void WVNetworkDelegate(Context p0){
       super();
    }
    public static Object ipc$super(WVNetworkDelegate p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/WVNetworkDelegate");
    }
    private static boolean shouldModifyAcceptHeader(String p0){
       Uri uri;
       int i = 1;
       IpChange $ipChange = WVNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("f122c3b7", objArray).booleanValue();
       }else if(p0 == null){
          return 0;
       }else if(vpw.commonConfig.j3 != null && ExtImgDecoder.getDecodeErrorCount() > 5){
          if (WVNetworkDelegate.sHasDowngraded.compareAndSet(0, i)) {
             v7t.d("WindVane/Image", "downgrade image decoder");
             y71.commitSuccess("WVConsecutiveDecodeError", "");
          }
          return 0;
       }else if(p0.contains("/O1CN")){
          return i;
       }else if((uri = Uri.parse(p0)) != null){
          uri = uri.getHost();
          if (TextUtils.equals("gw.alicdn.com", uri) || TextUtils.equals("img.alicdn.com", uri)) {
             return i;
          }
       }
       return e0;
    }
    public void onBeforeSendRequest(WebView p0,INetworkDelegate$IRequestData p1){
       Map$Entry uEntry;
       CharSequence uCharSequenc1;
       IpChange $ipChange = WVNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("288e18cf", objArray);
          return;
       }else if(p1 == null){
          return;
       }else {
          String url = p1.getUrl();
          if (!x74.m(url)) {
             return;
          }
          wpw commonConfig = vpw.commonConfig;
          CharSequence uCharSequenc = null;
          IRequestTiming iRequestTimi = (commonConfig.B3 != null)? WVH5PPManager.openRequestTiming(p0, url): uCharSequenc;
          if (iRequestTimi != null) {
             iRequestTimi.markNativeDelegateBeforeSendRequestStart();
          }
          if (!WVNetworkDelegate.shouldModifyAcceptHeader(url)) {
             if (iRequestTimi != null) {
                iRequestTimi.markNativeDelegateBeforeSendRequestEnd();
             }
             return;
          }else if(!ExtImgDecoder.getInstance().canExtImgDecoder()){
             if (iRequestTimi != null) {
                iRequestTimi.markNativeDelegateBeforeSendRequestEnd();
             }
             return;
          }else {
             Map headers = p1.getHeaders();
             String str = "WindVane/Image";
             if (yvj.a(headers)) {
                if (iRequestTimi != null) {
                   iRequestTimi.markNativeDelegateBeforeSendRequestEnd();
                }
                lcn.f(RVLLevel.Error, str, "main document request, skip adding accept: "+url);
                return;
             }else {
                commonConfig = commonConfig.r1;
                boolean b = ExtImgDecoder.getInstance().useAlphaChannelDecoder();
                if (commonConfig != null || b) {
                   v7t.d(str, "add heic accept "+commonConfig+" "+b+" "+url);
                   if (headers == null) {
                      headers = new HashMap();
                   }
                   Iterator iterator = headers.entrySet().iterator();
                   while (true) {
                      if (iterator.hasNext()) {
                         if ((uEntry = iterator.next()) != null && ((str = uEntry.getKey()) != null && str.equalsIgnoreCase("accept"))) {
                            uCharSequenc = uEntry.getKey();
                            uCharSequenc1 = uEntry.getValue();
                         }
                      }else {
                         uCharSequenc1 = uCharSequenc;
                      }
                      if (TextUtils.isEmpty(uCharSequenc)) {
                         uCharSequenc = "Accept";
                      }
                      if (commonConfig != null) {
                         uCharSequenc1 = (TextUtils.isEmpty(uCharSequenc1))? "image/heic": "image/heic,"+uCharSequenc1;
                      }
                      if (b) {
                         uCharSequenc1 = (TextUtils.isEmpty(uCharSequenc1))? "image/heia": "image/heia,"+uCharSequenc1;
                      }
                      p1.setHeader(uCharSequenc, uCharSequenc1);
                   }
                }
                if (iRequestTimi != null) {
                   iRequestTimi.markNativeDelegateBeforeSendRequestEnd();
                }
                return;
             }
          }
       }
    }
}
