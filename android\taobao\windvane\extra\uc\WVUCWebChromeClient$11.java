package android.taobao.windvane.extra.uc.WVUCWebChromeClient$11;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.WVUCWebChromeClient;
import tb.xee;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;

public class WVUCWebChromeClient$11 implements Runnable	// class@000241 from classes.dex
{
    public final WVUCWebChromeClient this$0;
    public final String val$consoleMsg;
    public final String val$url;
    public final xee val$zCacheAdapter;
    public static IpChange $ipChange;

    public void WVUCWebChromeClient$11(WVUCWebChromeClient p0,xee p1,String p2,String p3){
       this.this$0 = p0;
       this.val$zCacheAdapter = p1;
       this.val$url = p2;
       this.val$consoleMsg = p3;
       super();
    }
    public void run(){
       IpChange $ipChange = WVUCWebChromeClient$11.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          this.val$zCacheAdapter.clearResource(this.val$url, "WindVane", this.val$consoleMsg);
          return;
       }
    }
}
