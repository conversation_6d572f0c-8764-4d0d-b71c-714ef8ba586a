package androidx.activity.contextaware.ContextAwareKt;
import androidx.activity.contextaware.ContextAware;
import tb.g1a;
import tb.ar4;
import java.lang.Object;
import android.content.Context;
import kotlinx.coroutines.c;
import kotlin.coroutines.intrinsics.IntrinsicsKt__IntrinsicsJvmKt;
import androidx.activity.contextaware.ContextAwareKt$withContextAvailable$2$listener$1;
import tb.q23;
import androidx.activity.contextaware.OnContextAvailableListener;
import androidx.activity.contextaware.ContextAwareKt$withContextAvailable$2$1;
import tb.dkf;
import tb.jv6;
import tb.xhv;

public final class ContextAwareKt	// class@0004a6 from classes.dex
{

    public static final Object withContextAvailable(ContextAware p0,g1a p1,ar4 p2){
       Context uContext;
       if ((uContext = p0.peekAvailableContext()) != null) {
          return p1.invoke(uContext);
       }
       c uoc = new c(IntrinsicsKt__IntrinsicsJvmKt.c(p2), 1);
       uoc.E();
       ContextAwareKt$withContextAvailable$2$listener$1 owithContext = new ContextAwareKt$withContextAvailable$2$listener$1(uoc, p1);
       p0.addOnContextAvailableListener(owithContext);
       uoc.h(new ContextAwareKt$withContextAvailable$2$1(p0, owithContext));
       if ((p0 = uoc.A()) == dkf.d()) {
          jv6.c(p2);
       }
       return p0;
    }
    private static final Object withContextAvailable$$forInline(ContextAware p0,g1a p1,ar4 p2){
       Context uContext;
       if ((uContext = p0.peekAvailableContext()) != null) {
          return p1.invoke(uContext);
       }
       c uoc = new c(IntrinsicsKt__IntrinsicsJvmKt.c(p2), 1);
       uoc.E();
       ContextAwareKt$withContextAvailable$2$listener$1 owithContext = new ContextAwareKt$withContextAvailable$2$listener$1(uoc, p1);
       p0.addOnContextAvailableListener(owithContext);
       uoc.h(new ContextAwareKt$withContextAvailable$2$1(p0, owithContext));
       if ((p0 = uoc.A()) == dkf.d()) {
          jv6.c(p2);
       }
       return p0;
    }
}
