package android.taobao.windvane.export.prerender.PrerenderManager$preRender$1$1$1$a;
import java.lang.Runnable;
import android.taobao.windvane.export.prerender.PrerenderManager$preRender$1$1$1;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.export.prerender.PrerenderManager;
import java.util.List;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;

public final class PrerenderManager$preRender$1$1$1$a implements Runnable	// class@000177 from classes.dex
{
    public final PrerenderManager$preRender$1$1$1 a;
    public static IpChange $ipChange;

    public void PrerenderManager$preRender$1$1$1$a(PrerenderManager$preRender$1$1$1 p0){
       this.a = p0;
       super();
    }
    public final void run(){
       IpChange $ipChange = PrerenderManager$preRender$1$1$1$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(PrerenderManager.a(PrerenderManager.INSTANCE).remove(this.a.$prerenderItem)){
          lcn.f(RVLLevel.Info, "Themis/Performance/Prerender", "prerender webview expired");
       }
       return;
    }
}
