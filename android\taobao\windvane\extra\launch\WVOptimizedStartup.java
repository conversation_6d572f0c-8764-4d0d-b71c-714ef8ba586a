package android.taobao.windvane.extra.launch.WVOptimizedStartup;
import tb.t2o;
import java.util.concurrent.atomic.AtomicBoolean;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.app.Application;
import tb.vpw;
import tb.wpw;
import tb.jpw;
import tb.l8e;
import java.lang.Class;
import java.lang.StringBuilder;
import tb.v7t;
import android.taobao.windvane.extra.config.TBConfigManager;
import android.content.Context;
import tb.xg4;
import tb.btw;
import tb.eqw;
import tb.ypw;
import tb.xsw;
import android.taobao.windvane.config.WVConfigManager;
import tb.ipb;
import android.taobao.windvane.extra.jsbridge.JSAPIManager;
import tb.gqw;
import android.taobao.windvane.extra.launch.WVOptimizedStartup$Params;
import java.lang.Boolean;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;
import android.taobao.windvane.extra.core.WVCore;
import tb.icn;
import tb.xae;
import tb.mrt;
import android.taobao.windvane.extra.launch.WVOptimizedStartup$1;
import java.lang.Runnable;
import android.taobao.windvane.extra.launch.WVOptimizedStartup$2;
import android.taobao.windvane.extra.launch.WVOptimizedStartup$3;
import tb.gvm;

public class WVOptimizedStartup	// class@0001b9 from classes.dex
{
    public static IpChange $ipChange;
    private static final String TAG;
    private static final AtomicBoolean sInitialized;

    static {
       t2o.a(0x3d8000ea);
       WVOptimizedStartup.sInitialized = new AtomicBoolean(false);
    }
    public void WVOptimizedStartup(){
       super();
    }
    public static void access$000(){
       IpChange $ipChange = WVOptimizedStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("bcd60777", objArray);
          return;
       }else {
          WVOptimizedStartup.disableVerifyClass();
          return;
       }
    }
    public static void access$200(Application p0){
       IpChange $ipChange = WVOptimizedStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("77ec9306", objArray);
          return;
       }else {
          WVOptimizedStartup.initConfig(p0);
          return;
       }
    }
    public static void access$300(){
       IpChange $ipChange = WVOptimizedStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("5b88b93a", objArray);
          return;
       }else {
          WVOptimizedStartup.initJSAPIAndEmbed();
          return;
       }
    }
    private static void disableVerifyClass(){
       wpw b2;
       l8e ol8e;
       IpChange $ipChange = WVOptimizedStartup.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          $ipChange.ipc$dispatch("e358297e", objArray);
          return;
       }else if((b2 = vpw.commonConfig.B2) != null && (ol8e = jpw.c().a(l8e.class)) != null){
          i = ol8e.a();
       }
       v7t.d("WindVane/PreStartUp", "disableVerifySwitchEnabled: "+b2+", verifyDisabled: "+i);
       return;
    }
    private static void initConfig(Application p0){
       IpChange $ipChange = WVOptimizedStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("ce180d06", objArray);
          return;
       }else {
          TBConfigManager.getInstance().initEarly(p0);
          xg4.k();
          vpw.b().d();
          btw.c().e();
          eqw.c().e();
          ypw.b().d();
          xsw.b().d();
          WVConfigManager.a().b("windvane_common", vpw.b());
          WVConfigManager.a().b("windvane_domain", eqw.c());
          WVConfigManager.a().b("WindVane_URL_config", btw.c());
          WVConfigManager.a().b("cookie_black_list", ypw.b());
          WVConfigManager.a().b("windvane_uc_core", xsw.b());
          TBConfigManager.getInstance().initConfig();
          return;
       }
    }
    private static void initJSAPIAndEmbed(){
       IpChange $ipChange = WVOptimizedStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("7a7f2a6a", objArray);
          return;
       }else {
          JSAPIManager.getInstance().register();
          gqw.a();
          return;
       }
    }
    private static boolean isParamsValid(WVOptimizedStartup$Params p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = WVOptimizedStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("ab4a61ea", objArray).booleanValue();
       }else if(p0 != null && WVOptimizedStartup$Params.access$100(p0) != null){
          i = true;
       }
       return i;
    }
    public static void startup(WVOptimizedStartup$Params p0){
       boolean i = 0;
       int i1 = 1;
       IpChange $ipChange = WVOptimizedStartup.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          $ipChange.ipc$dispatch("60dff557", objArray);
          return;
       }else {
          AtomicBoolean sInitialized = WVOptimizedStartup.sInitialized;
          if (sInitialized.get()) {
             return;
          }
          if (!WVOptimizedStartup.isParamsValid(p0)) {
             lcn.f(RVLLevel.Error, "WindVane/PreStartUp", "params is invalid");
             return;
          }else if(sInitialized.compareAndSet(i, i1)){
             i = WVCore.getInstance().isUCStartInit();
             lcn.a(RVLLevel.Info, "WindVane/PreStartUp").j("startup").a("isInitStart", Boolean.valueOf(i)).f();
             if (i) {
                return;
             }else {
                mrt.a().execute(new WVOptimizedStartup$1());
                mrt.a().execute(new WVOptimizedStartup$2());
                mrt.a().execute(new WVOptimizedStartup$3(p0));
                gvm.a(WVOptimizedStartup$Params.access$100(p0));
             }
          }
          return;
       }
    }
}
