package androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$3$1$invoke$$inlined$onDispose$1;
import tb.gi20;
import androidx.activity.compose.PredictiveBackHandlerCallback;
import java.lang.Object;
import androidx.activity.OnBackPressedCallback;

public final class PredictiveBackHandlerKt$PredictiveBackHandler$3$1$invoke$$inlined$onDispose$1 implements gi20	// class@00048f from classes.dex
{
    public final PredictiveBackHandlerCallback $backCallBack$inlined;

    public void PredictiveBackHandlerKt$PredictiveBackHandler$3$1$invoke$$inlined$onDispose$1(PredictiveBackHandlerCallback p0){
       this.$backCallBack$inlined = p0;
       super();
    }
    public void dispose(){
       this.$backCallBack$inlined.remove();
    }
}
