package androidx.activity.compose.LocalOnBackPressedDispatcherOwner;
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1;
import tb.le40;
import tb.d1a;
import java.lang.Object;
import tb.v140;
import androidx.compose.runtime.CompositionLocalKt;
import androidx.compose.runtime.a;
import androidx.activity.OnBackPressedDispatcherOwner;
import tb.oa20;
import androidx.compose.ui.platform.AndroidCompositionLocals_androidKt;
import android.view.View;
import androidx.activity.ViewTreeOnBackPressedDispatcherOwner;
import android.content.Context;
import android.content.ContextWrapper;
import tb.x140;

public final class LocalOnBackPressedDispatcherOwner	// class@000488 from classes.dex
{
    public static final int $stable;
    public static final LocalOnBackPressedDispatcherOwner INSTANCE;
    private static final v140 LocalOnBackPressedDispatcherOwner;

    static {
       LocalOnBackPressedDispatcherOwner.INSTANCE = new LocalOnBackPressedDispatcherOwner();
       LocalOnBackPressedDispatcherOwner.LocalOnBackPressedDispatcherOwner = CompositionLocalKt.e(null, LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1.INSTANCE, 1, null);
    }
    private void LocalOnBackPressedDispatcherOwner(){
       super();
    }
    public final OnBackPressedDispatcherOwner getCurrent(a p0,int p1){
       p0.F(-2068013981);
       OnBackPressedDispatcherOwner onBackPresse = p0.d(LocalOnBackPressedDispatcherOwner.LocalOnBackPressedDispatcherOwner);
       p0.F(0x64249efd);
       if (onBackPresse == null) {
          onBackPresse = ViewTreeOnBackPressedDispatcherOwner.get(p0.d(AndroidCompositionLocals_androidKt.i()));
       }
       p0.K();
       if (onBackPresse == null) {
          onBackPresse = p0.d(AndroidCompositionLocals_androidKt.g());
          while (true) {
             if (onBackPresse instanceof ContextWrapper) {
                if (onBackPresse instanceof OnBackPressedDispatcherOwner) {
                   break ;
                }else {
                   onBackPresse = onBackPresse.getBaseContext();
                }
             }else {
                onBackPresse = null;
             }
          }
       }
       p0.K();
       return onBackPresse;
    }
    public final x140 provides(OnBackPressedDispatcherOwner p0){
       return LocalOnBackPressedDispatcherOwner.LocalOnBackPressedDispatcherOwner.d(p0);
    }
}
