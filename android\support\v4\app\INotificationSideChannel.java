package android.support.v4.app.INotificationSideChannel;
import android.os.IInterface;
import java.lang.String;
import android.app.Notification;

public interface abstract INotificationSideChannel implements IInterface	// class@00012b from classes.dex
{
    public static final String DESCRIPTOR;

    static {
       INotificationSideChannel.DESCRIPTOR = "android$support$v4$app$INotificationSideChannel".replace('$', '.');
    }
    void cancel(String p0,int p1,String p2);
    void cancelAll(String p0);
    void notify(String p0,int p1,String p2,Notification p3);
}
