package android.taobao.windvane.extra.uc.WVUCWebChromeClient$8;
import android.content.DialogInterface$OnCancelListener;
import android.taobao.windvane.extra.uc.WVUCWebChromeClient;
import com.uc.webview.export.JsPromptResult;
import java.lang.Object;
import android.content.DialogInterface;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class WVUCWebChromeClient$8 implements DialogInterface$OnCancelListener	// class@00024a from classes.dex
{
    public final WVUCWebChromeClient this$0;
    public final JsPromptResult val$res;
    public static IpChange $ipChange;

    public void WVUCWebChromeClient$8(WVUCWebChromeClient p0,JsPromptResult p1){
       this.this$0 = p0;
       this.val$res = p1;
       super();
    }
    public void onCancel(DialogInterface p0){
       IpChange $ipChange = WVUCWebChromeClient$8.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f4ed3926", objArray);
          return;
       }else {
          this.val$res.cancel();
          return;
       }
    }
}
