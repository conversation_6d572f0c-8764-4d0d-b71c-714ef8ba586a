package android.taobao.windvane.extra.config.TBConfigManager$2;
import tb.z8l;
import android.taobao.windvane.extra.config.TBConfigManager;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import com.taobao.orange.OrangeConfig;
import java.util.Map;
import org.json.JSONObject;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import tb.yaa;
import java.lang.Throwable;
import android.taobao.windvane.config.WVConfigManager;
import java.lang.StringBuilder;
import tb.v7t;

public class TBConfigManager$2 implements z8l	// class@00018c from classes.dex
{
    public final TBConfigManager this$0;
    public static IpChange $ipChange;

    public void TBConfigManager$2(TBConfigManager p0){
       this.this$0 = p0;
       super();
    }
    public void onConfigUpdate(String p0,boolean p1){
       Map configs;
       JSONObject $ipChange1;
       IpChange $ipChange = TBConfigManager$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("9458c0f9", objArray);
          return;
       }else if((configs = OrangeConfig.getInstance().getConfigs(p0)) != null && configs.size()){
          try{
             $ipChange1 = new JSONObject();
             Iterator iterator = configs.entrySet().iterator();
             while (iterator.hasNext()) {
                Map$Entry uEntry = iterator.next();
                String key = uEntry.getKey();
                $ipChange1.put(key, uEntry.getValue());
             }
             $ipChange1.put("appVersion", yaa.f().b());
          }catch(org.json.JSONException e5){
             e5.printStackTrace();
          }
          WVConfigManager.a().d(p0, $ipChange1.toString());
          v7t.i("WVConfig", "receive name=["+p0+"]");
          return;
       }else {
          WVConfigManager.a().d(p0, "");
          return;
       }
    }
}
