package android.taobao.windvane.extra.uc.AliNetworkAdapterNew$1;
import android.taobao.windvane.export.network.RequestCallback;
import android.taobao.windvane.extra.uc.AliNetworkAdapterNew;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import android.taobao.windvane.extra.uc.WVUCWebView;
import tb.ace;
import tb.cce;
import java.lang.Boolean;
import tb.vpw;
import tb.wpw;
import android.os.SystemClock;
import java.util.Map;
import java.util.List;

public class AliNetworkAdapterNew$1 extends RequestCallback	// class@0001fd from classes.dex
{
    public final AliNetworkAdapterNew this$0;
    public final EventHandler val$eventHandler;
    public static IpChange $ipChange;

    public void AliNetworkAdapterNew$1(AliNetworkAdapterNew p0,EventHandler p1){
       this.this$0 = p0;
       this.val$eventHandler = p1;
       super();
    }
    public static Object ipc$super(AliNetworkAdapterNew$1 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/AliNetworkAdapterNew$1");
    }
    public void onCustomCallback(int p0,Object[] p1){
       object oobject;
       WVUCWebView wVUCWebView;
       WVUCWebView wVUCWebView1;
       int i = 2;
       IpChange $ipChange = AliNetworkAdapterNew$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("c344bdea", objArray);
          return;
       }else if(!p0){
          if (p1 != null && p1.length == 1) {
             oobject = p1[0];
             wVUCWebView = AliNetworkAdapterNew.access$000(this.this$0);
             if (oobject instanceof Integer && wVUCWebView != null) {
                wVUCWebView.getWebViewContext().getWebViewPageModel().onPropertyIfAbsent("H5_snapshotMatchType", oobject);
             }
          }
       }else if(p0 == 1){
          if (p1 != null && p1.length == 1) {
             oobject = p1[0];
             wVUCWebView = AliNetworkAdapterNew.access$000(this.this$0);
             if (oobject instanceof Boolean && wVUCWebView != null) {
                wVUCWebView.getWebViewContext().setHitSnapshot(oobject.booleanValue());
             }
          }
       }else if(p0 == i && (vpw.commonConfig.I2 != null && (wVUCWebView1 = AliNetworkAdapterNew.access$000(this.this$0)) != null)){
          wVUCWebView1.getWebViewContext().getWebViewPageModel().onStageIfAbsent("H5_firstChunkReceived", SystemClock.uptimeMillis());
       }
       return;
    }
    public void onError(int p0,String p1){
       IpChange $ipChange = AliNetworkAdapterNew$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("7a671c9d", objArray);
          return;
       }else {
          this.val$eventHandler.error(p0, p1);
          return;
       }
    }
    public void onFinish(){
       IpChange $ipChange = AliNetworkAdapterNew$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("badeed9", objArray);
          return;
       }else {
          this.val$eventHandler.endData();
          return;
       }
    }
    public void onReceiveData(byte[] p0){
       IpChange $ipChange = AliNetworkAdapterNew$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f24c16dc", objArray);
          return;
       }else {
          this.val$eventHandler.data(p0, p0.length);
          return;
       }
    }
    public void onResponse(int p0,Map p1){
       List list;
       int i = 2;
       int i1 = 0;
       IpChange $ipChange = AliNetworkAdapterNew$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("bb214fe9", objArray);
          return;
       }else if(p1 != null){
          String str = "x-protocol";
          if (p1.containsKey(str)) {
             if ((list = p1.get(str)) != null && list.size() > 0) {
                str = list.get(i1);
                if (!"http".equals(str) && !"https".equals(str)) {
                   this.val$eventHandler.status(i, i1, p0, "");
                }else {
                   this.val$eventHandler.status(i1, i1, p0, "");
                }
             }
          }else if(p1.containsKey(":status")){
             this.val$eventHandler.status(i, i1, p0, "");
          }else {
             this.val$eventHandler.status(i1, i1, p0, "");
          }
       }
       this.val$eventHandler.headers(p1);
       return;
    }
}
