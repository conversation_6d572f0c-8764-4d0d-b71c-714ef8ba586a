package android.taobao.windvane.extra.uc.WVUCWebView$2;
import android.taobao.windvane.extra.uc.remotefetch.WVUCRemoteFetcher$WVUCFetcherCallback;
import java.lang.Object;
import java.lang.Throwable;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.bf1;
import tb.r9u;
import android.taobao.windvane.extra.uc.WVUCWebView;

public final class WVUCWebView$2 implements WVUCRemoteFetcher$WVUCFetcherCallback	// class@000254 from classes.dex
{
    public static IpChange $ipChange;

    public void WVUCWebView$2(){
       super();
    }
    public void onFetchFail(Throwable p0){
       IpChange $ipChange = WVUCWebView$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("249e4e15", objArray);
          return;
       }else {
          r9u.c(bf1.FETCH_UC_SO);
          WVUCWebView.access$600(p0);
          return;
       }
    }
    public void onFetchSuccess(String p0){
       IpChange $ipChange = WVUCWebView$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e41973f9", objArray);
          return;
       }else {
          r9u.c(bf1.FETCH_UC_SO);
          WVUCWebView.access$500(p0);
          return;
       }
    }
}
