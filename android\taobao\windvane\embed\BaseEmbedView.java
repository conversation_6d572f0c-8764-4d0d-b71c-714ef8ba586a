package android.taobao.windvane.embed.BaseEmbedView;
import com.uc.webview.export.extension.IEmbedView;
import com.uc.webview.export.extension.IEmbedViewContainer$OnParamChangedListener;
import com.uc.webview.export.extension.IEmbedViewContainer$OnStateChangedListener;
import com.uc.webview.export.extension.IEmbedViewContainer$OnVisibilityChangedListener;
import tb.kpw;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import android.content.Context;
import android.view.View;
import android.graphics.Bitmap;
import android.taobao.windvane.webview.IWVWebView;
import com.uc.webview.export.extension.EmbedViewConfig;
import java.lang.CharSequence;
import android.text.TextUtils;
import org.json.JSONObject;
import java.lang.Throwable;
import android.content.MutableContextWrapper;
import android.content.ContextWrapper;
import android.app.Activity;
import com.uc.webview.export.extension.IEmbedViewContainer;
import java.lang.Integer;

public abstract class BaseEmbedView extends kpw implements IEmbedView, IEmbedViewContainer$OnParamChangedListener, IEmbedViewContainer$OnStateChangedListener, IEmbedViewContainer$OnVisibilityChangedListener	// class@000153 from classes.dex
{
    public Context context;
    public String id;
    public EmbedViewConfig params;
    public View view;
    public IWVWebView webView;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800050);
    }
    public void BaseEmbedView(){
       super();
       this.view = null;
       this.params = null;
       this.id = "";
    }
    public static Object ipc$super(BaseEmbedView p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/embed/BaseEmbedView");
    }
    public boolean execute(String p0,String p1,WVCallBackContext p2){
       int i = 0;
       IpChange $ipChange = BaseEmbedView.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return i;
       }
       Object[] objArray = new Object[]{this,p0,p1,p2};
       return $ipChange.ipc$dispatch("bcd41fd1", objArray).booleanValue();
    }
    public abstract View generateView(Context p0);
    public Bitmap getSnapShot(){
       IpChange $ipChange = BaseEmbedView.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("48acabf", objArray);
    }
    public View getView(){
       BaseEmbedView tview;
       IpChange $ipChange = BaseEmbedView.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("576a35e4", objArray);
       }else if((tview = this.view) != null){
          return tview;
       }else if((tview = this.context) == null){
          return null;
       }else {
          View view = this.generateView(tview);
          this.view = view;
          return view;
       }
    }
    public abstract String getViewType();
    public boolean init(String p0,String p1,IWVWebView p2,EmbedViewConfig p3){
       JSONObject jSONObject;
       int i = 0;
       IpChange $ipChange = BaseEmbedView.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          return $ipChange.ipc$dispatch("ba9b2af8", objArray).booleanValue();
       }else if(p1.equals(this.getViewType()) && p2 != null){
          p1 = (TextUtils.isEmpty(p0))? "WVEmbedView": "WVEmbedView_"+p0;
          this.id = p0;
          p2.addJsObject(p1, this);
          try{
             jSONObject = new JSONObject();
             jSONObject.put("bridgeId", p0);
          }catch(org.json.JSONException e6){
             e6.printStackTrace();
          }
          p2.evaluateJavascript("javascript:window.WindVane&&window.WindVane.fireEvent\(\'WVEmbed.Ready\',"+jSONObject.toString()+"\);");
          this.context = p2.getContext();
          if (p2.getContext() instanceof MutableContextWrapper) {
             this.context = p2.getContext().getBaseContext();
          }
          if (!this.context instanceof Activity) {
             this.context = null;
          }
          this.webView = p2;
          this.params = p3;
          return 1;
       }else {
          return i;
       }
    }
    public boolean init(String p0,String p1,IWVWebView p2,EmbedViewConfig p3,IEmbedViewContainer p4){
       JSONObject jSONObject;
       int i = 0;
       IpChange $ipChange = BaseEmbedView.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3,p4};
          return $ipChange.ipc$dispatch("eb3ca6a7", objArray).booleanValue();
       }else if(p1.equals(this.getViewType()) && p2 != null){
          p1 = (TextUtils.isEmpty(p0))? "WVEmbedView": "WVEmbedView_"+p0;
          p2.addJsObject(p1, this);
          try{
             jSONObject = new JSONObject();
             jSONObject.put("bridgeId", p0);
          }catch(org.json.JSONException e10){
             e10.printStackTrace();
          }
          p2.evaluateJavascript("javascript:window.WindVane&&window.WindVane.fireEvent\(\'WVEmbed.Ready\',"+jSONObject.toString()+"\);");
          this.context = p2.getContext();
          if (p2.getContext() instanceof MutableContextWrapper) {
             this.context = p2.getContext().getBaseContext();
          }
          if (!this.context instanceof Activity) {
             this.context = null;
          }
          this.webView = p2;
          this.params = p3;
          this.id = p0;
          return 1;
       }else {
          return i;
       }
    }
    public void onAttachedToWebView(){
       IpChange $ipChange = BaseEmbedView.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("f1bdbec2", objArray);
       }
       return;
    }
    public void onDestroy(){
       IpChange $ipChange = BaseEmbedView.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("a6532022", objArray);
          return;
       }else {
          this.view = null;
          this.context = null;
          return;
       }
    }
    public void onDetachedFromWebView(){
       IpChange $ipChange = BaseEmbedView.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("f7ebb65", objArray);
       }
       return;
    }
    public void onParamChanged(String[] p0,String[] p1){
       IpChange $ipChange = BaseEmbedView.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("7ddb624d", objArray);
       }
       return;
    }
    public void onVisibilityChanged(int p0){
       IpChange $ipChange = BaseEmbedView.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("7606d219", objArray);
       }
       return;
    }
}
