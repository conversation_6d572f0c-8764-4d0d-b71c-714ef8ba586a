package android.taobao.windvane.extra.uc.DowngradableSSRService$1$1;
import android.taobao.windvane.extra.uc.prefetch.TNetCallBack;
import android.taobao.windvane.extra.uc.DowngradableSSRService$1;
import tb.bgq;
import java.lang.Object;
import anetwork.channel.NetworkEvent$ProgressEvent;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.util.Arrays;
import tb.mnf;
import anetwork.channel.NetworkEvent$FinishEvent;
import com.taobao.android.riverlogger.RVLLevel;
import java.lang.StringBuilder;
import tb.lcn;
import tb.egq$b;
import tb.egq;
import java.util.Map;
import java.lang.Integer;
import java.lang.Boolean;

public class DowngradableSSRService$1$1 implements TNetCallBack	// class@00020c from classes.dex
{
    public final DowngradableSSRService$1 this$1;
    public final bgq val$ssrRequest;
    public static IpChange $ipChange;

    public void DowngradableSSRService$1$1(DowngradableSSRService$1 p0,bgq p1){
       this.this$1 = p0;
       this.val$ssrRequest = p1;
       super();
    }
    public void onDataReceived(NetworkEvent$ProgressEvent p0,Object p1){
       IpChange $ipChange = DowngradableSSRService$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("e27ec0e9", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if((p1 = this.this$1.val$callback) != null){
          p1.onReceiveData(this.val$ssrRequest, Arrays.copyOf(p0.getBytedata(), p0.getSize()));
       }
       return;
    }
    public void onFinished(NetworkEvent$FinishEvent p0,Object p1){
       int httpCode;
       DowngradableSSRService$1 val$callback;
       DowngradableSSRService$1 val$callback1;
       IpChange $ipChange = DowngradableSSRService$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("a00910e8", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if((httpCode = p0.getHttpCode()) < 0){
          String desc = p0.getDesc();
          lcn.f(RVLLevel.Error, "DowngradableSSRService", "error code = "+httpCode+", desc="+desc+", url = "+this.val$ssrRequest.a);
          if ((val$callback = this.this$1.val$callback) != null) {
             val$callback.onError(this.val$ssrRequest, new egq$b().b(httpCode).d(desc).a());
          }
       }else if((val$callback1 = this.this$1.val$callback) != null){
          val$callback1.onFinish(this.val$ssrRequest);
       }
       return;
    }
    public boolean onResponseCode(int p0,Map p1,Object p2){
       int i = 0;
       IpChange $ipChange = DowngradableSSRService$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1,p2};
          return $ipChange.ipc$dispatch("45b007d6", objArray).booleanValue();
       }else if((p2 = this.this$1.val$callback) != null){
          p2.onResponse(this.val$ssrRequest, p0, p1);
       }
       return i;
    }
}
