package android.taobao.windvane.export.cache.memory.MemoryResWarmupManager;
import tb.t2o;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$d;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.Object;
import android.taobao.windvane.export.cache.memory.model.ResourceItemModel;
import com.uc.webview.export.WebResourceResponse;
import java.util.Map;
import java.util.HashMap;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$e;
import java.lang.Runnable;
import com.uc.webview.export.extension.StorageUtils;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.extra.uc.offlineresource.OfflineResourceClient;
import tb.xae;
import tb.mrt;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$a;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$3;
import android.webkit.ValueCallback;
import tb.y8o;
import com.taobao.zcache.ResourceResponse;
import tb.yox;
import com.taobao.zcache.Error;
import android.taobao.windvane.util.MimeTypeEnum;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import android.taobao.windvane.extra.core.WVCore;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$b;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$4;
import com.uc.webview.export.extension.JsAot;
import java.lang.StringBuilder;
import tb.v7t;
import java.lang.Throwable;

public class MemoryResWarmupManager	// class@00015e from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800067);
    }
    public static void a(MemoryResWarmupManager$d p0,boolean p1,String p2){
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,new Boolean(p1),p2};
          $ipChange.ipc$dispatch("8398c965", objArray);
          return;
       }else {
          MemoryResWarmupManager.g(p0, p1, p2);
          return;
       }
    }
    public static void b(ResourceItemModel p0,MemoryResWarmupManager$d p1){
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("2bd09c7f", objArray);
          return;
       }else {
          MemoryResWarmupManager.l(p0, p1);
          return;
       }
    }
    public static void c(ResourceItemModel p0,MemoryResWarmupManager$d p1){
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("bfb454c0", objArray);
          return;
       }else {
          MemoryResWarmupManager.k(p0, p1);
          return;
       }
    }
    public static void d(ResourceItemModel p0,WebResourceResponse p1,MemoryResWarmupManager$d p2){
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2};
          $ipChange.ipc$dispatch("887e422f", objArray);
          return;
       }else {
          MemoryResWarmupManager.h(p0, p1, p2);
          return;
       }
    }
    public static Map e(){
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("5d873088", objArray);
       }else {
          HashMap hashMap = new HashMap();
          hashMap.put("maxAge", "3600");
          hashMap.put("ignoreQuery", "0");
          hashMap.put("isMainRes", "0");
          hashMap.put("useOnce", "1");
          return hashMap;
       }
    }
    public static boolean f(ResourceItemModel p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("e02f493", objArray).booleanValue();
       }else if("css".equals(p0.type)){
          return p0.src.endsWith(".css");
       }else if("js".equals(p0.type)){
          return p0.src.endsWith(".js");
       }else if("image".equals(p0.type)){
          p0 = p0.src;
          if (TextUtils.isEmpty(p0)) {
             return i;
          }else if(p0.endsWith(".jpg")){
             return i1;
          }else if(p0.endsWith(".png")){
             return i1;
          }else if(p0.endsWith(".heic")){
             return i1;
          }else if(p0.endsWith(".gif")){
             return i1;
          }else if(p0.endsWith(".webp")){
             return i1;
          }
       }
       return i;
    }
    public static void g(MemoryResWarmupManager$d p0,boolean p1,String p2){
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,new Boolean(p1),p2};
          $ipChange.ipc$dispatch("3b7293a3", objArray);
          return;
       }else if(p0 != null){
          MemoryResWarmupManager.i(new MemoryResWarmupManager$e(p0, p1, p2));
       }
       return;
    }
    public static void h(ResourceItemModel p0,WebResourceResponse p1,MemoryResWarmupManager$d p2){
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2};
          $ipChange.ipc$dispatch("2215df8c", objArray);
          return;
       }else {
          HashMap hashMap = new HashMap();
          hashMap.put(p0.src, p1);
          StorageUtils.precacheResources(hashMap, MemoryResWarmupManager.e());
          if (vpw.commonConfig.F3 != null && ("js".equals(p0.type) && (!TextUtils.isEmpty(p0.src) && (p0.enableAot != null && OfflineResourceClient.getInstance().isEnabled())))) {
             MemoryResWarmupManager.o(p0);
          }
          return;
       }
    }
    public static void i(Runnable p0){
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("6a842a1f", objArray);
          return;
       }else {
          mrt.a().execute(p0);
          return;
       }
    }
    public static void j(ResourceItemModel p0,MemoryResWarmupManager$d p1){
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("e3c5b52e", objArray);
          return;
       }else {
          MemoryResWarmupManager.m(p0, new MemoryResWarmupManager$a(p1, p0));
          return;
       }
    }
    public static void k(ResourceItemModel p0,MemoryResWarmupManager$d p1){
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("8238d7c7", objArray);
          return;
       }else {
          StorageUtils.getResourceFromHttpCache(p0.src, new MemoryResWarmupManager$3(p1, p0));
          return;
       }
    }
    public static void l(ResourceItemModel p0,MemoryResWarmupManager$d p1){
       ResourceResponse resourceResp;
       String mimeType;
       int i = 0;
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("3e29492f", objArray);
          return;
       }else if((resourceResp = yox.h(new y8o(p0.src))) != null && (resourceResp.getError() == null && resourceResp.getData() != null)){
          if ("js".equals(p0.type)) {
             mimeType = MimeTypeEnum.JS.getMimeType();
          }else if("css".equals(p0.type)){
             mimeType = MimeTypeEnum.CSS.getMimeType();
          }else if("image".equals(p0.type)){
             ResourceItemModel src = p0.src;
             if (TextUtils.isEmpty(src)) {
                MemoryResWarmupManager.g(p1, i, "url is empty");
                return;
             }else if(src.endsWith(".jpg")){
                mimeType = MimeTypeEnum.JPG.getMimeType();
             }else if(src.endsWith(".png")){
                mimeType = MimeTypeEnum.PNG.getMimeType();
             }else if(src.endsWith(".heic")){
                mimeType = MimeTypeEnum.HEIC.getMimeType();
             }else if(src.endsWith(".gif")){
                mimeType = MimeTypeEnum.GIF.getMimeType();
             }else if(src.endsWith(".webp")){
                mimeType = MimeTypeEnum.WEBP.getMimeType();
             }
          }
          mimeType = null;
          MemoryResWarmupManager.h(p0, new WebResourceResponse(mimeType, "UTF-8", new ByteArrayInputStream(resourceResp.getData())), p1);
          MemoryResWarmupManager.g(p1, 1, "from zcache");
          return;
       }else {
          MemoryResWarmupManager.g(p1, i, "not in ZCache");
          return;
       }
    }
    public static void m(ResourceItemModel p0,MemoryResWarmupManager$d p1){
       ResourceItemModel src;
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("2150728b", objArray);
          return;
       }else if(TextUtils.isEmpty(p0.mode)){
          p0.mode = "only_if_cached";
       }
       if (vpw.commonConfig.K2 == null) {
          MemoryResWarmupManager.g(p1, 0, "resource prewarm is disabled");
          return;
       }else if(p0.mode != null && ((src = p0.src) != null && p0.type != null)){
          if (!src.startsWith("https://")) {
             MemoryResWarmupManager.g(p1, 0, "src is not https");
             return;
          }else if(!WVCore.getInstance().isUCSupport()){
             MemoryResWarmupManager.g(p1, 0, "uc not support");
             return;
          }else if(!MemoryResWarmupManager.f(p0)){
             MemoryResWarmupManager.g(p1, 0, "type and src is not matched");
             return;
          }else if("only_if_cached".equals(p0.mode)){
             MemoryResWarmupManager.n(p0, p1);
          }else if("normal".equals(p0.mode)){
             MemoryResWarmupManager.g(p1, 0, "unsupported mode");
          }else {
             MemoryResWarmupManager.g(p1, 0, "invalid mode");
          }
          return;
       }else {
          MemoryResWarmupManager.g(p1, 0, "required field is null");
          return;
       }
    }
    public static void n(ResourceItemModel p0,MemoryResWarmupManager$d p1){
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("71e8e339", objArray);
          return;
       }else {
          MemoryResWarmupManager.i(new MemoryResWarmupManager$b(p0, p1));
          return;
       }
    }
    public static void o(ResourceItemModel p0){
       int i = 0;
       String str = "MemoryResWarmupManager";
       String str1 = "generateCodeCache:";
       IpChange $ipChange = MemoryResWarmupManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("8b479e5d", objArray);
          return;
       }else {
          HashMap hashMap = new HashMap();
          ResourceItemModel src = p0.src;
          try{
             hashMap.put(src, "");
             JsAot.generateCodeCache(hashMap, new MemoryResWarmupManager$4(p0, hashMap));
             v7t.i(str, str1+p0.src);
          }catch(java.lang.Exception e6){
             Object[] objArray1 = new Object[i];
             v7t.e(str, "generateCodeCache error", e6, objArray1);
          }
          return;
       }
    }
}
