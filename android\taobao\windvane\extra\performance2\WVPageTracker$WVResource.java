package android.taobao.windvane.extra.performance2.WVPageTracker$WVResource;
import java.io.Serializable;
import tb.t2o;
import android.taobao.windvane.extra.performance2.WVPageTracker;
import java.lang.Object;

public class WVPageTracker$WVResource implements Serializable	// class@0001dd from classes.dex
{
    public int dataSize;
    public boolean isHTML;
    public long loadingEndTime;
    public long loadingStartTime;
    public int statusCode;
    public final WVPageTracker this$0;
    public String url;
    public String zcacheInfo;
    public int zcacheState;

    static {
       t2o.a(0x3d800113);
    }
    public void WVPageTracker$WVResource(WVPageTracker p0){
       this.this$0 = p0;
       super();
    }
}
