package android.taobao.windvane.extra.jsbridge.DefaultAsyncApiProxy;
import tb.yd1;
import java.io.Serializable;
import tb.t2o;
import android.taobao.windvane.extra.jsbridge.DefaultAsyncApiProxy$Companion;
import tb.a07;
import android.os.HandlerThread;
import java.lang.String;
import java.lang.Thread;
import android.os.Handler;
import android.os.Looper;
import java.lang.Object;
import java.lang.Runnable;
import com.android.alibaba.ip.runtime.IpChange;
import android.util.Log;
import android.taobao.windvane.extra.jsbridge.CommonAsyncJSAPIBridge;
import tb.ckf;
import android.taobao.windvane.extra.uc.WVUCWebView;

public final class DefaultAsyncApiProxy implements yd1, Serializable	// class@000198 from classes.dex
{
    private Handler asyncApiHandler;
    private WVUCWebView ucWebView;
    public static IpChange $ipChange;
    public static final DefaultAsyncApiProxy$Companion Companion;
    private static final Handler apiHandler;
    private static final HandlerThread apiThread;

    static {
       t2o.a(0x3d8000cc);
       t2o.a(0x3d8001dd);
       DefaultAsyncApiProxy.Companion = new DefaultAsyncApiProxy$Companion(null);
       HandlerThread handlerThrea = new HandlerThread("wvAsyncApiThread");
       handlerThrea.start();
       DefaultAsyncApiProxy.apiThread = handlerThrea;
       DefaultAsyncApiProxy.apiHandler = new Handler(handlerThrea.getLooper());
    }
    public void DefaultAsyncApiProxy(){
       super();
       this.asyncApiHandler = DefaultAsyncApiProxy.apiHandler;
    }
    public void apiCall(Runnable p0){
       IpChange $ipChange = DefaultAsyncApiProxy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("6143691d", objArray);
          return;
       }else {
          DefaultAsyncApiProxy tasyncApiHan = this.asyncApiHandler;
          if (p0 != null) {
             tasyncApiHan.post(p0);
          }
          return;
       }
    }
    public void injectAsyncApiEvn(){
       DefaultAsyncApiProxy tucWebView;
       IpChange $ipChange = DefaultAsyncApiProxy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("47b5d235", objArray);
          return;
       }else {
          Log.e("DefaultAsyncApiProxy", "enable async api");
          if ((tucWebView = this.ucWebView) != null) {
             DefaultAsyncApiProxy tucWebView1 = this.ucWebView;
             ckf.d(tucWebView1);
             CommonAsyncJSAPIBridge uCommonAsync = new CommonAsyncJSAPIBridge(tucWebView1);
             tucWebView.addJavascriptInterface(uCommonAsync, "__windvane_async__");
          }
          return;
       }
    }
    public void releaseProxy(){
       IpChange $ipChange = DefaultAsyncApiProxy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("d2ce77ae", objArray);
       }
       return;
    }
    public void setUCWebView(Object p0){
       IpChange $ipChange = DefaultAsyncApiProxy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3738b76c", objArray);
          return;
       }else if(p0 instanceof WVUCWebView){
          this.ucWebView = p0;
       }
       return;
    }
}
