package androidx.activity.compose.OnBackInstance$job$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import androidx.activity.OnBackPressedCallback;
import androidx.activity.compose.OnBackInstance;
import tb.ar4;
import java.lang.Object;
import tb.uu4;
import tb.xhv;
import tb.dkf;
import kotlin.jvm.internal.Ref$BooleanRef;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;
import kotlinx.coroutines.channels.c;
import kotlinx.coroutines.channels.ReceiveChannel;
import tb.qp9;
import tb.yp9;
import androidx.activity.compose.OnBackInstance$job$1$1;
import tb.w1a;

public final class OnBackInstance$job$1 extends SuspendLambda implements u1a	// class@00048b from classes.dex
{
    public final OnBackPressedCallback $callback;
    public final u1a $onBack;
    public Object L$0;
    public int label;
    public final OnBackInstance this$0;

    public void OnBackInstance$job$1(OnBackPressedCallback p0,u1a p1,OnBackInstance p2,ar4 p3){
       this.$callback = p0;
       this.$onBack = p1;
       this.this$0 = p2;
       super(2, p3);
    }
    public final ar4 create(Object p0,ar4 p1){
       return new OnBackInstance$job$1(this.$callback, this.$onBack, this.this$0, p1);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(uu4 p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       OnBackInstance$job$1 tlabel;
       OnBackInstance$job$1 obj = dkf.d();
       if ((tlabel = this.label) != null) {
          if (tlabel == 1) {
             obj = this.L$0;
             b.b(p0);
          }else {
             throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
          }
       }else {
          b.b(p0);
          if (this.$callback.isEnabled()) {
             p0 = new Ref$BooleanRef();
             this.L$0 = p0;
             this.label = 1;
             if (this.$onBack.invoke(yp9.N(yp9.H(this.this$0.getChannel()), new OnBackInstance$job$1$1(p0, null)), this) == obj) {
                return obj;
             }else {
                Ref$BooleanRef uBooleanRef = p0;
             }
          }else {
          label_005a :
             return xhv.INSTANCE;
          }
       }
       if (obj.element != null) {
          goto label_005a ;
       }else {
          throw new IllegalStateException("You must collect the progress flow");
       }
    }
}
