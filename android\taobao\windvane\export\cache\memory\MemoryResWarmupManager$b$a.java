package android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$b$a;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$d;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$b;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager;
import android.taobao.windvane.export.cache.memory.model.ResourceItemModel;

public class MemoryResWarmupManager$b$a implements MemoryResWarmupManager$d	// class@00015a from classes.dex
{
    public final MemoryResWarmupManager$b a;
    public static IpChange $ipChange;

    public void MemoryResWarmupManager$b$a(MemoryResWarmupManager$b p0){
       super();
       this.a = p0;
    }
    public void a(boolean p0,String p1){
       int i = 1;
       IpChange $ipChange = MemoryResWarmupManager$b$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0),p1};
          $ipChange.ipc$dispatch("c845825", objArray);
          return;
       }else if(p0){
          MemoryResWarmupManager.a(this.a.b, i, p1);
          return;
       }else {
          MemoryResWarmupManager$b$a ta = this.a;
          MemoryResWarmupManager.b(ta.a, ta.b);
          return;
       }
    }
}
