package android.taobao.windvane.extra.uc.init.WVDexOptimizer;
import tb.t2o;
import java.lang.Object;
import android.content.Context;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.r9u;
import android.taobao.windvane.extra.uc.remotefetch.WVUCRemoteFetcher;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.os.SystemClock;
import java.io.File;
import com.uc.webview.export.extension.U4Engine;
import java.lang.StringBuilder;
import tb.v7t;
import java.lang.Throwable;

public class WVDexOptimizer	// class@000270 from classes.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x3d8001a5);
    }
    private void WVDexOptimizer(){
       super();
    }
    public static void preloadDexSafe(Context p0){
       String str = "WVDexOptimizer";
       String str1 = "preloadDex cost ";
       IpChange $ipChange = WVDexOptimizer.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("c87106", objArray);
          return;
       }else {
          r9u.b("preloadDex");
          String str2 = WVUCRemoteFetcher.fetchUCRemoteLocal();
          if (!TextUtils.isEmpty(str2)) {
             U4Engine.loadCoreDex(p0, new File(str2));
             v7t.i(str, str1+(SystemClock.uptimeMillis() - SystemClock.uptimeMillis()));
          }else {
             v7t.d(str, "preloadDex error ucSoPath is empty");
          }
          r9u.d();
          return;
       }
    }
}
