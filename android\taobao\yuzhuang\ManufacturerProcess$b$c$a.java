package android.taobao.yuzhuang.ManufacturerProcess$b$c$a;
import java.io.FilenameFilter;
import android.taobao.yuzhuang.ManufacturerProcess$b$c;
import java.lang.Object;
import java.io.File;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.CharSequence;

public class ManufacturerProcess$b$c$a implements FilenameFilter	// class@00031f from classes.dex
{
    public static IpChange $ipChange;

    public void ManufacturerProcess$b$c$a(ManufacturerProcess$b$c p0){
       super();
    }
    public boolean accept(File p0,String p1){
       IpChange $ipChange = ManufacturerProcess$b$c$a.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p1.toLowerCase().contains("taobao");
       }
       Object[] objArray = new Object[]{this,p0,p1};
       return $ipChange.ipc$dispatch("6696dd14", objArray).booleanValue();
    }
}
