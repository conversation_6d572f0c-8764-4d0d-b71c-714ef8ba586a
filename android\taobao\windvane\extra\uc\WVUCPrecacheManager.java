package android.taobao.windvane.extra.uc.WVUCPrecacheManager;
import tb.jqw;
import tb.t2o;
import java.util.HashSet;
import java.lang.Object;
import android.taobao.windvane.extra.uc.WVUCPrecacheManager$1;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import java.lang.System;
import tb.vpw;
import tb.wpw;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.taobao.windvane.extra.uc.WVUCPrecacheManager$WVUCPrecacheManagerHolder;
import tb.lqw;
import android.os.Handler;
import java.lang.Runnable;
import tb.kqw;
import java.util.Iterator;
import tb.iqw;
import java.lang.Integer;

public class WVUCPrecacheManager implements jqw	// class@00023d from classes.dex
{
    private Handler mPrecacheHandler;
    public static IpChange $ipChange;
    private static final String TAG;
    private static WVUCPrecacheManager mInstance;
    private static boolean sCanClearByCommonConfig;
    private static boolean sCanClearByZcacheUpdate;
    private static boolean sCanPrecacheByCommonConfig;
    private static boolean sCanPrecacheByZcacheUpdate;
    private static boolean sHasInitUrlSet;
    private static boolean sHasPrecache;
    private static boolean sLastEnableUCPrecache;
    private static String sLastPrecachePackageName;
    private static long sLastPrecacheTime;
    private static final long sMaxPrecacheTime;
    private static HashSet sPreMemCacheUrlSet;
    private static HashSet sPrecacheDocResMap;

    static {
       t2o.a(0x3d800170);
       t2o.a(0x3d80029a);
       WVUCPrecacheManager.mInstance = null;
       WVUCPrecacheManager.sHasInitUrlSet = false;
       WVUCPrecacheManager.sHasPrecache = false;
       WVUCPrecacheManager.sCanClearByCommonConfig = false;
       WVUCPrecacheManager.sCanClearByZcacheUpdate = false;
       WVUCPrecacheManager.sCanPrecacheByCommonConfig = false;
       WVUCPrecacheManager.sCanPrecacheByZcacheUpdate = false;
       WVUCPrecacheManager.sLastEnableUCPrecache = false;
       WVUCPrecacheManager.sLastPrecachePackageName = "";
       WVUCPrecacheManager.sLastPrecacheTime = -1;
       WVUCPrecacheManager.sPreMemCacheUrlSet = new HashSet();
       WVUCPrecacheManager.sPrecacheDocResMap = new HashSet();
    }
    private void WVUCPrecacheManager(){
       super();
       this.mPrecacheHandler = null;
       this.init();
    }
    public void WVUCPrecacheManager(WVUCPrecacheManager$1 p0){
       super();
    }
    public static boolean canClearPrecache(){
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("720990c7", objArray).booleanValue();
       }else if(!WVUCPrecacheManager.sHasPrecache){
          return i;
       }else if(!WVUCPrecacheManager.sCanClearByCommonConfig && !WVUCPrecacheManager.sCanClearByZcacheUpdate){
          if ((WVUCPrecacheManager.sLastPrecacheTime) > 0 && ((System.currentTimeMillis() - WVUCPrecacheManager.sLastPrecacheTime) - 0x36ee80) > 0) {
             return true;
          }
          return i;
       }else {
          return true;
       }
    }
    public static boolean canPrecache(){
       HashSet sPreMemCache;
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("c673d3ba", objArray).booleanValue();
       }else if(vpw.commonConfig.o != null && !TextUtils.isEmpty(WVUCPrecacheManager.sLastPrecachePackageName)){
          if (!WVUCPrecacheManager.sHasInitUrlSet) {
             WVUCPrecacheManager.sHasInitUrlSet = true;
             WVUCPrecacheManager.updatePreMemCacheUrls();
          }
          if ((sPreMemCache = WVUCPrecacheManager.sPreMemCacheUrlSet) != null && sPreMemCache.size() > 0) {
             if (!WVUCPrecacheManager.sCanPrecacheByCommonConfig && (!WVUCPrecacheManager.sCanPrecacheByZcacheUpdate && WVUCPrecacheManager.sHasPrecache)) {
                return i;
             }else {
                return true;
             }
          }
       }
       return i;
    }
    public static WVUCPrecacheManager getInstance(){
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVUCPrecacheManager$WVUCPrecacheManagerHolder.sInstance;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("432cac70", objArray);
    }
    private void init(){
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("fede197", objArray);
          return;
       }else {
          lqw.d().b(this);
          return;
       }
    }
    private static void notifyUpdateCommonConfig(){
       Object[] objArray;
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i];
          $ipChange.ipc$dispatch("8acf4a6", objArray);
          return;
       }else {
          wpw commonConfig = vpw.commonConfig;
          wpw o = commonConfig.o;
          commonConfig = commonConfig.p;
          boolean sLastEnableU = WVUCPrecacheManager.sLastEnableUCPrecache;
          boolean b = true;
          if (sLastEnableU && o == null) {
             WVUCPrecacheManager.sCanClearByCommonConfig = b;
             WVUCPrecacheManager.sCanPrecacheByCommonConfig = i;
          }else if(!sLastEnableU && o != null){
             WVUCPrecacheManager.sCanClearByCommonConfig = b;
             WVUCPrecacheManager.sCanPrecacheByCommonConfig = b;
          }else if(!WVUCPrecacheManager.sLastPrecachePackageName.equals(commonConfig)){
             WVUCPrecacheManager.sCanClearByCommonConfig = b;
             if (!TextUtils.isEmpty(commonConfig)) {
                WVUCPrecacheManager.sCanPrecacheByCommonConfig = b;
             }
          }else {
             b = objArray;
          }
          WVUCPrecacheManager.sLastEnableUCPrecache = o;
          WVUCPrecacheManager.sLastPrecachePackageName = commonConfig;
          if (b || !WVUCPrecacheManager.sHasInitUrlSet) {
             WVUCPrecacheManager.updatePreMemCacheUrls();
          }
          return;
          objArray = 1;
       }
    }
    private static void notifyUpdateZcache(String p0){
       int i = 1;
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          $ipChange.ipc$dispatch("e3d9b66b", objArray);
          return;
       }else if(!TextUtils.isEmpty(p0) && p0.equals(WVUCPrecacheManager.sLastPrecachePackageName)){
          WVUCPrecacheManager.sCanClearByZcacheUpdate = i;
          WVUCPrecacheManager.sCanPrecacheByZcacheUpdate = i;
          WVUCPrecacheManager.updatePreMemCacheUrls();
       }
       return;
    }
    public static HashSet preMemCacheUrlSet(){
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVUCPrecacheManager.sPreMemCacheUrlSet;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("978d02a7", objArray);
    }
    public static void resetClearConfig(){
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          $ipChange.ipc$dispatch("6915dfa7", objArray);
          return;
       }else {
          WVUCPrecacheManager.sCanClearByCommonConfig = i;
          WVUCPrecacheManager.sCanClearByZcacheUpdate = i;
          return;
       }
    }
    public static void resetPrecacheConfig(){
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          $ipChange.ipc$dispatch("c9a93e97", objArray);
          return;
       }else {
          WVUCPrecacheManager.sCanPrecacheByCommonConfig = i;
          WVUCPrecacheManager.sCanPrecacheByZcacheUpdate = i;
          return;
       }
    }
    private void sendClearPrecacheDocMessage(String p0){
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3bbc7aa4", objArray);
          return;
       }else if(WVUCPrecacheManager.sPrecacheDocResMap.size() <= 0){
          return;
       }else if(this.mPrecacheHandler == null){
          this.mPrecacheHandler = new Handler();
       }
       this.mPrecacheHandler.postDelayed(new WVUCPrecacheManager$1(this, p0), 0x2710);
       return;
    }
    public static void setHasPrecache(boolean p0){
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Boolean(p0)};
          $ipChange.ipc$dispatch("250a97d6", objArray);
          return;
       }else {
          WVUCPrecacheManager.sHasPrecache = p0;
          WVUCPrecacheManager.sLastPrecacheTime = (p0)? System.currentTimeMillis(): -1;
          return;
       }
    }
    private static void updatePreMemCacheUrls(){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          $ipChange.ipc$dispatch("42f06a72", objArray);
          return;
       }else if(vpw.commonConfig.o != null && !TextUtils.isEmpty(WVUCPrecacheManager.sLastPrecachePackageName)){
          Object[] objArray1 = new Object[i];
          objArray1[i1] = WVUCPrecacheManager.sLastPrecachePackageName;
          kqw okqw = lqw.d().g(6011, objArray1);
          if (okqw.a != null && ((okqw = okqw.b) != null && okqw instanceof HashSet)) {
             WVUCPrecacheManager.sPreMemCacheUrlSet = okqw;
             WVUCPrecacheManager.sHasInitUrlSet = i;
          }
          return;
       }else {
          WVUCPrecacheManager.sPreMemCacheUrlSet = new HashSet();
          return;
       }
    }
    public void addPrecacheDoc(String p0){
       int i = 0;
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("49df5f29", objArray);
          return;
       }else if(TextUtils.isEmpty(p0)){
          return;
       }else {
          String str = "#";
          if (p0.indexOf(str) > 0) {
             p0 = p0.substring(i, p0.indexOf(str));
          }
          WVUCPrecacheManager.sPrecacheDocResMap.add(p0);
          this.sendClearPrecacheDocMessage(p0);
          return;
       }
    }
    public boolean canPrecacheDoc(String p0){
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("611139de", objArray).booleanValue();
       }else if(vpw.commonConfig.q != null && !TextUtils.isEmpty(p0)){
          if (WVUCPrecacheManager.sPreMemCacheUrlSet.isEmpty()) {
             return 1;
          }
          Iterator iterator = WVUCPrecacheManager.sPreMemCacheUrlSet.iterator();
          while (true) {
             if (!iterator.hasNext()) {
                return 1;
             }
             if (p0.startsWith(iterator.next())) {
                break ;
             }
          }
          return 0;
       }else {
          return 0;
       }
    }
    public void clearPrecacheDoc(String p0){
       int i = 0;
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("2bce86fd", objArray);
          return;
       }else {
          String str = "#";
          if (p0.indexOf(str) > 0) {
             p0 = p0.substring(i, p0.indexOf(str));
          }
          WVUCPrecacheManager.sPrecacheDocResMap.remove(p0);
          return;
       }
    }
    public boolean hasPrecacheDoc(String p0){
       int i = 0;
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("d7ec2854", objArray).booleanValue();
       }else {
          String str = "#";
          if (p0.indexOf(str) > 0) {
             p0 = p0.substring(i, p0.indexOf(str));
          }
          return WVUCPrecacheManager.sPrecacheDocResMap.contains(p0);
       }
    }
    public kqw onEvent(int p0,iqw p1,Object[] p2){
       int i = 0;
       IpChange $ipChange = WVUCPrecacheManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1,p2};
          return $ipChange.ipc$dispatch("75ee5a2a", objArray);
       }else if(p0 != 6008){
          if (p0 == 6012) {
             WVUCPrecacheManager.notifyUpdateCommonConfig();
          }
       }else {
          WVUCPrecacheManager.notifyUpdateZcache(p2[i]);
       }
       return null;
    }
}
