package android.taobao.windvane.extra.uc.AliNetworkDecider;
import android.taobao.windvane.extra.uc.interfaces.INetworkDecider;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;
import android.taobao.windvane.extra.uc.WVUCWebView;

public class AliNetworkDecider implements INetworkDecider	// class@000200 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800135);
       t2o.a(0x3d8001a8);
    }
    public void AliNetworkDecider(){
       super();
    }
    public int chooseNetwork(String p0){
       int i = 0;
       int i1 = 2;
       IpChange $ipChange = AliNetworkDecider.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          objArray[1] = p0;
          return $ipChange.ipc$dispatch("b1abc35b", objArray).intValue();
       }else if(!p0.startsWith("ws://") && (!p0.startsWith("wss://") && WVUCWebView.getUseTaobaoNetwork())){
          i = 2;
       }
       return i;
    }
}
