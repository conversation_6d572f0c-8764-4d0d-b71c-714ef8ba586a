package android.taobao.windvane.webview.IWVWebView;
import java.util.concurrent.ConcurrentHashMap;
import android.content.Context;
import java.lang.Runnable;
import java.lang.String;
import java.lang.Object;
import android.webkit.ValueCallback;
import android.view.View;

public interface abstract IWVWebView	// class@00030c from classes.dex
{
    public static final ConcurrentHashMap JsbridgeHis;

    static {
       IWVWebView.JsbridgeHis = new ConcurrentHashMap();
    }
    Context _getContext();
    boolean _post(Runnable p0);
    void addJsObject(String p0,Object p1);
    boolean back();
    void clearCache();
    void evaluateJavascript(String p0);
    void evaluateJavascript(String p0,ValueCallback p1);
    void fireEvent(String p0,String p1);
    Context getContext();
    String getDataOnActive();
    Object getJsObject(String p0);
    String getUrl();
    String getUserAgentString();
    View getView();
    void hideLoadingView();
    void loadUrl(String p0);
    void refresh();
    void setDataOnActive(String p0);
    void setUserAgentString(String p0);
    void showLoadingView();
}
