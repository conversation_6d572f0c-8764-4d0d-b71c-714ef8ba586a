package a.a.a.a$a;
import a.a.a.a;
import android.os.Binder;
import tb.t2o;
import android.os.IBinder;
import java.lang.String;
import android.os.IInterface;
import a.a.a.a$a$a;

public abstract class a$a extends Binder implements a	// class@000007 from classes.dex
{

    static {
       t2o.a(0xa900002);
       t2o.a(0xa900001);
    }
    public static a a(IBinder p0){
       IInterface iInterface;
       if (p0 == null) {
          return null;
       }
       if ((iInterface = p0.queryLocalInterface("com.heytap.openid.IOpenID")) != null && iInterface instanceof a) {
          return iInterface;
       }
       return new a$a$a(p0);
    }
}
