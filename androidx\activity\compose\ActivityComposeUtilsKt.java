package androidx.activity.compose.ActivityComposeUtilsKt;
import android.content.Context;
import java.lang.Object;
import android.content.ContextWrapper;
import java.lang.String;
import tb.ckf;

public final class ActivityComposeUtilsKt	// class@000476 from classes.dex
{

    public static final Object findOwner(Context p0){
       if (!p0 instanceof ContextWrapper) {
          return null;
       }
       ckf.m(3, "T");
       throw null;
    }
}
