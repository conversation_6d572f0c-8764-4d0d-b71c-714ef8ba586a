package android.taobao.windvane.extra.WVPerformanceListenerManager;
import tb.t2o;
import java.lang.Object;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.List;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.IPerformanceListener;
import java.lang.Boolean;
import java.util.Map;
import tb.mqw;
import android.taobao.windvane.extra.WVPerformanceListenerManager$1;
import java.lang.Runnable;

public class WVPerformanceListenerManager	// class@000188 from classes.dex
{
    private final List mPerformanceListeners;
    public static IpChange $ipChange;
    private static WVPerformanceListenerManager sInstance;

    static {
       t2o.a(0x3d8000bc);
    }
    private void WVPerformanceListenerManager(){
       super();
       this.mPerformanceListeners = new CopyOnWriteArrayList();
    }
    public static List access$000(WVPerformanceListenerManager p0){
       IpChange $ipChange = WVPerformanceListenerManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mPerformanceListeners;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("dc67504c", objArray);
    }
    public static WVPerformanceListenerManager getInstance(){
       IpChange $ipChange = WVPerformanceListenerManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("dc4436e8", objArray);
       }else if(WVPerformanceListenerManager.sInstance == null){
          WVPerformanceListenerManager wVPerformanc = WVPerformanceListenerManager.class;
          _monitor_enter(wVPerformanc);
          if (WVPerformanceListenerManager.sInstance == null) {
             WVPerformanceListenerManager.sInstance = new WVPerformanceListenerManager();
          }
          _monitor_exit(wVPerformanc);
       }
       return WVPerformanceListenerManager.sInstance;
    }
    public boolean addPerformanceListener(IPerformanceListener p0){
       IpChange $ipChange = WVPerformanceListenerManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mPerformanceListeners.add(p0);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("5eaddbbf", objArray).booleanValue();
    }
    public void onWhitePageOccur(Map p0){
       IpChange $ipChange = WVPerformanceListenerManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f99bd10b", objArray);
          return;
       }else if(!this.mPerformanceListeners.size()){
          return;
       }else {
          mqw.a().b(new WVPerformanceListenerManager$1(this, p0));
          return;
       }
    }
    public boolean removePerformanceListener(IPerformanceListener p0){
       IpChange $ipChange = WVPerformanceListenerManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mPerformanceListeners.remove(p0);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("a0d437fc", objArray).booleanValue();
    }
}
