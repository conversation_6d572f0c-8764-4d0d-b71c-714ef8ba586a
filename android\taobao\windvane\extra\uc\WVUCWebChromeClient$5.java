package android.taobao.windvane.extra.uc.WVUCWebChromeClient$5;
import android.content.DialogInterface$OnClickListener;
import android.taobao.windvane.extra.uc.WVUCWebChromeClient;
import com.uc.webview.export.JsResult;
import java.lang.Object;
import android.content.DialogInterface;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;

public class WVUCWebChromeClient$5 implements DialogInterface$OnClickListener	// class@000247 from classes.dex
{
    public final WVUCWebChromeClient this$0;
    public final JsResult val$res;
    public static IpChange $ipChange;

    public void WVUCWebChromeClient$5(WVUCWebChromeClient p0,JsResult p1){
       this.this$0 = p0;
       this.val$res = p1;
       super();
    }
    public void onClick(DialogInterface p0,int p1){
       IpChange $ipChange = WVUCWebChromeClient$5.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1)};
          $ipChange.ipc$dispatch("7e49304d", objArray);
          return;
       }else {
          this.val$res.cancel();
          return;
       }
    }
}
