package android.taobao.windvane.connect.HttpConnector$NetWorkErrorException;
import java.lang.Exception;
import tb.t2o;
import android.taobao.windvane.connect.HttpConnector;
import java.lang.String;

public class HttpConnector$NetWorkErrorException extends Exception	// class@000150 from classes.dex
{
    public final HttpConnector this$0;

    static {
       t2o.a(0x3d80003e);
    }
    public void HttpConnector$NetWorkErrorException(HttpConnector p0,String p1){
       this.this$0 = p0;
       super(p1);
    }
}
