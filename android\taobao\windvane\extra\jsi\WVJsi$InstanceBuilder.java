package android.taobao.windvane.extra.jsi.WVJsi$InstanceBuilder;
import tb.t2o;
import android.content.Context;
import java.lang.Object;
import android.taobao.windvane.extra.jsi.JsiMode;
import android.os.Bundle;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.os.BaseBundle;
import java.lang.StringBuilder;
import com.alibaba.mtl.appmonitor.AppMonitor$Alarm;
import java.lang.Throwable;
import tb.v7t;
import android.taobao.windvane.extra.jsi.WVJsi$InstanceResult;
import android.taobao.windvane.extra.jsi.WVJsi;
import tb.hqf;
import android.taobao.windvane.extra.jsi.WVJsi$InstanceException;
import android.taobao.windvane.extra.jsi.WVJsi$1;
import com.alibaba.jsi.standard.JSEngine;
import java.lang.Exception;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import android.os.Handler;
import android.util.Log;
import tb.vpw;
import tb.wpw;
import java.lang.Integer;

public class WVJsi$InstanceBuilder	// class@0001ae from classes.dex
{
    private String bizName;
    private final Context context;
    private String dataDir;
    private String flags;
    private Handler handler;
    private JsiMode jsiMode;
    private String name;
    private int timeout;
    private String version;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000e4);
    }
    public void WVJsi$InstanceBuilder(Context p0){
       super();
       this.jsiMode = JsiMode.V8;
       this.context = p0;
    }
    private Bundle buildCommonArgs(){
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("2cb80381", objArray);
       }else {
          Bundle uBundle = new Bundle();
          uBundle.putString("name", this.name);
          uBundle.putString("version", this.version);
          uBundle.putString("datadir", this.dataDir);
          uBundle.putString("flags", this.flags);
          uBundle.putInt("timeout", this.timeout);
          return uBundle;
       }
    }
    private void commitInstanceFail(String p0,String p1,String p2){
       int i = 0;
       String str = "WVJsi";
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("a93cc2cf", objArray);
          return;
       }else {
          AppMonitor$Alarm.commitFail("WindVane", str, p0+"|"+this.name, p1, p2);
          return;
       }
    }
    private void commitInstanceSuccess(String p0){
       int i = 0;
       String str = "WVJsi";
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3e1632c8", objArray);
          return;
       }else {
          AppMonitor$Alarm.commitSuccess("WindVane", str, p0+"|"+this.name);
          return;
       }
    }
    private WVJsi$InstanceResult tryQjsInstance(Context p0){
       String str = "name";
       String str1 = "WVJsi/CREATE";
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("9ccbcbf1", objArray);
       }else {
          WVJsi.access$400(p0);
          int i = 3;
          if (!hqf.isEngineReady(i)) {
             this.commitInstanceFail("qjs", "QJS_NOT_READY", null);
             return new WVJsi$InstanceResult(null, new WVJsi$InstanceException("qjs engine is not ready", null), null);
          }else {
             Bundle uBundle = this.buildCommonArgs();
             uBundle.putInt("engine", i);
             RVLLevel info = RVLLevel.Info;
             lcn.a(info, str1).j("QJS_CREATE_START").a(str, this.name).f();
             WVJsi$InstanceResult instanceResu = new WVJsi$InstanceResult(JSEngine.createInstance(p0, uBundle, this.handler), null, null);
             if (instanceResu.isSuccess()) {
                this.commitInstanceSuccess("qjs");
                lcn.a(info, str1).j("QJS_CREATE_SUCCESS").a(str, this.name).f();
             }else {
                this.commitInstanceFail("qjs", "CREATE_FAIL", null);
                lcn.a(RVLLevel.Error, str1).j("QJS_CREATE_FAIL").a(str, this.name).f();
             }
             return instanceResu;
          }
       }
    }
    private WVJsi$InstanceResult tryV8Instance(Context p0){
       int i = 1;
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("b8b6eecf", objArray);
       }else {
          WVJsi.access$200(p0);
          if (!hqf.isEngineReady(i)) {
             this.commitInstanceFail("v8", "V8_NOT_READY", null);
             lcn.a(RVLLevel.Error, "WVJsi/CREATE").j("V8_NOT_READY").a("name", this.name).f();
             return new WVJsi$InstanceResult(null, new WVJsi$InstanceException("v8 engine is not ready", null), null);
          }else {
             Bundle uBundle = this.buildCommonArgs();
             uBundle.putInt("engine", i);
             RVLLevel info = RVLLevel.Info;
             lcn.a(info, "WVJsi/CREATE").j("V8_CREATE_START").a("name", this.name).f();
             WVJsi$InstanceResult instanceResu = new WVJsi$InstanceResult(JSEngine.createInstance(p0, uBundle, this.handler), null, null);
             if (instanceResu.isSuccess()) {
                this.commitInstanceSuccess("v8");
                lcn.a(info, "WVJsi/CREATE").j("V8_CREATE_SUCCESS").a("name", this.name).f();
             }else {
                this.commitInstanceFail("v8", "CREATE_FAIL", null);
                lcn.a(RVLLevel.Error, "WVJsi/CREATE").j("V8_CREATE_FAIL").a("name", this.name).f();
             }
             return instanceResu;
          }
       }
    }
    public WVJsi$InstanceBuilder bizName(String p0){
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ce0b544f", objArray);
       }else {
          this.bizName = p0;
          return this;
       }
    }
    public WVJsi$InstanceResult build(){
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("2129e129", objArray);
       }else if(vpw.commonConfig.Z2 == null && !this.jsiMode.isV8Enable()){
          v7t.n("WVJsi", "v8 is required to be enabled");
       }else {
          WVJsi$InstanceResult instanceResu = this.tryV8Instance(this.context);
          if (instanceResu.isSuccess()) {
             return instanceResu;
          }
       }
       if (this.jsiMode.isQjsEnable()) {
          return this.tryQjsInstance(this.context);
       }else {
          return new WVJsi$InstanceResult(null, new WVJsi$InstanceException("instance create fail:"+this.jsiMode, null), null);
       }
    }
    public WVJsi$InstanceBuilder dataDir(String p0){
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ffb429d4", objArray);
       }else {
          this.dataDir = p0;
          return this;
       }
    }
    public WVJsi$InstanceBuilder flags(String p0){
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("71711cd8", objArray);
       }else {
          this.flags = p0;
          return this;
       }
    }
    public WVJsi$InstanceBuilder handler(Handler p0){
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6bf39781", objArray);
       }else {
          this.handler = p0;
          return this;
       }
    }
    public WVJsi$InstanceBuilder mode(JsiMode p0){
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("829080a6", objArray);
       }else {
          this.jsiMode = p0;
          return this;
       }
    }
    public WVJsi$InstanceBuilder name(String p0){
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("e2f331dc", objArray);
       }else {
          this.name = p0;
          return this;
       }
    }
    public WVJsi$InstanceBuilder timeout(int p0){
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          return $ipChange.ipc$dispatch("ab6de49b", objArray);
       }else {
          this.timeout = p0;
          return this;
       }
    }
    public WVJsi$InstanceBuilder version(String p0){
       IpChange $ipChange = WVJsi$InstanceBuilder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("906d6169", objArray);
       }else {
          this.version = p0;
          return this;
       }
    }
}
