package androidx.activity.compose.ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import java.lang.String;
import java.util.UUID;

public final class ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1 extends Lambda implements d1a	// class@00047a from classes.dex
{
    public static final ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1 INSTANCE;

    static {
       ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1.INSTANCE = new ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1();
    }
    public void ActivityResultRegistryKt$rememberLauncherForActivityResult$key$1(){
       super(0);
    }
    public Object invoke(){
       return this.invoke();
    }
    public final String invoke(){
       return UUID.randomUUID().toString();
    }
}
