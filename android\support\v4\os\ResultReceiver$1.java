package android.support.v4.os.ResultReceiver$1;
import android.os.Parcelable$Creator;
import java.lang.Object;
import android.os.Parcel;
import android.support.v4.os.ResultReceiver;

class ResultReceiver$1 implements Parcelable$Creator	// class@000138 from classes.dex
{

    public void ResultReceiver$1(){
       super();
    }
    public ResultReceiver createFromParcel(Parcel p0){
       return new ResultReceiver(p0);
    }
    public Object createFromParcel(Parcel p0){
       return this.createFromParcel(p0);
    }
    public ResultReceiver[] newArray(int p0){
       ResultReceiver[] resultReceiv = new ResultReceiver[p0];
       return resultReceiv;
    }
    public Object[] newArray(int p0){
       return this.newArray(p0);
    }
}
