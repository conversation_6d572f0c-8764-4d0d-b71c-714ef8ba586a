package android.support.v4.app.RemoteActionCompatParcelizer;
import androidx.core.app.RemoteActionCompatParcelizer;
import androidx.versionedparcelable.VersionedParcel;
import androidx.core.app.RemoteActionCompat;

public final class RemoteActionCompatParcelizer extends RemoteActionCompatParcelizer	// class@00012c from classes.dex
{

    public void RemoteActionCompatParcelizer(){
       super();
    }
    public static RemoteActionCompat read(VersionedParcel p0){
       return RemoteActionCompatParcelizer.read(p0);
    }
    public static void write(RemoteActionCompat p0,VersionedParcel p1){
       RemoteActionCompatParcelizer.write(p0, p1);
    }
}
