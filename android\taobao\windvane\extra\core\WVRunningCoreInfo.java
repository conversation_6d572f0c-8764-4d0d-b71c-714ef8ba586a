package android.taobao.windvane.extra.core.WVRunningCoreInfo;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import com.uc.webview.export.extension.ICoreVersion;
import com.uc.webview.export.extension.IRunningCoreInfo;

public class WVRunningCoreInfo	// class@00018f from classes.dex
{
    public static IpChange $ipChange;
    private static IRunningCoreInfo sRunningCoreInfo;

    static {
       t2o.a(0x3d8000c4);
    }
    public void WVRunningCoreInfo(){
       super();
    }
    public static String getCoreVersion(){
       IRunningCoreInfo sRunningCore;
       ICoreVersion iCoreVersion;
       IpChange $ipChange = WVRunningCoreInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("980bf3d6", objArray);
       }else if((sRunningCore = WVRunningCoreInfo.sRunningCoreInfo) == null){
          return null;
       }else if((iCoreVersion = sRunningCore.coreVersion()) != null){
          return iCoreVersion.version();
       }else {
          return null;
       }
    }
    public static void setRunningCoreInfo(IRunningCoreInfo p0){
       IpChange $ipChange = WVRunningCoreInfo.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("df8acadb", objArray);
          return;
       }else {
          WVRunningCoreInfo.sRunningCoreInfo = p0;
          return;
       }
    }
}
