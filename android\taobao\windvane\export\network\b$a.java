package android.taobao.windvane.export.network.b$a;
import android.taobao.windvane.export.network.RequestCallback;
import android.taobao.windvane.export.network.Request;
import android.taobao.windvane.export.network.RequestCache;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import java.lang.Boolean;
import java.util.Map;

public final class b$a extends RequestCallback	// class@000171 from classes.dex
{
    public final Request a;
    public final RequestCache b;
    public final RequestCallback c;
    public final int d;
    public static IpChange $ipChange;

    public void b$a(Request p0,RequestCache p1,RequestCallback p2,int p3){
       this.a = p0;
       this.b = p1;
       this.c = p2;
       this.d = p3;
       super();
    }
    public static Object ipc$super(b$a p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/export/network/NetworkService$1");
    }
    public void onCustomCallback(int p0,Object[] p1){
       b$a tc;
       IpChange $ipChange = b$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("c344bdea", objArray);
          return;
       }else {
          this.b.onCustomCallback(p0, p1);
          if ((tc = this.c) != null) {
             tc.onCustomCallback(p0, p1);
          }
          return;
       }
    }
    public void onError(int p0,String p1){
       b$a tc;
       IpChange $ipChange = b$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("7a671c9d", objArray);
          return;
       }else {
          this.b.onError(p0, p1);
          if ((tc = this.c) != null) {
             tc.onError(p0, p1);
          }
          lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("finish").a("requestId", Integer.valueOf(this.d)).a("success", Boolean.FALSE).a("errorMsg", p1).f();
          this.a.l("documentRequestEnd");
          return;
       }
    }
    public void onFinish(){
       b$a tc;
       IpChange $ipChange = b$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("badeed9", objArray);
          return;
       }else {
          this.b.onFinish();
          if ((tc = this.c) != null) {
             tc.onFinish();
          }
          lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("finish").a("requestId", Integer.valueOf(this.d)).a("success", Boolean.TRUE).f();
          this.a.l("documentRequestEnd");
          return;
       }
    }
    public void onNetworkResponse(int p0,Map p1){
       b$a tc;
       IpChange $ipChange = b$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("92d71559", objArray);
          return;
       }else {
          this.a.l("documentNetworkTTFB");
          this.b.onNetworkResponse(p0, p1);
          if ((tc = this.c) != null) {
             tc.onNetworkResponse(p0, p1);
          }
          lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("onNetworkResponse").a("requestId", Integer.valueOf(this.d)).a("headers", p1).f();
          return;
       }
    }
    public void onReceiveData(byte[] p0){
       b$a tc;
       IpChange $ipChange = b$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f24c16dc", objArray);
          return;
       }else {
          this.b.onReceiveData(p0);
          if ((tc = this.c) != null) {
             tc.onReceiveData(p0);
          }
          return;
       }
    }
    public void onResponse(int p0,Map p1){
       b$a tc;
       IpChange $ipChange = b$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("bb214fe9", objArray);
          return;
       }else {
          this.a.l("documentTTFB");
          this.b.onResponse(p0, p1);
          if ((tc = this.c) != null) {
             tc.onResponse(p0, p1);
          }
          lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("onResponse").a("requestId", Integer.valueOf(this.d)).a("headers", p1).f();
          return;
       }
    }
}
