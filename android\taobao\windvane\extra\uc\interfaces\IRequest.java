package android.taobao.windvane.extra.uc.interfaces.IRequest;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import java.util.Map;
import java.lang.String;
import android.taobao.windvane.extra.uc.interfaces.IRequestTiming;

public interface abstract IRequest	// class@000274 from classes.dex
{

    void cancel();
    EventHandler getEventHandler();
    Map getHeaders();
    String getMethod();
    IRequestTiming getRequestTiming();
    Map getUploadDataMap();
    Map getUploadFileMap();
    long getUploadFileTotalLen();
    String getUrl();
    void setEventHandler(EventHandler p0);
    void setRequestTiming(IRequestTiming p0);
}
