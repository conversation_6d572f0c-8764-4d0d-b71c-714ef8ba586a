package android.taobao.windvane.extra.uc.Escape;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.net.URL;
import java.lang.StringBuffer;
import java.lang.StringBuilder;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.net.URLEncoder;
import java.net.URLDecoder;
import java.net.URI;

public class Escape	// class@00020f from classes.dex
{
    public static IpChange $ipChange;
    private static final String[] hex;
    private static final byte[] val;

    static {
       t2o.a(0x3d800144);
       String[] stringArray = new String[256];
       stringArray[0] = "00";
       stringArray[1] = "01";
       stringArray[2] = "02";
       stringArray[3] = "03";
       stringArray[4] = "04";
       stringArray[5] = "05";
       stringArray[6] = "06";
       stringArray[7] = "07";
       stringArray[8] = "08";
       stringArray[9] = "09";
       stringArray[10] = "0A";
       stringArray[11] = "0B";
       stringArray[12] = "0C";
       stringArray[13] = "0D";
       stringArray[14] = "0E";
       stringArray[15] = "0F";
       stringArray[16] = "10";
       stringArray[17] = "11";
       stringArray[18] = "12";
       stringArray[19] = "13";
       stringArray[20] = "14";
       stringArray[21] = "15";
       stringArray[22] = "16";
       stringArray[23] = "17";
       stringArray[24] = "18";
       stringArray[25] = "19";
       stringArray[26] = "1A";
       stringArray[27] = "1B";
       stringArray[28] = "1C";
       stringArray[29] = "1D";
       stringArray[30] = "1E";
       stringArray[31] = "1F";
       stringArray[32] = "20";
       stringArray[33] = "21";
       stringArray[34] = "22";
       stringArray[35] = "23";
       stringArray[36] = "24";
       stringArray[37] = "25";
       stringArray[38] = "26";
       stringArray[39] = "27";
       stringArray[40] = "28";
       stringArray[41] = "29";
       stringArray[42] = "2A";
       stringArray[43] = "2B";
       stringArray[44] = "2C";
       stringArray[45] = "2D";
       stringArray[46] = "2E";
       stringArray[47] = "2F";
       stringArray[48] = "30";
       stringArray[49] = "31";
       stringArray[50] = "32";
       stringArray[51] = "33";
       stringArray[52] = "34";
       stringArray[53] = "35";
       stringArray[54] = "36";
       stringArray[55] = "37";
       stringArray[56] = "38";
       stringArray[57] = "39";
       stringArray[58] = "3A";
       stringArray[59] = "3B";
       stringArray[60] = "3C";
       stringArray[61] = "3D";
       stringArray[62] = "3E";
       stringArray[63] = "3F";
       stringArray[64] = "40";
       stringArray[65] = "41";
       stringArray[66] = "42";
       stringArray[67] = "43";
       stringArray[68] = "44";
       stringArray[69] = "45";
       stringArray[70] = "46";
       stringArray[71] = "47";
       stringArray[72] = "48";
       stringArray[73] = "49";
       stringArray[74] = "4A";
       stringArray[75] = "4B";
       stringArray[76] = "4C";
       stringArray[77] = "4D";
       stringArray[78] = "4E";
       stringArray[79] = "4F";
       stringArray[80] = "50";
       stringArray[81] = "51";
       stringArray[82] = "52";
       stringArray[83] = "53";
       stringArray[84] = "54";
       stringArray[85] = "55";
       stringArray[86] = "56";
       stringArray[87] = "57";
       stringArray[88] = "58";
       stringArray[89] = "59";
       stringArray[90] = "5A";
       stringArray[91] = "5B";
       stringArray[92] = "5C";
       stringArray[93] = "5D";
       stringArray[94] = "5E";
       stringArray[95] = "5F";
       stringArray[96] = "60";
       stringArray[97] = "61";
       stringArray[98] = "62";
       stringArray[99] = "63";
       stringArray[100] = "64";
       stringArray[101] = "65";
       stringArray[102] = "66";
       stringArray[103] = "67";
       stringArray[104] = "68";
       stringArray[105] = "69";
       stringArray[106] = "6A";
       stringArray[107] = "6B";
       stringArray[108] = "6C";
       stringArray[109] = "6D";
       stringArray[110] = "6E";
       stringArray[111] = "6F";
       stringArray[112] = "70";
       stringArray[113] = "71";
       stringArray[114] = "72";
       stringArray[115] = "73";
       stringArray[116] = "74";
       stringArray[117] = "75";
       stringArray[118] = "76";
       stringArray[119] = "77";
       stringArray[120] = "78";
       stringArray[121] = "79";
       stringArray[122] = "7A";
       stringArray[123] = "7B";
       stringArray[124] = "7C";
       stringArray[125] = "7D";
       stringArray[126] = "7E";
       stringArray[127] = "7F";
       stringArray[128] = "80";
       stringArray[129] = "81";
       stringArray[130] = "82";
       stringArray[131] = "83";
       stringArray[132] = "84";
       stringArray[133] = "85";
       stringArray[134] = "86";
       stringArray[135] = "87";
       stringArray[136] = "88";
       stringArray[137] = "89";
       stringArray[138] = "8A";
       stringArray[139] = "8B";
       stringArray[140] = "8C";
       stringArray[141] = "8D";
       stringArray[142] = "8E";
       stringArray[143] = "8F";
       stringArray[144] = "90";
       stringArray[145] = "91";
       stringArray[146] = "92";
       stringArray[147] = "93";
       stringArray[148] = "94";
       stringArray[149] = "95";
       stringArray[150] = "96";
       stringArray[151] = "97";
       stringArray[152] = "98";
       stringArray[153] = "99";
       stringArray[154] = "9A";
       stringArray[155] = "9B";
       stringArray[156] = "9C";
       stringArray[157] = "9D";
       stringArray[158] = "9E";
       stringArray[159] = "9F";
       stringArray[160] = "A0";
       stringArray[161] = "A1";
       stringArray[162] = "A2";
       stringArray[163] = "A3";
       stringArray[164] = "A4";
       stringArray[165] = "A5";
       stringArray[166] = "A6";
       stringArray[167] = "A7";
       stringArray[168] = "A8";
       stringArray[169] = "A9";
       stringArray[170] = "AA";
       stringArray[171] = "AB";
       stringArray[172] = "AC";
       stringArray[173] = "AD";
       stringArray[174] = "AE";
       stringArray[175] = "AF";
       stringArray[176] = "B0";
       stringArray[177] = "B1";
       stringArray[178] = "B2";
       stringArray[179] = "B3";
       stringArray[180] = "B4";
       stringArray[181] = "B5";
       stringArray[182] = "B6";
       stringArray[183] = "B7";
       stringArray[184] = "B8";
       stringArray[185] = "B9";
       stringArray[186] = "BA";
       stringArray[187] = "BB";
       stringArray[188] = "BC";
       stringArray[189] = "BD";
       stringArray[190] = "BE";
       stringArray[191] = "BF";
       stringArray[192] = "C0";
       stringArray[193] = "C1";
       stringArray[194] = "C2";
       stringArray[195] = "C3";
       stringArray[196] = "C4";
       stringArray[197] = "C5";
       stringArray[198] = "C6";
       stringArray[199] = "C7";
       stringArray[200] = "C8";
       stringArray[201] = "C9";
       stringArray[202] = "CA";
       stringArray[203] = "CB";
       stringArray[204] = "CC";
       stringArray[205] = "CD";
       stringArray[206] = "CE";
       stringArray[207] = "CF";
       stringArray[208] = "D0";
       stringArray[209] = "D1";
       stringArray[210] = "D2";
       stringArray[211] = "D3";
       stringArray[212] = "D4";
       stringArray[213] = "D5";
       stringArray[214] = "D6";
       stringArray[215] = "D7";
       stringArray[216] = "D8";
       stringArray[217] = "D9";
       stringArray[218] = "DA";
       stringArray[219] = "DB";
       stringArray[220] = "DC";
       stringArray[221] = "DD";
       stringArray[222] = "DE";
       stringArray[223] = "DF";
       stringArray[224] = "E0";
       stringArray[225] = "E1";
       stringArray[226] = "E2";
       stringArray[227] = "E3";
       stringArray[228] = "E4";
       stringArray[229] = "E5";
       stringArray[230] = "E6";
       stringArray[231] = "E7";
       stringArray[232] = "E8";
       stringArray[233] = "E9";
       stringArray[234] = "EA";
       stringArray[235] = "EB";
       stringArray[236] = "EC";
       stringArray[237] = "ED";
       stringArray[238] = "EE";
       stringArray[239] = "EF";
       stringArray[240] = "F0";
       stringArray[241] = "F1";
       stringArray[242] = "F2";
       stringArray[243] = "F3";
       stringArray[244] = "F4";
       stringArray[245] = "F5";
       stringArray[246] = "F6";
       stringArray[247] = "F7";
       stringArray[248] = "F8";
       stringArray[249] = "F9";
       stringArray[250] = "FA";
       stringArray[251] = "FB";
       stringArray[252] = "FC";
       stringArray[253] = "FD";
       stringArray[254] = "FE";
       stringArray[255] = "FF";
       Escape.hex = stringArray;
       Escape.val = new byte[256]{'?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?',0x00,0x01,0x02,0x03,0x04,0x05,0x06,0x07,0x08,0x09,'?','?','?','?','?','?','?',0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,'?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?',0x0a,0x0b,0x0c,0x0d,0x0e,0x0f,'?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?','?'};
    }
    public void Escape(){
       super();
    }
    private static String decodeUrl(String p0){
       int port;
       String[] stringArray;
       int i = 1;
       String str = "&";
       String str1 = ":";
       IpChange $ipChange = Escape.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("249cbe6e", objArray);
       }else {
          URL uRL = new URL(p0);
          StringBuffer str2 = uRL.getProtocol()+"://"+uRL.getHost();
          if ((port = uRL.getPort()) != 80 && port != -1) {
             str2 = str2+str1+port;
          }
          str1 = uRL.getPath();
          String query = uRL.getQuery();
          if (!TextUtils.isEmpty(str1)) {
             String str3 = "/";
             port = (str1.length() > i && str1.lastIndexOf(str3) == (str1.length() - i))? 1: 0;
             if ((stringArray = str1.split(str3)) != null) {
                str2 = str2+str3;
                int len = stringArray.length;
                int i1 = 0;
                while (i1 < len) {
                   if (!TextUtils.isEmpty(stringArray[i1])) {
                      str2 = str2.append(URLEncoder.encode(Escape.tryDecode(stringArray[i1]), "utf-8"));
                      int i2 = len - 1;
                      if (i1 < i2) {
                         str2 = str2.append(str3);
                      }
                   }
                   i1 = i1 + i;
                }
             }
             if (port) {
                str2 = str2+str3;
             }
          }
          if (!TextUtils.isEmpty(query)) {
             str2 = str2+"?";
             if ((stringArray = query.split(str)) != null) {
                int len1 = stringArray.length;
                port = 0;
                while (port < len1) {
                   int i3 = stringArray[port].indexOf(61);
                   if (-1 != i3) {
                      i3 = i3 + i;
                      str2 = str2.append("".append(stringArray[port].substring(0, i3)).append("=").append(URLEncoder.encode(Escape.tryDecode(stringArray[port].substring(i3)), "utf-8")).toString());
                      i3 = len1 - 1;
                      if (port < i3) {
                         str2 = str2.append(str);
                      }
                   }
                   port = port + i;
                }
             }
          }
          return str2;
       }
    }
    public static String escape(String p0){
       char c;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = Escape.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("f3c4ee8e", objArray);
       }else {
          StringBuffer str = "";
          int i2 = p0.length();
          while (i < i2) {
             if ((c = p0.charAt(i)) == ' ') {
                str = str.append('+');
             }else if(65 <= c && c <= 'Z'){
                str = str.append((char)c);
             }else if(97 <= c && c <= 'z'){
                str = str.append((char)c);
             }else if(48 <= c && c <= '9'){
                str = str.append((char)c);
             }else if(c != '-' && (c != '_' && (c != '.' && (c != '!' && (c != '~' && (c != '*' && (c != '/' && (c != '(' && c != ')')))))))){
                if (c <= 127) {
                   str = str.append('%').append(Escape.hex[c]);
                }else {
                   String[] hex = Escape.hex;
                   int i3 = c >> 8;
                   int i4 = c & 0x00ff;
                   str = str.append("%u").append(hex[i3]).append(hex[i4]);
                }
             }else {
                str = str.append((char)c);
             }
             i = i + i1;
          }
          return str;
       }
    }
    private static String tryDecode(String p0){
       IpChange $ipChange = Escape.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("55dd4e86", objArray);
       }else {
          String str = "";
          if (!TextUtils.isEmpty(p0)) {
             str = URLDecoder.decode(p0, "utf-8");
             if (TextUtils.isEmpty(str)) {
                str = URLDecoder.decode(p0, "gbk");
             }
             if (TextUtils.isEmpty(str)) {
                str = Escape.unescape(p0);
             }
             if (TextUtils.isEmpty(str)) {
             label_0049 :
                return p0;
             }
          }
          p0 = str;
          goto label_0049 ;
       }
    }
    public static String tryDecodeUrl(String p0){
       IpChange $ipChange = Escape.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("ebe7c249", objArray);
       }else {
          URI.create(p0);
          return p0;
       }
    }
    public static String unescape(String p0){
       char c;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = Escape.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("55f74495", objArray);
       }else {
          StringBuffer str = "";
          int i2 = p0.length();
          while (i < i2) {
             if ((c = p0.charAt(i)) == '+') {
                str = str.append(' ');
             }else if(65 <= c && c <= 'Z'){
                str = str.append((char)c);
             }else if(97 <= c && c <= 'z'){
                str = str.append((char)c);
             }else if(48 <= c && c <= '9'){
                str = str.append((char)c);
             }else if(c != '-' && (c != '_' && (c != '.' && (c != '!' && (c != '~' && (c != '*' && (c != '/' && (c != '(' && c != ')')))))))){
                if (c == '%') {
                   int i3 = i + 1;
                   if (117 != p0.charAt(i3)) {
                      byte[] val = Escape.val;
                      i3 = val[p0.charAt(i3)] << 4;
                      i = i + 2;
                      i3 = i3 | val[p0.charAt(i)];
                   }else {
                      byte[] val1 = Escape.val;
                      int i4 = i + 2;
                      i4 = val1[p0.charAt(i4)] << 4;
                      int i5 = i + 3;
                      i4 = i4 | val1[p0.charAt(i5)];
                      i4 = i4 << 4;
                      i5 = i + 4;
                      i4 = i4 | val1[p0.charAt(i5)];
                      i4 = i4 << 4;
                      i = i + 5;
                      i3 = val1[p0.charAt(i)] | i4;
                   }
                   str = str.append((char)i3);
                }
             }else {
                str = str.append((char)c);
             }
             i = i + i1;
          }
          return str;
       }
    }
}
