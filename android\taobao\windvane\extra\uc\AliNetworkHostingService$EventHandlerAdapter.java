package android.taobao.windvane.extra.uc.AliNetworkHostingService$EventHandlerAdapter;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import tb.t2o;
import android.taobao.windvane.extra.uc.AliNetworkHostingService;
import java.lang.String;
import com.uc.webview.export.extension.INetworkHostingService$IDelegate;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import android.taobao.windvane.extra.uc.interfaces.IRequestTiming;
import java.lang.Number;
import java.lang.RuntimeException;
import java.util.Map;
import java.util.ArrayList;
import java.lang.StringBuilder;
import tb.v7t;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import java.util.List;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.vpw;
import tb.wpw;
import tb.w9o;
import tb.ace;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Boolean;
import android.taobao.windvane.extra.uc.interfaces.IRequest;

public class AliNetworkHostingService$EventHandlerAdapter implements EventHandler	// class@000202 from classes.dex
{
    private final INetworkHostingService$IDelegate mDelegate;
    private boolean mFirstDataFlag;
    private int mNetworkEngin;
    private IRequest mRequest;
    private IRequestTiming mRequestTiming;
    private int mResourceType;
    private String mStatusLine;
    private final String mUrl;
    private WVUCWebView mWebView;
    public final AliNetworkHostingService this$0;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800138);
       t2o.a(0x3d8001a6);
    }
    public void AliNetworkHostingService$EventHandlerAdapter(AliNetworkHostingService p0,String p1,INetworkHostingService$IDelegate p2){
       this.this$0 = p0;
       super();
       this.mFirstDataFlag = true;
       this.mUrl = p1;
       this.mDelegate = p2;
    }
    public void data(byte[] p0,int p1){
       AliNetworkHostingService$EventHandlerAdapter tmRequestTim;
       int i = 0;
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1)};
          $ipChange.ipc$dispatch("f3fee4b", objArray);
          return;
       }else {
          this.mDelegate.onDataReceived(p0, p1);
          if (this.mFirstDataFlag != null) {
             this.mFirstDataFlag = i;
             if ((tmRequestTim = this.mRequestTiming) != null) {
                tmRequestTim.markNativeRequestFirstDataTime();
             }
          }
          return;
       }
    }
    public void endData(){
       AliNetworkHostingService$EventHandlerAdapter tmRequestTim;
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("eef712c", objArray);
          return;
       }else {
          this.mDelegate.onFinished();
          if ((tmRequestTim = this.mRequestTiming) != null) {
             tmRequestTim.markNativeRequestEndTime();
          }
          return;
       }
    }
    public void error(int p0,String p1){
       AliNetworkHostingService$EventHandlerAdapter tmRequestTim;
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("89556f1e", objArray);
          return;
       }else {
          this.mDelegate.onError(p0, p1);
          if ((tmRequestTim = this.mRequestTiming) != null) {
             tmRequestTim.markNativeRequestEndTime();
          }
          return;
       }
    }
    public int getResourceType(){
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mResourceType;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("14b68858", objArray).intValue();
    }
    public void headers(Object p0){
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          throw new RuntimeException("UNSUPPORT");
       }
       Object[] objArray = new Object[]{this,p0};
       $ipChange.ipc$dispatch("78ea67a9", objArray);
       return;
    }
    public void headers(Map p0){
       AliNetworkHostingService$EventHandlerAdapter tmWebView;
       int i = 0;
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("b1d23ed8", objArray);
          return;
       }else {
          ArrayList uArrayList = new ArrayList();
          ArrayList uArrayList1 = new ArrayList();
          v7t.a("alinetwork-service", "[onResponseCode] in. url="+this.mUrl+",headers="+p0);
          Iterator iterator = p0.entrySet().iterator();
          CharSequence uCharSequenc = null;
          while (iterator.hasNext()) {
             Map$Entry uEntry = iterator.next();
             if (uEntry.getKey() != null && (uEntry.getKey().equalsIgnoreCase("content-encoding") && (uEntry.getValue() != null && uEntry.getValue().get(i).equalsIgnoreCase("gzip")))) {
                v7t.a("alinetwork-service", "[onResponseCode] has gzip header. url=".append(this.mUrl).toString());
             }else {
                Iterator iterator1 = uEntry.getValue().iterator();
                while (iterator1.hasNext()) {
                   String str = iterator1.next();
                   if (uEntry.getKey() == null) {
                      uCharSequenc = uEntry.getValue().get(i);
                   }else {
                      uArrayList.add(uEntry.getKey());
                      uArrayList1.add(str);
                      continue ;
                   }
                }
             }
          }
          if (!TextUtils.isEmpty(uCharSequenc)) {
             this.mStatusLine = uCharSequenc;
          }
          String[] stringArray = new String[uArrayList.size()];
          String[] stringArray1 = new String[uArrayList1.size()];
          v7t.a("alinetwork-service", "[onResponseCode] in2.");
          this.mDelegate.onResponseReceived(this.mStatusLine, uArrayList.toArray(stringArray), uArrayList1.toArray(stringArray1));
          v7t.a("alinetwork-service", "[onResponseCode] in3.");
          if (vpw.commonConfig.z2 != null && (this.mResourceType == null && (tmWebView = this.mWebView) != null)) {
             w9o ow9o = new w9o();
             ow9o.a = p0;
             tmWebView.getWebViewContext().setMainFrameResponseInfo(ow9o);
          }
          return;
       }
    }
    public boolean isSynchronous(){
       int i = 0;
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          i = $ipChange.ipc$dispatch("b41964ca", objArray).booleanValue();
       }
       return i;
    }
    public void setRequest(IRequest p0){
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4e61966", objArray);
          return;
       }else {
          this.mRequest = p0;
          return;
       }
    }
    public void setRequestTiming(IRequestTiming p0){
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d11bbce6", objArray);
          return;
       }else {
          this.mRequestTiming = p0;
          return;
       }
    }
    public void setResourceType(int p0){
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("5db103b2", objArray);
          return;
       }else {
          this.mResourceType = p0;
          return;
       }
    }
    public void setWebView(WVUCWebView p0){
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("766db43f", objArray);
          return;
       }else {
          this.mWebView = p0;
          return;
       }
    }
    public void status(int p0,int p1,int p2,String p3){
       AliNetworkHostingService$EventHandlerAdapter tmRequestTim;
       IpChange $ipChange = AliNetworkHostingService$EventHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),new Integer(p1),new Integer(p2),p3};
          $ipChange.ipc$dispatch("6f380fd4", objArray);
          return;
       }else {
          String str = "HTTP/";
          String str1 = (2 == p0)? "2.0": "1.1";
          this.mStatusLine = str+str1+" "+p2+" "+p3;
          if ((tmRequestTim = this.mRequestTiming) != null) {
             tmRequestTim.markNativeRequestResponseTime();
          }
          return;
       }
    }
}
