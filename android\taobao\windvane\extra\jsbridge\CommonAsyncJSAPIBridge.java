package android.taobao.windvane.extra.jsbridge.CommonAsyncJSAPIBridge;
import java.io.Serializable;
import com.uc.webview.export.extension.JSInterface;
import tb.t2o;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import java.lang.String;
import tb.ckf;
import com.android.alibaba.ip.runtime.IpChange;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.uc.webview.export.extension.JSInterface$JSRoute;
import java.lang.Boolean;
import com.uc.webview.export.WebView;
import tb.vpw;
import tb.wpw;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import android.webkit.ValueCallback;
import java.lang.Integer;
import android.taobao.windvane.extra.jsbridge.CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1;
import tb.yd1;
import android.taobao.windvane.extra.jsbridge.CommonAsyncJSAPIBridge$sam$java_lang_Runnable$0;
import tb.d1a;
import java.lang.Runnable;

public final class CommonAsyncJSAPIBridge extends JSInterface implements Serializable	// class@000196 from classes.dex
{
    private final String parentId;
    private final WVUCWebView webView;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000c8);
    }
    public void CommonAsyncJSAPIBridge(WVUCWebView p0){
       ckf.g(p0, "webView");
       super();
       this.webView = p0;
       this.parentId = p0.getCurId();
    }
    public static final String access$getParentId$p(CommonAsyncJSAPIBridge p0){
       IpChange $ipChange = CommonAsyncJSAPIBridge.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.parentId;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("ea08e670", objArray);
    }
    private final String getResultForLogging(String p0){
       IpChange $ipChange = CommonAsyncJSAPIBridge.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("90b3b046", objArray);
       }else if(p0 == null){
          return null;
       }else if(p0.length() < 0x2710){
          return p0;
       }else {
          return "RESULT_TOO_LONG";
       }
    }
    public static Object ipc$super(CommonAsyncJSAPIBridge p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/jsbridge/CommonAsyncJSAPIBridge");
    }
    private final void sendJSResult(String p0,JSInterface$JSRoute p1,boolean p2,String p3,boolean p4){
       int i = 4;
       IpChange $ipChange = CommonAsyncJSAPIBridge.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Boolean(p2),p3,new Boolean(p4)};
          $ipChange.ipc$dispatch("2e690c6e", objArray);
          return;
       }else if(this.webView.isDestroied()){
          return;
       }else if(vpw.commonConfig.C2 != null){
          icn oicn = lcn.a(RVLLevel.Info, "Bridge").m(this.parentId).k("callback", p0);
          String str = (p2)? "SUCCESS": "FAILED";
          oicn.a("status", str).a("result", this.getResultForLogging(p3)).a("keepAlive", Boolean.valueOf(p4)).f();
       }
       Object[] objArray1 = new Object[i];
       objArray1[0] = Boolean.valueOf(p2);
       objArray1[1] = p3;
       objArray1[2] = p0;
       objArray1[3] = Boolean.valueOf(p4);
       p1.send(objArray1, null);
       return;
    }
    public static void sendJSResult$default(CommonAsyncJSAPIBridge p0,String p1,JSInterface$JSRoute p2,boolean p3,String p4,boolean p5,int p6,Object p7){
       int i = p6;
       int i1 = 4;
       int i2 = 1;
       IpChange $ipChange = CommonAsyncJSAPIBridge.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2,new Boolean(p3),p4,new Boolean(p5),new Integer(i),p7};
          $ipChange.ipc$dispatch("819e0c48", objArray);
          return;
       }else {
          int i3 = p3;
          int i4 = p5;
          if (!((i1 & i))) {
             i2 = i3;
          }
          String str = ((i & 0x08))? "": p4;
          boolean b = ((i & 0x10))? false: i4;
          p0.sendJSResult(p1, p2, i2, str, b);
          return;
       }
    }
    public final WVUCWebView getWebView(){
       IpChange $ipChange = CommonAsyncJSAPIBridge.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.webView;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("151ffdcd", objArray);
    }
    public final void invokeNativeMethod(String p0,String p1,String p2,String p3){
       yd1 asyncApiProx;
       IpChange $ipChange = CommonAsyncJSAPIBridge.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          $ipChange.ipc$dispatch("d7f9221f", objArray);
          return;
       }else if(this.webView.isDestroied()){
          return;
       }else {
          CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 $ipChange1 = new CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1(this, p0, p1, p2, p3, this.getJSRoute(), this.getUrl());
          if ((asyncApiProx = this.webView.getAsyncApiProxy()) != null) {
             asyncApiProx.apiCall(new CommonAsyncJSAPIBridge$sam$java_lang_Runnable$0($ipChange));
          }
          return;
       }
    }
}
