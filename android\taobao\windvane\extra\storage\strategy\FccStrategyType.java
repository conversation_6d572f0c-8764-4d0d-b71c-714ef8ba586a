package android.taobao.windvane.extra.storage.strategy.FccStrategyType;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class FccStrategyType extends Enum	// class@0001f6 from classes.dex
{
    private static final FccStrategyType[] $VALUES;
    public static IpChange $ipChange;
    public static final FccStrategyType CACHE;
    public static final FccStrategyType DEFAULT;
    public static final FccStrategyType LEGACY;
    public static final FccStrategyType SNAPSHOT;

    static {
       FccStrategyType uFccStrategy = new FccStrategyType("CACHE", 0);
       FccStrategyType.CACHE = uFccStrategy;
       FccStrategyType uFccStrategy1 = new FccStrategyType("SNAPSHOT", 1);
       FccStrategyType.SNAPSHOT = uFccStrategy1;
       FccStrategyType uFccStrategy2 = new FccStrategyType("DEFAULT", 2);
       FccStrategyType.DEFAULT = uFccStrategy2;
       FccStrategyType uFccStrategy3 = new FccStrategyType("LEGACY", 3);
       FccStrategyType.LEGACY = uFccStrategy3;
       FccStrategyType[] uFccStrategy4 = new FccStrategyType[]{uFccStrategy,uFccStrategy1,uFccStrategy2,uFccStrategy3};
       FccStrategyType.$VALUES = uFccStrategy4;
    }
    private void FccStrategyType(String p0,int p1){
       super(p0, p1);
    }
    public static Object ipc$super(FccStrategyType p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/storage/strategy/FccStrategyType");
    }
    public static FccStrategyType valueOf(String p0){
       IpChange $ipChange = FccStrategyType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(FccStrategyType.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("3a774f7e", objArray);
    }
    public static FccStrategyType[] values(){
       IpChange $ipChange = FccStrategyType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return FccStrategyType.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("4670efef", objArray);
    }
}
