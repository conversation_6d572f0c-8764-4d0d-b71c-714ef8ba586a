package android.taobao.windvane.extra.uc.AliNetworkAdapterNew;
import android.taobao.windvane.extra.uc.interfaces.INetwork;
import tb.urb;
import tb.t2o;
import android.content.Context;
import java.lang.Object;
import android.taobao.windvane.extra.uc.AliNetworkAdapter;
import android.taobao.windvane.extra.uc.MtopSsrNetworkAdapter;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.String;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.extra.performance2.WVPageTracker;
import com.android.alibaba.ip.runtime.IpChange;
import android.taobao.windvane.extra.uc.interfaces.IRequest;
import java.lang.Boolean;
import tb.qqm;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import android.taobao.windvane.extra.uc.AliNetworkAdapterNew$1;
import tb.esd;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.taobao.windvane.export.network.RequestCallback;
import android.taobao.windvane.export.network.b;
import android.taobao.windvane.extra.uc.AliNetworkAdapterNew$2;
import java.util.Map;
import java.lang.Long;
import java.lang.Integer;
import tb.jfq;
import java.lang.Number;
import tb.ace;
import tb.cce;
import android.taobao.windvane.extra.uc.MTopSSRRequest;

public class AliNetworkAdapterNew implements INetwork, urb	// class@0001ff from classes.dex
{
    private final AliNetworkAdapter mAliNetworkAdapter;
    private final MtopSsrNetworkAdapter mMtopSsrNetworkAdapter;
    private WVUCWebView mWebView;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800132);
       t2o.a(0x3d8001a7);
       t2o.a(0x3d8001da);
    }
    public void AliNetworkAdapterNew(Context p0){
       super();
       AliNetworkAdapter uAliNetworkA = new AliNetworkAdapter(p0);
       this.mAliNetworkAdapter = uAliNetworkA;
       this.mMtopSsrNetworkAdapter = new MtopSsrNetworkAdapter(uAliNetworkA, null);
    }
    public void AliNetworkAdapterNew(Context p0,int p1){
       super();
       AliNetworkAdapter uAliNetworkA = new AliNetworkAdapter(p0, p1);
       this.mAliNetworkAdapter = uAliNetworkA;
       this.mMtopSsrNetworkAdapter = new MtopSsrNetworkAdapter(uAliNetworkA, null);
    }
    public void AliNetworkAdapterNew(Context p0,int p1,String p2){
       super();
       AliNetworkAdapter uAliNetworkA = new AliNetworkAdapter(p0, p1, p2);
       this.mAliNetworkAdapter = uAliNetworkA;
       this.mMtopSsrNetworkAdapter = new MtopSsrNetworkAdapter(uAliNetworkA, null);
    }
    public void AliNetworkAdapterNew(Context p0,String p1){
       super();
       AliNetworkAdapter uAliNetworkA = new AliNetworkAdapter(p0, 2, p1);
       this.mAliNetworkAdapter = uAliNetworkA;
       this.mMtopSsrNetworkAdapter = new MtopSsrNetworkAdapter(uAliNetworkA, null);
    }
    public void AliNetworkAdapterNew(Context p0,String p1,WVUCWebView p2){
       super();
       AliNetworkAdapter uAliNetworkA = new AliNetworkAdapter(p0, p1, p2);
       this.mAliNetworkAdapter = uAliNetworkA;
       MtopSsrNetworkAdapter mtopSsrNetwo = new MtopSsrNetworkAdapter(uAliNetworkA, p2);
       this.mMtopSsrNetworkAdapter = mtopSsrNetwo;
       this.mWebView = p2;
       if (vpw.commonConfig.n1 != null && p2 != null) {
          mtopSsrNetwo.setParentId(p2.pageTracker.getPageIdentifier());
       }
       return;
    }
    public static WVUCWebView access$000(AliNetworkAdapterNew p0){
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mWebView;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("5ef11a6c", objArray);
    }
    private boolean consumePrefetchRequest(IRequest p0){
       AliNetworkAdapterNew tmWebView;
       int i = 0;
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("56e5edd9", objArray).booleanValue();
       }else if(p0 != null && (tmWebView = this.mWebView) != null){
          qqm prefetchInfo = tmWebView.getPrefetchInfo();
          String url = p0.getUrl();
          EventHandler eventHandler = p0.getEventHandler();
          AliNetworkAdapterNew$1 u1 = new AliNetworkAdapterNew$1(this, eventHandler);
          esd stageRecorde = this.getStageRecorder();
          if (prefetchInfo != null && (eventHandler != null && (!TextUtils.isEmpty(url) && (TextUtils.equals(url, prefetchInfo.a) && b.b(prefetchInfo.b, u1, stageRecorde))))) {
             return 1;
          }
          return b.c(url, u1, stageRecorde);
       }else {
          return i;
       }
    }
    private esd getStageRecorder(){
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new AliNetworkAdapterNew$2(this, this.mWebView);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6bf33f73", objArray);
    }
    public void destoryWebView(){
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("78625e6c", objArray);
          return;
       }else {
          this.mAliNetworkAdapter.destoryWebView();
          return;
       }
    }
    public IRequest formatRequest(EventHandler p0,String p1,String p2,boolean p3,Map p4,Map p5,Map p6,Map p7,long p8,int p9,int p10){
       object oobject = this;
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[12];
          objArray[0] = oobject;
          objArray[1] = p0;
          objArray[2] = p1;
          objArray[3] = p2;
          objArray[4] = new Boolean(p3);
          objArray[5] = p4;
          objArray[6] = p5;
          objArray[7] = p6;
          objArray[8] = p7;
          objArray[9] = new Long(p8);
          objArray[10] = new Integer(p9);
          objArray[11] = new Integer(p10);
          return $ipChange.ipc$dispatch("5c346fdd", objArray);
       }else if(vpw.commonConfig.n1 != null && jfq.a(p1)){
          return oobject.mMtopSsrNetworkAdapter.formatRequest(p0, p1, p2, p3, p4, p5, p6, p7, p8, p9, p10);
       }else {
          return oobject.mAliNetworkAdapter.formatRequest(p0, p1, p2, p3, p4, p5, p6, p7, p8, p9, p10);
       }
    }
    public String getCurId(){
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mAliNetworkAdapter.getCurId();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("24aaca54", objArray);
    }
    public int getNetworkType(){
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mAliNetworkAdapter.getNetworkType();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("700d68cc", objArray).intValue();
    }
    public String getPId(){
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mAliNetworkAdapter.getPId();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("dc67dba4", objArray);
    }
    public String getVersion(){
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mAliNetworkAdapter.getVersion();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2a8fef97", objArray);
    }
    public boolean requestURL(EventHandler p0,String p1,String p2,boolean p3,Map p4,Map p5,Map p6,Map p7,long p8,int p9,int p10){
       object oobject = this;
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[12];
          objArray[0] = oobject;
          objArray[1] = p0;
          objArray[2] = p1;
          objArray[3] = p2;
          objArray[4] = new Boolean(p3);
          objArray[5] = p4;
          objArray[6] = p5;
          objArray[7] = p6;
          objArray[8] = p7;
          objArray[9] = new Long(p8);
          objArray[10] = new Integer(p9);
          objArray[11] = new Integer(p10);
          return $ipChange.ipc$dispatch("85230ab7", objArray).booleanValue();
       }else if(vpw.commonConfig.n1 != null && jfq.a(p1)){
          return oobject.mMtopSsrNetworkAdapter.requestURL(p0, p1, p2, p3, p4, p5, p6, p7, p8, p9, p10);
       }else {
          return oobject.mAliNetworkAdapter.requestURL(p0, p1, p2, p3, p4, p5, p6, p7, p8, p9, p10);
       }
    }
    public boolean sendRequest(IRequest p0){
       AliNetworkAdapterNew tmWebView;
       int i = 1;
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("f55a9b04", objArray).booleanValue();
       }else if(this.consumePrefetchRequest(p0)){
          try{
             if ((tmWebView = this.mWebView) != null) {
                tmWebView.getWebViewContext().getWebViewPageModel().onProperty("documentPrefetchHit", Boolean.TRUE);
             }
             return e0;
          }catch(java.lang.Exception e0){
          }
       }else if(p0 instanceof MTopSSRRequest){
          return this.mMtopSsrNetworkAdapter.sendRequest(p0);
       }else {
          return this.mAliNetworkAdapter.sendRequest(p0);
       }
    }
    public void setBizCode(String p0){
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d29306ef", objArray);
          return;
       }else {
          this.mAliNetworkAdapter.setBizCode(p0);
          return;
       }
    }
    public void setId(urb p0){
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("799f6d32", objArray);
          return;
       }else {
          this.mAliNetworkAdapter.setId(p0);
          return;
       }
    }
    public void updateCurId(){
       IpChange $ipChange = AliNetworkAdapterNew.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("28ea03b9", objArray);
          return;
       }else {
          this.mAliNetworkAdapter.updateCurId();
          return;
       }
    }
}
