package android.taobao.util.CalledFromWrongThreadException;
import java.lang.RuntimeException;
import tb.t2o;
import java.lang.String;

public class CalledFromWrongThreadException extends RuntimeException	// class@000147 from classes.dex
{

    static {
       t2o.a(0x30c00011);
    }
    public void CalledFromWrongThreadException(){
       super("Only the original thread that created a view hierarchy can touch its views.");
    }
}
