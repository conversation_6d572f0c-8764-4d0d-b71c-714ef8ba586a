package android.taobao.windvane.extra.launch.WVOptimizedStartup$2;
import java.lang.Runnable;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.os.SystemClock;
import tb.r9u;
import android.taobao.windvane.extra.uc.WVUCWebView;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import java.lang.Long;
import java.lang.StringBuilder;
import java.lang.Throwable;

public final class WVOptimizedStartup$2 implements Runnable	// class@0001b6 from classes.dex
{
    public static IpChange $ipChange;

    public void WVOptimizedStartup$2(){
       super();
    }
    public void run(){
       String str = "WindVane/PreStartUp";
       IpChange $ipChange = WVOptimizedStartup$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          try{
             r9u.b("initCore");
             WVUCWebView.initUCCore();
             r9u.d();
             lcn.a(RVLLevel.Info, str).j("startup").a("initUCCoreCost", Long.valueOf((SystemClock.uptimeMillis() - SystemClock.uptimeMillis()))).f();
          }catch(java.lang.Exception e1){
             lcn.f(RVLLevel.Error, str, "startup error"+e1.getMessage());
          }
          return;
       }
    }
}
