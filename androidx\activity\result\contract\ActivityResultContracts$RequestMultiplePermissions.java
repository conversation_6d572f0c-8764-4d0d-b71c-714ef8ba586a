package androidx.activity.result.contract.ActivityResultContracts$RequestMultiplePermissions;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.activity.result.contract.ActivityResultContracts$RequestMultiplePermissions$Companion;
import tb.a07;
import android.content.Context;
import java.lang.Object;
import android.content.Intent;
import java.lang.String;
import tb.ckf;
import androidx.activity.result.contract.ActivityResultContract$SynchronousResult;
import java.util.Map;
import kotlin.collections.a;
import androidx.core.content.ContextCompat;
import tb.v3i;
import tb.hfn;
import java.util.LinkedHashMap;
import java.lang.Boolean;
import kotlin.Pair;
import tb.jpu;
import java.util.ArrayList;
import java.util.List;
import tb.ic1;
import java.lang.Iterable;
import tb.i04;

public final class ActivityResultContracts$RequestMultiplePermissions extends ActivityResultContract	// class@0004d9 from classes.dex
{
    public static final String ACTION_REQUEST_PERMISSIONS = "androidx.activity.result.contract.action.REQUEST_PERMISSIONS";
    public static final ActivityResultContracts$RequestMultiplePermissions$Companion Companion;
    public static final String EXTRA_PERMISSIONS;
    public static final String EXTRA_PERMISSION_GRANT_RESULTS;

    static {
       ActivityResultContracts$RequestMultiplePermissions.Companion = new ActivityResultContracts$RequestMultiplePermissions$Companion(null);
    }
    public void ActivityResultContracts$RequestMultiplePermissions(){
       super();
    }
    public Intent createIntent(Context p0,Object p1){
       return this.createIntent(p0, p1);
    }
    public Intent createIntent(Context p0,String[] p1){
       ckf.g(p0, "context");
       ckf.g(p1, "input");
       return ActivityResultContracts$RequestMultiplePermissions.Companion.createIntent$activity_release(p1);
    }
    public ActivityResultContract$SynchronousResult getSynchronousResult(Context p0,Object p1){
       return this.getSynchronousResult(p0, p1);
    }
    public ActivityResultContract$SynchronousResult getSynchronousResult(Context p0,String[] p1){
       ActivityResultContract$SynchronousResult synchronousR;
       ckf.g(p0, "context");
       ckf.g(p1, "input");
       if (!p1.length) {
          return new ActivityResultContract$SynchronousResult(a.h());
       }
       int len = p1.length;
       int i = 0;
       int i1 = 0;
       while (true) {
          if (i1 < len) {
             if (!ContextCompat.checkSelfPermission(p0, p1[i1])) {
                i1 = i1 + 1;
             }else {
                synchronousR = null;
                break ;
             }
          }else {
             LinkedHashMap linkedHashMa = new LinkedHashMap(hfn.c(v3i.e(p1.length), 16));
             int len1 = p1.length;
             for (; i < len1; i = i + 1) {
                Pair pair = jpu.a(p1[i], Boolean.TRUE);
                Object first = pair.getFirst();
                linkedHashMa.put(first, pair.getSecond());
             }
             synchronousR = new ActivityResultContract$SynchronousResult(linkedHashMa);
          }
       }
       return synchronousR;
    }
    public Object parseResult(int p0,Intent p1){
       return this.parseResult(p0, p1);
    }
    public Map parseResult(int p0,Intent p1){
       int[] intArrayExtr;
       if (p0 != -1) {
          return a.h();
       }
       if (p1 == null) {
          return a.h();
       }
       String[] stringArrayE = p1.getStringArrayExtra("androidx.activity.result.contract.extra.PERMISSIONS");
       if ((intArrayExtr = p1.getIntArrayExtra("androidx.activity.result.contract.extra.PERMISSION_GRANT_RESULTS")) == null || stringArrayE == null) {
          return a.h();
       }
       ArrayList uArrayList = new ArrayList(intArrayExtr.length);
       int len = intArrayExtr.length;
       for (int i = 0; i < len; i = i + 1) {
          boolean b = (!intArrayExtr[i])? true: false;
          uArrayList.add(Boolean.valueOf(b));
       }
       return a.p(i04.J0(ic1.D(stringArrayE), uArrayList));
    }
}
