package androidx.activity.compose.ActivityResultLauncherHolder;
import java.lang.Object;
import androidx.activity.result.ActivityResultLauncher;
import androidx.core.app.ActivityOptionsCompat;
import tb.xhv;
import java.lang.IllegalStateException;
import java.lang.String;

public final class ActivityResultLauncherHolder	// class@000477 from classes.dex
{
    private ActivityResultLauncher launcher;
    public static final int $stable = 8;

    public void ActivityResultLauncherHolder(){
       super();
    }
    public final ActivityResultLauncher getLauncher(){
       return this.launcher;
    }
    public final void launch(Object p0,ActivityOptionsCompat p1){
       ActivityResultLauncherHolder tlauncher;
       if ((tlauncher = this.launcher) != null) {
          tlauncher.launch(p0, p1);
          p0 = xhv.INSTANCE;
       }else {
          p0 = null;
       }
       if (p0 != null) {
          return;
       }else {
          throw new IllegalStateException("Launcher has not been initialized");
       }
    }
    public final void setLauncher(ActivityResultLauncher p0){
       this.launcher = p0;
    }
    public final void unregister(){
       ActivityResultLauncherHolder tlauncher;
       xhv iNSTANCE;
       if ((tlauncher = this.launcher) != null) {
          tlauncher.unregister();
          iNSTANCE = xhv.INSTANCE;
       }else {
          iNSTANCE = null;
       }
       if (iNSTANCE != null) {
          return;
       }else {
          throw new IllegalStateException("Launcher has not been initialized");
       }
    }
}
