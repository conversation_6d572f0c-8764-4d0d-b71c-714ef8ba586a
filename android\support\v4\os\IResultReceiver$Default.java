package android.support.v4.os.IResultReceiver$Default;
import android.support.v4.os.IResultReceiver;
import java.lang.Object;
import android.os.IBinder;
import android.os.Bundle;

public class IResultReceiver$Default implements IResultReceiver	// class@00012e from classes.dex
{

    public void IResultReceiver$Default(){
       super();
    }
    public IBinder asBinder(){
       return null;
    }
    public void send(int p0,Bundle p1){
    }
}
