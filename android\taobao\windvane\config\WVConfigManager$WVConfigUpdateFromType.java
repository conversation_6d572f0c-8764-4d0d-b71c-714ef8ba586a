package android.taobao.windvane.config.WVConfigManager$WVConfigUpdateFromType;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class WVConfigManager$WVConfigUpdateFromType extends Enum	// class@00014c from classes.dex
{
    private static final WVConfigManager$WVConfigUpdateFromType[] $VALUES;
    public static IpChange $ipChange;
    public static final WVConfigManager$WVConfigUpdateFromType WVConfigUpdateFromTypeActive;
    public static final WVConfigManager$WVConfigUpdateFromType WVConfigUpdateFromTypeAppActive;
    public static final WVConfigManager$WVConfigUpdateFromType WVConfigUpdateFromTypeCustom;
    public static final WVConfigManager$WVConfigUpdateFromType WVConfigUpdateFromTypeFinish;
    public static final WVConfigManager$WVConfigUpdateFromType WVConfigUpdateFromTypeLaunch;
    public static final WVConfigManager$WVConfigUpdateFromType WVConfigUpdateFromTypeLocaleChange;
    public static final WVConfigManager$WVConfigUpdateFromType WVConfigUpdateFromTypePush;
    public static final WVConfigManager$WVConfigUpdateFromType WVConfigUpdateFromZCache3_0;

    static {
       WVConfigManager$WVConfigUpdateFromType wVConfigUpda = new WVConfigManager$WVConfigUpdateFromType("WVConfigUpdateFromTypeCustom", 0);
       WVConfigManager$WVConfigUpdateFromType.WVConfigUpdateFromTypeCustom = wVConfigUpda;
       WVConfigManager$WVConfigUpdateFromType wVConfigUpda1 = new WVConfigManager$WVConfigUpdateFromType("WVConfigUpdateFromTypeActive", 1);
       WVConfigManager$WVConfigUpdateFromType.WVConfigUpdateFromTypeActive = wVConfigUpda1;
       WVConfigManager$WVConfigUpdateFromType wVConfigUpda2 = new WVConfigManager$WVConfigUpdateFromType("WVConfigUpdateFromTypeFinish", 2);
       WVConfigManager$WVConfigUpdateFromType.WVConfigUpdateFromTypeFinish = wVConfigUpda2;
       WVConfigManager$WVConfigUpdateFromType wVConfigUpda3 = new WVConfigManager$WVConfigUpdateFromType("WVConfigUpdateFromTypePush", 3);
       WVConfigManager$WVConfigUpdateFromType.WVConfigUpdateFromTypePush = wVConfigUpda3;
       WVConfigManager$WVConfigUpdateFromType wVConfigUpda4 = new WVConfigManager$WVConfigUpdateFromType("WVConfigUpdateFromTypeLaunch", 4);
       WVConfigManager$WVConfigUpdateFromType.WVConfigUpdateFromTypeLaunch = wVConfigUpda4;
       WVConfigManager$WVConfigUpdateFromType wVConfigUpda5 = new WVConfigManager$WVConfigUpdateFromType("WVConfigUpdateFromTypeAppActive", 5);
       WVConfigManager$WVConfigUpdateFromType.WVConfigUpdateFromTypeAppActive = wVConfigUpda5;
       WVConfigManager$WVConfigUpdateFromType wVConfigUpda6 = new WVConfigManager$WVConfigUpdateFromType("WVConfigUpdateFromTypeLocaleChange", 6);
       WVConfigManager$WVConfigUpdateFromType.WVConfigUpdateFromTypeLocaleChange = wVConfigUpda6;
       WVConfigManager$WVConfigUpdateFromType wVConfigUpda7 = new WVConfigManager$WVConfigUpdateFromType("WVConfigUpdateFromZCache3_0", 7);
       WVConfigManager$WVConfigUpdateFromType.WVConfigUpdateFromZCache3_0 = wVConfigUpda7;
       WVConfigManager$WVConfigUpdateFromType[] wVConfigUpda8 = new WVConfigManager$WVConfigUpdateFromType[]{wVConfigUpda,wVConfigUpda1,wVConfigUpda2,wVConfigUpda3,wVConfigUpda4,wVConfigUpda5,wVConfigUpda6,wVConfigUpda7};
       WVConfigManager$WVConfigUpdateFromType.$VALUES = wVConfigUpda8;
    }
    private void WVConfigManager$WVConfigUpdateFromType(String p0,int p1){
       super(p0, p1);
    }
    public static Object ipc$super(WVConfigManager$WVConfigUpdateFromType p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/config/WVConfigManager$WVConfigUpdateFromType");
    }
    public static WVConfigManager$WVConfigUpdateFromType valueOf(String p0){
       IpChange $ipChange = WVConfigManager$WVConfigUpdateFromType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(WVConfigManager$WVConfigUpdateFromType.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("f7560edb", objArray);
    }
    public static WVConfigManager$WVConfigUpdateFromType[] values(){
       IpChange $ipChange = WVConfigManager$WVConfigUpdateFromType.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVConfigManager$WVConfigUpdateFromType.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("c7ad3d4a", objArray);
    }
}
