package android.taobao.windvane.extra.jsi.WVJsi;
import tb.t2o;
import java.lang.Object;
import android.taobao.windvane.extra.uc.UCLog;
import java.lang.String;
import java.lang.Throwable;
import tb.v7t;
import android.content.Context;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.StringBuilder;
import android.content.pm.ApplicationInfo;
import android.taobao.windvane.extra.jsi.WVJsi$InstanceBuilder;
import tb.vpw;
import tb.wpw;
import tb.jpw;
import tb.bhc;
import java.lang.Class;
import android.os.Bundle;
import android.os.BaseBundle;
import com.alibaba.jsi.standard.JSEngine;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import android.util.Log;
import android.taobao.windvane.extra.uc.remotefetch.WVUCRemoteFetcher;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.io.File;
import java.util.HashMap;
import com.uc.webview.export.extension.U4Engine;
import com.uc.webview.base.io.PathUtils;
import android.os.SystemClock;
import java.lang.ClassLoader;
import java.util.Map;
import java.lang.Long;
import com.uc.webview.base.UCKnownException;
import java.lang.Integer;
import android.taobao.windvane.extra.uc.remotefetch.WVUCRemoteFetcher$WVUCFetcherCallback;

public class WVJsi	// class@0001b1 from classes.dex
{
    public static IpChange $ipChange;
    private static String ENGINE_TYPE_QUICK_JS;
    private static String ENGINE_TYPE_V8;
    private static String JSI_FILE_NAME;
    private static String KEY_JSI_ENGINE_PATH;
    private static String KEY_JSI_ENGINE_TYPE;
    private static String KEY_JSI_SO_PATH;
    private static final Object LOAD_LOCK;
    private static final String MONITOR_POINT;
    private static final String TAG;
    private static boolean hasLoadQjsJsi;
    private static boolean hasLoadUCJsi;

    static {
       t2o.a(0x3d8000e2);
       WVJsi.hasLoadUCJsi = false;
       WVJsi.hasLoadQjsJsi = false;
       WVJsi.KEY_JSI_SO_PATH = "jsiSoPath";
       WVJsi.KEY_JSI_ENGINE_PATH = "jsEngineSoPath";
       WVJsi.KEY_JSI_ENGINE_TYPE = "engineType";
       WVJsi.ENGINE_TYPE_V8 = "v8";
       WVJsi.ENGINE_TYPE_QUICK_JS = "qjs";
       WVJsi.JSI_FILE_NAME = "libjsi.so";
       WVJsi.LOAD_LOCK = new Object();
       if (!UCLog.getInstance().isInited()) {
          UCLog.getInstance().init(true);
       }
    }
    public void WVJsi(){
       super();
    }
    public static boolean access$200(Context p0){
       IpChange $ipChange = WVJsi.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVJsi.loadV8JsiIfNecessary(p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("a04487f3", objArray).booleanValue();
    }
    public static boolean access$400(Context p0){
       IpChange $ipChange = WVJsi.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVJsi.loadQjsJsiIfNecessary(p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("b0a507b1", objArray).booleanValue();
    }
    private static String getJsiSoPath(Context p0){
       IpChange $ipChange = WVJsi.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.getApplicationInfo().nativeLibraryDir+"/"+WVJsi.JSI_FILE_NAME;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("4dd834aa", objArray);
    }
    public static WVJsi$InstanceBuilder instanceBuilder(Context p0){
       IpChange $ipChange = WVJsi.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return new WVJsi$InstanceBuilder(p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("35b4d79b", objArray);
    }
    public static boolean loadJsi(Context p0){
       boolean b;
       IpChange $ipChange = WVJsi.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("59fcd9eb", objArray).booleanValue();
       }else if(!(b = WVJsi.loadV8JsiIfNecessary(p0))){
          b = WVJsi.loadQjsJsiIfNecessary(p0);
       }
       return b;
    }
    private static boolean loadQjsJsi(Context p0){
       bhc uobhc;
       int i = 1;
       String str = "QJS_LOAD_FAIL";
       String str1 = "WVJsi/LOAD";
       IpChange $ipChange = WVJsi.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("a22fe3b9", objArray).booleanValue();
       }else if(vpw.commonConfig.n2 != null && (uobhc = jpw.c().a(bhc.class)) != null){
          uobhc.a(p0);
       }
       Bundle uBundle = new Bundle();
       uBundle.putString(WVJsi.KEY_JSI_SO_PATH, WVJsi.getJsiSoPath(p0));
       uBundle.putString(WVJsi.KEY_JSI_ENGINE_TYPE, WVJsi.ENGINE_TYPE_QUICK_JS);
       if (JSEngine.loadSo(p0, uBundle)) {
          WVJsi.hasLoadQjsJsi = i;
          lcn.a(RVLLevel.Info, str1).j("QJS_LOAD_SUCCESS").f();
       }else {
          lcn.a(RVLLevel.Error, str1).j(str).f();
       }
       return WVJsi.hasLoadQjsJsi;
    }
    private static boolean loadQjsJsiIfNecessary(Context p0){
       int i = 1;
       IpChange $ipChange = WVJsi.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("881690bf", objArray).booleanValue();
       }else if(WVJsi.hasLoadQjsJsi){
          return i;
       }else {
          Object lOAD_LOCK = WVJsi.LOAD_LOCK;
          _monitor_enter(lOAD_LOCK);
          if (WVJsi.hasLoadQjsJsi) {
             _monitor_exit(lOAD_LOCK);
             return i;
          }else {
             _monitor_exit(lOAD_LOCK);
             return WVJsi.loadQjsJsi(p0);
          }
       }
    }
    private static boolean loadV8Jsi(Context p0){
       bhc uobhc;
       boolean b1;
       File uFile1;
       wpw owpw;
       HashMap hashMap1;
       String str9;
       String str10;
       File fileJsiLib;
       object oobject = p0;
       int i = 0;
       IpChange $ipChange = WVJsi.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject};
          return $ipChange.ipc$dispatch("43223b0d", objArray).booleanValue();
       }else {
          wpw commonConfig = vpw.commonConfig;
          if (commonConfig.n2 != null && (uobhc = jpw.c().a(bhc.class)) != null) {
             uobhc.a(oobject);
          }
          String str = WVUCRemoteFetcher.fetchUCRemoteLocal();
          if (TextUtils.isEmpty(str)) {
             lcn.a(RVLLevel.Error, "WVJsi/LOAD").j("V8_LOAD_FAIL").a("msg", "kernel file, kernelPath is empty").f();
             WVJsi.tryFetchRemoteUC();
             return i;
          }else {
             File uFile = new File(str);
             boolean b = uFile.isDirectory();
             HashMap hashMap = new HashMap();
             if (b) {
                hashMap.put("specifiedDir", uFile.getAbsolutePath());
             }
             wpw h3 = commonConfig.h3;
             uobhc = 0;
             if (h3 != null) {
                b1 = WVJsi.hasLoadQjsJsi;
                uFile1 = uobhc;
             }else if(commonConfig.Y2 != null){
                File uFile2 = (b)? uFile: U4Engine.getExtractDir(oobject, uFile);
                if (uFile2 != null && (uFile2.exists() && (uFile2 = PathUtils.getDirCoreLib(uFile2)) != null)) {
                   fileJsiLib = PathUtils.getFileJsiLib(uFile2);
                }
             }else {
                fileJsiLib = new File(WVJsi.getJsiSoPath(p0));
             }
             uFile1 = fileJsiLib;
             b1 = true;
             long l = SystemClock.uptimeMillis();
             String str1 = "null";
             String str2 = "jsiSoFile";
             String str3 = "kernelPath";
             String str4 = "V8_LOAD_SUCCESS";
             if (uFile.isFile()) {
                String str5 = str4;
                String str6 = str3;
                String str7 = str2;
                owpw = h3;
                hashMap1 = hashMap;
                if (U4Engine.loadJsiSo(p0, JSEngine.class.getClassLoader(), uFile1, uFile, b1, 0)) {
                   WVJsi.hasLoadUCJsi = true;
                   icn oicn = lcn.a(RVLLevel.Info, "WVJsi/LOAD").j(str5).a("isDir", Boolean.valueOf(b)).a("ignoreOldCore", Boolean.valueOf(b1)).a(str6, str);
                   if (uFile1 != null) {
                      str1 = uFile1.getAbsolutePath();
                   }
                   String str8 = str1;
                   str2 = str7;
                   oicn.a(str2, str8).f();
                }else {
                   str9 = str5;
                   str10 = str6;
                   str2 = str7;
                label_0139 :
                   if (uFile.isDirectory()) {
                      String str11 = str2;
                      str2 = str10;
                      String str12 = str9;
                      if (U4Engine.loadJsiSo(p0, JSEngine.class.getClassLoader(), uFile1, 0, 0, hashMap1)) {
                         WVJsi.hasLoadUCJsi = true;
                         icn oicn1 = lcn.a(RVLLevel.Info, "WVJsi/LOAD").j(str12).a("isDir", Boolean.valueOf(b)).a(str2, str);
                         if (uFile1 != null) {
                            str1 = uFile1.getAbsolutePath();
                         }
                         str4 = str1;
                         oicn1.a(str11, str4).f();
                      }
                   }
                   lcn.a(RVLLevel.Error, "WVJsi/LOAD").j("V8_LOAD_FAIL").a("isDir", Boolean.valueOf(b)).a("msg", "failed or use illegal kernelPath ="+str).f();
                   WVJsi.tryFetchRemoteUC();
                }
             }else {
                owpw = h3;
                hashMap1 = hashMap;
                str9 = str4;
                str10 = str3;
                goto label_0139 ;
             }
             lcn.a(RVLLevel.Info, "WVJsi/LOAD").j("V8_LOAD_COST").a("enableJSIReuseCore", Boolean.valueOf(owpw)).a("cost", Long.valueOf((SystemClock.uptimeMillis() - l))).f();
             return WVJsi.hasLoadUCJsi;
          }
       }
    }
    private static boolean loadV8JsiIfNecessary(Context p0){
       int i = 1;
       IpChange $ipChange = WVJsi.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return $ipChange.ipc$dispatch("21621ceb", objArray).booleanValue();
       }else if(WVJsi.hasLoadUCJsi){
          return i;
       }else {
          Object lOAD_LOCK = WVJsi.LOAD_LOCK;
          _monitor_enter(lOAD_LOCK);
          if (WVJsi.hasLoadUCJsi) {
             _monitor_exit(lOAD_LOCK);
             return i;
          }else {
             _monitor_exit(lOAD_LOCK);
             return WVJsi.loadV8Jsi(p0);
          }
       }
    }
    private static void tryFetchRemoteUC(){
       IpChange $ipChange = WVJsi.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("7e55a33a", objArray);
          return;
       }else if(vpw.commonConfig.Q2 != null){
          WVUCRemoteFetcher.fetchUCRemote(null);
       }else if(TextUtils.isEmpty(WVUCRemoteFetcher.fetchUCRemoteLocal())){
          WVUCRemoteFetcher.fetchUCRemote(null);
       }
       return;
    }
}
