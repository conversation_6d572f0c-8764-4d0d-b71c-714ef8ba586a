package android.taobao.windvane.extra.storage.FirstChunkStorage$1;
import java.util.Comparator;
import android.taobao.windvane.extra.storage.FirstChunkStorage;
import java.lang.Object;
import android.taobao.windvane.extra.storage.FirstChunkStorage$MetaItemResult;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Number;
import com.alibaba.fastjson.JSONObject;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.viy;

public class FirstChunkStorage$1 implements Comparator	// class@0001e5 from classes.dex
{
    public final FirstChunkStorage this$0;
    public static IpChange $ipChange;

    public void FirstChunkStorage$1(FirstChunkStorage p0){
       this.this$0 = p0;
       super();
    }
    public int compare(FirstChunkStorage$MetaItemResult p0,FirstChunkStorage$MetaItemResult p1){
       int i = 0;
       IpChange $ipChange = FirstChunkStorage$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("c43ec3d9", objArray).intValue();
       }else {
          String str = p0.meatItem.getString("fcc-url-query-rule");
          String str1 = p1.meatItem.getString("fcc-url-query-rule");
          String[] stringArray = new String[i];
          String[] stringArray1 = new String[i];
          if (!TextUtils.isEmpty(str)) {
             stringArray = str.split(",");
          }
          if (!TextUtils.isEmpty(str1)) {
             stringArray1 = str1.split(",");
          }
          if (stringArray.length == stringArray1.length) {
             FirstChunkStorage.access$000(this.this$0, stringArray);
             FirstChunkStorage.access$000(this.this$0, stringArray1);
             return viy.a("fcc-url-query-rule", stringArray).compareTo(viy.a("fcc-url-query-rule", stringArray1));
          }else {
             return (- (stringArray.length - stringArray1.length));
          }
       }
    }
    public int compare(Object p0,Object p1){
       return this.compare(p0, p1);
    }
}
