package androidx.activity.result.contract.ActivityResultContracts$TakePicturePreview;
import androidx.activity.result.contract.ActivityResultContract;
import android.content.Context;
import java.lang.Object;
import android.content.Intent;
import java.lang.Void;
import java.lang.String;
import tb.ckf;
import androidx.activity.result.contract.ActivityResultContract$SynchronousResult;
import android.graphics.Bitmap;
import android.os.Parcelable;

public class ActivityResultContracts$TakePicturePreview extends ActivityResultContract	// class@0004e0 from classes.dex
{

    public void ActivityResultContracts$TakePicturePreview(){
       super();
    }
    public Intent createIntent(Context p0,Object p1){
       return this.createIntent(p0, p1);
    }
    public Intent createIntent(Context p0,Void p1){
       ckf.g(p0, "context");
       return new Intent("android.media.action.IMAGE_CAPTURE");
    }
    public ActivityResultContract$SynchronousResult getSynchronousResult(Context p0,Object p1){
       return this.getSynchronousResult(p0, p1);
    }
    public final ActivityResultContract$SynchronousResult getSynchronousResult(Context p0,Void p1){
       ckf.g(p0, "context");
       return null;
    }
    public final Bitmap parseResult(int p0,Intent p1){
       Bitmap uBitmap = null;
       if (p0 != -1) {
          Bitmap uBitmap1 = uBitmap;
       }
       if (p1 != null) {
          uBitmap = p1.getParcelableExtra("data");
       }
       return uBitmap;
    }
    public Object parseResult(int p0,Intent p1){
       return this.parseResult(p0, p1);
    }
}
