package androidx.activity.contextaware.ContextAwareKt$withContextAvailable$2$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.contextaware.ContextAware;
import androidx.activity.contextaware.ContextAwareKt$withContextAvailable$2$listener$1;
import java.lang.Object;
import java.lang.Throwable;
import tb.xhv;
import androidx.activity.contextaware.OnContextAvailableListener;

public final class ContextAwareKt$withContextAvailable$2$1 extends Lambda implements g1a	// class@0004a4 from classes.dex
{
    public final ContextAwareKt$withContextAvailable$2$listener$1 $listener;
    public final ContextAware $this_withContextAvailable;

    public void ContextAwareKt$withContextAvailable$2$1(ContextAware p0,ContextAwareKt$withContextAvailable$2$listener$1 p1){
       this.$this_withContextAvailable = p0;
       this.$listener = p1;
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(Throwable p0){
       this.$this_withContextAvailable.removeOnContextAvailableListener(this.$listener);
    }
}
