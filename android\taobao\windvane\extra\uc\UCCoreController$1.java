package android.taobao.windvane.extra.uc.UCCoreController$1;
import android.webkit.ValueCallback;
import tb.abq;
import java.lang.Object;
import java.util.Map;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.StringBuilder;
import java.lang.Integer;
import tb.gtw;
import tb.y71;
import java.lang.Throwable;
import tb.x74;

public final class UCCoreController$1 implements ValueCallback	// class@00021d from classes.dex
{
    public final abq val$spanWrapper;
    public static IpChange $ipChange;

    public void UCCoreController$1(abq p0){
       this.val$spanWrapper = p0;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(Map p0){
       object oobject = this;
       object oobject1 = p0;
       String str = "url";
       String str1 = ", stack: ";
       String str2 = "ThreadWatchdog get callback. pid: ";
       IpChange $ipChange = UCCoreController$1.$ipChange;
       String str3 = "ThreadWatchdog";
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,oobject1};
          $ipChange.ipc$dispatch("cc3b1331", objArray);
          return;
       }else {
          String str4 = str2+oobject1.get("pid").intValue()+", tid: "+oobject1.get("tid").intValue()+", threadName: "+oobject1.get("threadName")+", state: "+oobject1.get("state")+", type: "+oobject1.get("type")+", url: "+oobject1.get(str)+", webviewCount: "+oobject1.get("webviewCount").intValue()+", taskInfo: "+oobject1.get("taskInfo")+", elapseInMs: "+oobject1.get("elapseInMs").intValue()+", alarmInMs: "+oobject1.get("alarmInMs").intValue()+str1+oobject1.get("stack");
          oobject.val$spanWrapper.e(str4);
          y71.commitFail(str3, 0, gtw.f(oobject1.get(str)), str4);
          return;
       }
    }
}
