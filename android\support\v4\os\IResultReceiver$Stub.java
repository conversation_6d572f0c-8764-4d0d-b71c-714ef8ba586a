package android.support.v4.os.IResultReceiver$Stub;
import android.support.v4.os.IResultReceiver;
import android.os.Binder;
import android.os.IInterface;
import java.lang.String;
import android.os.IBinder;
import android.support.v4.os.IResultReceiver$Stub$Proxy;
import android.os.Parcel;
import android.os.Bundle;
import android.os.Parcelable$Creator;
import java.lang.Object;
import android.support.v4.os.IResultReceiver$_Parcel;

public abstract class IResultReceiver$Stub extends Binder implements IResultReceiver	// class@000130 from classes.dex
{
    static final int TRANSACTION_send = 1;

    public void IResultReceiver$Stub(){
       super();
       this.attachInterface(this, IResultReceiver.DESCRIPTOR);
    }
    public static IResultReceiver asInterface(IBinder p0){
       IInterface iInterface;
       if (p0 == null) {
          return null;
       }
       if ((iInterface = p0.queryLocalInterface(IResultReceiver.DESCRIPTOR)) != null && iInterface instanceof IResultReceiver) {
          return iInterface;
       }
       return new IResultReceiver$Stub$Proxy(p0);
    }
    public IBinder asBinder(){
       return this;
    }
    public boolean onTransact(int p0,Parcel p1,Parcel p2,int p3){
       String dESCRIPTOR = IResultReceiver.DESCRIPTOR;
       if (p0 >= 1 && p0 <= 0xffffff) {
          p1.enforceInterface(dESCRIPTOR);
       }
       if (p0 == 0x5f4e5446) {
          p2.writeString(dESCRIPTOR);
          return 1;
       }else if(p0 != 1){
          return super.onTransact(p0, p1, p2, p3);
       }else {
          this.send(p1.readInt(), IResultReceiver$_Parcel.access$000(p1, Bundle.CREATOR));
          return 1;
       }
    }
}
