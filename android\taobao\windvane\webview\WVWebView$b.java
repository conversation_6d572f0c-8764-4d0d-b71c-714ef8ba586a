package android.taobao.windvane.webview.WVWebView$b;
import android.view.View$OnClickListener;
import android.taobao.windvane.webview.WVWebView;
import java.lang.Object;
import android.view.View;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.os.Build$VERSION;
import android.content.Context;
import tb.hzl$a;
import tb.hzl;
import android.taobao.windvane.webview.WVWebView$b$b;
import java.lang.Runnable;
import android.taobao.windvane.webview.WVWebView$b$a;
import android.os.Handler;
import tb.voe;
import android.taobao.windvane.view.PopupWindowController;

public class WVWebView$b implements View$OnClickListener	// class@000313 from classes.dex
{
    public final WVWebView a;
    public static IpChange $ipChange;

    public void WVWebView$b(WVWebView p0){
       super();
       this.a = p0;
    }
    public void onClick(View p0){
       int i = 0;
       IpChange $ipChange = WVWebView$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8dfcefe2", objArray);
          return;
       }else if(WVWebView.access$300(this.a) != null && (WVWebView.access$300(this.a).length > 0 && WVWebView.access$300(this.a)[i].equals(p0.getTag()))){
          if (Build$VERSION.SDK_INT < 30) {
             String[] stringArray = new String[]{"android.permission.WRITE_EXTERNAL_STORAGE"};
             hzl.b(this.a.context, stringArray).i(new WVWebView$b$b(this)).h(new WVWebView$b$a(this)).d();
          }else {
             voe.g(this.a.context.getApplicationContext(), WVWebView.access$100(this.a), this.a.mHandler);
          }
       }
    }
}
