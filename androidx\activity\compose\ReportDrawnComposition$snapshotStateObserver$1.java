package androidx.activity.compose.ReportDrawnComposition$snapshotStateObserver$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import tb.d1a;
import tb.xhv;

public final class ReportDrawnComposition$snapshotStateObserver$1 extends Lambda implements g1a	// class@000495 from classes.dex
{
    public static final ReportDrawnComposition$snapshotStateObserver$1 INSTANCE;

    static {
       ReportDrawnComposition$snapshotStateObserver$1.INSTANCE = new ReportDrawnComposition$snapshotStateObserver$1();
    }
    public void ReportDrawnComposition$snapshotStateObserver$1(){
       super(1);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(d1a p0){
       p0.invoke();
    }
}
