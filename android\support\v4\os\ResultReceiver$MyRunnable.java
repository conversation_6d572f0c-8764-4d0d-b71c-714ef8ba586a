package android.support.v4.os.ResultReceiver$MyRunnable;
import java.lang.Runnable;
import android.support.v4.os.ResultReceiver;
import android.os.Bundle;
import java.lang.Object;

class ResultReceiver$MyRunnable implements Runnable	// class@00013a from classes.dex
{
    final int mResultCode;
    final Bundle mResultData;
    final ResultReceiver this$0;

    public void ResultReceiver$MyRunnable(ResultReceiver p0,int p1,Bundle p2){
       this.this$0 = p0;
       super();
       this.mResultCode = p1;
       this.mResultData = p2;
    }
    public void run(){
       this.this$0.onReceiveResult(this.mResultCode, this.mResultData);
    }
}
