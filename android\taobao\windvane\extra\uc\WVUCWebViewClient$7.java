package android.taobao.windvane.extra.uc.WVUCWebViewClient$7;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.WVUCWebViewClient;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.StringBuilder;
import tb.v7t;

public class WVUCWebViewClient$7 implements Runnable	// class@000269 from classes.dex
{
    public final WVUCWebViewClient this$0;
    public static IpChange $ipChange;

    public void WVUCWebViewClient$7(WVUCWebViewClient p0){
       this.this$0 = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = WVUCWebViewClient$7.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          v7t.d("WVUCWebViewClient.sandbox", "crash count reset - "+this.this$0.crashCount);
          this.this$0.crashCount = 0;
          return;
       }
    }
}
