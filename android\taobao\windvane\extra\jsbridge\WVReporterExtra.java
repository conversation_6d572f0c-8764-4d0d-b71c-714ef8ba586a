package android.taobao.windvane.extra.jsbridge.WVReporterExtra;
import android.taobao.windvane.jsbridge.api.WVReporter;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import java.lang.Boolean;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import org.json.JSONObject;
import tb.kpw;
import android.taobao.windvane.webview.IWVWebView;
import android.taobao.windvane.webview.WVWebView;
import android.taobao.windvane.extra.uc.WVUCWebView;
import android.net.Uri;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.csw;
import tb.trw;
import tb.v7t;
import java.lang.Long;
import android.util.Log;
import tb.nsw;
import java.lang.Throwable;

public class WVReporterExtra extends WVReporter	// class@0001ab from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000e0);
    }
    public void WVReporterExtra(){
       super();
    }
    public static Object ipc$super(WVReporterExtra p0,String p1,Object[] p2){
       if (p1.hashCode() == -1126948911) {
          return new Boolean(super.execute(p2[0], p2[1], p2[2]));
       }
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/jsbridge/WVReporterExtra");
    }
    public boolean execute(String p0,String p1,WVCallBackContext p2){
       int i = 0;
       IpChange $ipChange = WVReporterExtra.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("bcd41fd1", objArray).booleanValue();
       }else if("reportPerformanceCheckResult".equals(p0)){
          this.reportPerformanceCheckResult(p2, p1);
       }else if("reportPrerenderStatus".equals(p0)){
          this.reportPrerenderStatus(p2, p1);
       }else {
          return i;
       }
       return super.execute(p0, p1, p2);
    }
    public void reportPerformanceCheckResult(WVCallBackContext p0,String p1){
       Object[] objArray;
       WVWebView bizCode;
       Uri uri;
       String str4;
       object oobject = this;
       object oobject1 = p0;
       object oobject2 = p1;
       int i = 3;
       String str = "";
       IpChange $ipChange = WVReporterExtra.$ipChange;
       if ($ipChange instanceof IpChange) {
          objArray = new Object[i];
          objArray[0] = oobject;
          objArray[1] = oobject1;
          objArray[2] = oobject2;
          $ipChange.ipc$dispatch("4e524717", objArray);
          return;
       }else {
          try{
             JSONObject $ipChange1 = new JSONObject(oobject2);
             long l = $ipChange1.optLong("score", 0);
             String str1 = $ipChange1.optString("version", str);
             String str2 = $ipChange1.optString("result", str);
             String str3 = $ipChange1.optString("detail", str);
             str = oobject.mWebView.getUrl();
             $ipChange1 = 0;
             kpw mWebView = oobject.mWebView;
             if (mWebView instanceof WVWebView) {
                bizCode = mWebView.bizCode;
             }else if(mWebView instanceof WVUCWebView){
                bizCode = mWebView.bizCode;
             }
             if ((uri = Uri.parse(str)) != null && uri.isHierarchical()) {
                String queryParamet = uri.getQueryParameter("wvBizCode");
                if (!TextUtils.isEmpty(queryParamet)) {
                   str4 = queryParamet;
                label_007c :
                   if (trw.getPerformanceMonitor() != null) {
                      trw.getPerformanceMonitor().didPerformanceCheckResult(str, l, str1, str4, str2);
                   }
                   if (v7t.h()) {
                      objArray = new Object[i];
                      objArray[0] = str;
                      objArray[1] = Long.valueOf(l);
                      objArray[2] = str3;
                      Log.e("WindVaneWebPerfCheck", String.format("WindVaneWebPerfCheck: %s|%d|%s", objArray));
                   }
                   p0.success();
                }
             }
             str4 = bizCode;
             goto label_007c ;
          }catch(java.lang.Exception e0){
             nsw onsw = new nsw();
             onsw.b("msg", e0.getMessage());
             oobject1.error(onsw);
          }
          return;
       }
    }
    public void reportPrerenderStatus(WVCallBackContext p0,String p1){
       IpChange $ipChange = WVReporterExtra.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("7969e5c5", objArray);
          return;
       }else {
          p0.error();
          return;
       }
    }
}
