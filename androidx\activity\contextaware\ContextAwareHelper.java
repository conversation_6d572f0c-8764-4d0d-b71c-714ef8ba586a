package androidx.activity.contextaware.ContextAwareHelper;
import java.lang.Object;
import java.util.concurrent.CopyOnWriteArraySet;
import androidx.activity.contextaware.OnContextAvailableListener;
import java.lang.String;
import tb.ckf;
import android.content.Context;
import java.util.Set;
import java.util.Iterator;

public final class ContextAwareHelper	// class@0004a3 from classes.dex
{
    private Context context;
    private final Set listeners;

    public void ContextAwareHelper(){
       super();
       this.listeners = new CopyOnWriteArraySet();
    }
    public final void addOnContextAvailableListener(OnContextAvailableListener p0){
       ContextAwareHelper tcontext;
       ckf.g(p0, "listener");
       if ((tcontext = this.context) != null) {
          p0.onContextAvailable(tcontext);
       }
       this.listeners.add(p0);
       return;
    }
    public final void clearAvailableContext(){
       this.context = null;
    }
    public final void dispatchOnContextAvailable(Context p0){
       ckf.g(p0, "context");
       this.context = p0;
       Iterator iterator = this.listeners.iterator();
       while (iterator.hasNext()) {
          iterator.next().onContextAvailable(p0);
       }
       return;
    }
    public final Context peekAvailableContext(){
       return this.context;
    }
    public final void removeOnContextAvailableListener(OnContextAvailableListener p0){
       ckf.g(p0, "listener");
       this.listeners.remove(p0);
    }
}
