package android.taobao.windvane.extra.uc.WebViewContext$1;
import android.view.View$OnAttachStateChangeListener;
import android.taobao.windvane.extra.uc.WebViewContext;
import java.util.concurrent.atomic.AtomicBoolean;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import android.view.View;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.v7t;
import android.taobao.windvane.extra.performance.BuiltinWebViewPageModel;
import tb.cce;
import com.uc.webview.export.extension.UCExtension;
import com.uc.webview.export.WebView;
import java.lang.Boolean;
import java.util.List;
import java.util.Iterator;
import tb.ycd;
import tb.ace;

public class WebViewContext$1 implements View$OnAttachStateChangeListener	// class@00026d from classes.dex
{
    public final WebViewContext this$0;
    public final AtomicBoolean val$hasAddBuiltinPageModel;
    public final WVUCWebView val$webView;
    public static IpChange $ipChange;

    public void WebViewContext$1(WebViewContext p0,AtomicBoolean p1,WVUCWebView p2){
       this.this$0 = p0;
       this.val$hasAddBuiltinPageModel = p1;
       this.val$webView = p2;
       super();
    }
    public void onViewAttachedToWindow(View p0){
       UCExtension uCExtension;
       ycd oycd;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = WebViewContext$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3d337638", objArray);
          return;
       }else if(this.val$hasAddBuiltinPageModel.compareAndSet(i1, i)){
          v7t.i("WebViewContext", "addBuiltinPageModel");
          this.this$0.addWebViewPageModel(new BuiltinWebViewPageModel(this.val$webView));
          if (WebViewContext.access$000(this.this$0)) {
             if ((uCExtension = this.val$webView.getUCExtension()) != null) {
                uCExtension.setIsPreRender(i1);
                this.this$0.getWebViewPageModel().onProperty("H5_PreRender", Boolean.TRUE);
                this.this$0.getWebViewPageModel().onProperty("H5_URL", this.val$webView.getUrl());
             }
             Iterator iterator = WebViewContext.access$100(this.this$0).iterator();
             while (iterator.hasNext()) {
                if ((oycd = iterator.next()) != null) {
                   oycd.a(this.val$webView.getWebViewContext().getRealUrl());
                }
             }
          }
          this.val$webView.removeOnAttachStateChangeListener(this);
       }
       return;
    }
    public void onViewDetachedFromWindow(View p0){
       IpChange $ipChange = WebViewContext$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("7f64d55b", objArray);
       }
       return;
    }
}
