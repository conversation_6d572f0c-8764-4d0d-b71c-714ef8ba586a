package androidx.activity.compose.BackHandlerKt$BackHandler$2$1$invoke$$inlined$onDispose$1;
import tb.gi20;
import androidx.activity.compose.BackHandlerKt$BackHandler$backCallback$1$1;
import java.lang.Object;
import androidx.activity.OnBackPressedCallback;

public final class BackHandlerKt$BackHandler$2$1$invoke$$inlined$onDispose$1 implements gi20	// class@00047d from classes.dex
{
    public final BackHandlerKt$BackHandler$backCallback$1$1 $backCallback$inlined;

    public void BackHandlerKt$BackHandler$2$1$invoke$$inlined$onDispose$1(BackHandlerKt$BackHandler$backCallback$1$1 p0){
       this.$backCallback$inlined = p0;
       super();
    }
    public void dispose(){
       this.$backCallback$inlined.remove();
    }
}
