package a.a.a.a.a.a.a.c.a$a;
import java.lang.Runnable;
import a.a.a.a.a.a.a.c.a;
import java.lang.String;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import java.lang.Object;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;

public class a$a implements Runnable	// class@000008 from classes.dex
{
    public final String a;
    public final WVCallBackContext b;
    public final a c;

    public void a$a(a p0,String p1,WVCallBackContext p2){
       this.c = p0;
       this.a = p1;
       this.b = p2;
       super();
    }
    public void run(){
       a.a(this.c, JSON.parseObject(this.a), this.b);
    }
}
