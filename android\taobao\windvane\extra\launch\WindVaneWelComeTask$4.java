package android.taobao.windvane.extra.launch.WindVaneWelComeTask$4;
import java.lang.Runnable;
import android.taobao.windvane.extra.launch.WindVaneWelComeTask;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.vxm;
import tb.mdd;
import java.util.Map;
import tb.oba;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;

public class WindVaneWelComeTask$4 implements Runnable	// class@0001c6 from classes.dex
{
    public final WindVaneWelComeTask this$0;
    public static IpChange $ipChange;

    public void WindVaneWelComeTask$4(WindVaneWelComeTask p0){
       this.this$0 = p0;
       super();
    }
    public void run(){
       mdd omdd;
       Map$Entry uEntry;
       IpChange $ipChange = WindVaneWelComeTask$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if((omdd = vxm.b.e()) != null){
          Iterator iterator = oba.a().entrySet().iterator();
          while (iterator.hasNext()) {
             if ((uEntry = iterator.next()) == null) {
                continue ;
             }
             omdd.a(uEntry.getKey(), uEntry.getValue());
          }
       }
       return;
    }
}
