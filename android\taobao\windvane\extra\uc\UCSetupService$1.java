package android.taobao.windvane.extra.uc.UCSetupService$1;
import com.uc.webview.base.task.ITaskExecutor;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.lang.Runnable;
import com.android.alibaba.ip.runtime.IpChange;
import tb.wae;
import tb.mrt;
import tb.xae;
import java.lang.Long;
import java.util.concurrent.ScheduledFuture;

public final class UCSetupService$1 extends ITaskExecutor	// class@000223 from classes.dex
{
    public static IpChange $ipChange;

    public void UCSetupService$1(){
       super();
    }
    public static Object ipc$super(UCSetupService$1 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/UCSetupService$1");
    }
    public void execute(Runnable p0){
       IpChange $ipChange = UCSetupService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("1afb0dfa", objArray);
          return;
       }else {
          mrt.b().execute(p0);
          return;
       }
    }
    public void schedule(Runnable p0,long p1){
       IpChange $ipChange = UCSetupService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("cc2af728", objArray);
          return;
       }else {
          mrt.b().a(p0, p1);
          return;
       }
    }
}
