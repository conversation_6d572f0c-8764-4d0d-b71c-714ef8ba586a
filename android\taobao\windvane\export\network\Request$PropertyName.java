package android.taobao.windvane.export.network.Request$PropertyName;
import java.lang.annotation.Annotation;

public interface abstract Request$PropertyName implements Annotation	// class@000164 from classes.dex
{
    public static final String MAIN_REQUEST_CONNECT_TYPE = "BottomNavigationView_menu";
    public static final String MAIN_REQUEST_FALCO_ID = "BottomSheetActivityTheme";
    public static final String MAIN_REQUEST_RX_SIZE = "BottomSheetBehavior_Layout_android_elevation";
    public static final String NETWORK_CONNECT_TYPE = "connectionType";
    public static final String NETWORK_EYE_TRACE_ID = "eagleEyeTraceID";
    public static final String NETWORK_FALCO_ID = " logInfo";
    public static final String NETWORK_HIT_CDN_CACHE = "3a6a1fd5";
    public static final String NETWORK_IP_REFER = "57d0b68c";
    public static final String NETWORK_LOW_CONNECT_IN_FLIGHT = "connInFlight";
    public static final String NETWORK_LOW_LATANCY_PING = "9362bed9";
    public static final String NETWORK_NET_LOAD = "Fast";
    public static final String NETWORK_RETRY_TIMES = "FeatureNameSpace_uik_clickViewMaskFeature";
    public static final String NETWORK_SEND_SIZE = "Lcom/alibaba/mtl/appmonitor/AppMonitor$Alarm$3;";
    public static final String NETWORK_SEVER_RT = "Lcom/alibaba/poplayer/factory/view/base/ClickableAreaParam;";
    public static final String NETWORK_TOTAL_SIZE = "NETWORK_CLASS_UNKNOWN";

}
