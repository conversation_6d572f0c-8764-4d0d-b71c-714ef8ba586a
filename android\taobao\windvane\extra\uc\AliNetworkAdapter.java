package android.taobao.windvane.extra.uc.AliNetworkAdapter;
import android.taobao.windvane.extra.uc.interfaces.INetwork;
import tb.urb;
import tb.t2o;
import android.content.Context;
import java.lang.String;
import java.lang.Object;
import java.util.HashSet;
import tb.vpw;
import tb.wpw;
import java.lang.Math;
import anetwork.channel.degrade.DegradableNetwork;
import anetwork.channel.http.HttpNetwork;
import android.taobao.windvane.extra.uc.WVUCWebView;
import com.android.alibaba.ip.runtime.IpChange;
import anetwork.channel.Request;
import android.taobao.windvane.extra.uc.AliRequestAdapter;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import java.lang.Number;
import android.taobao.windvane.extra.uc.IOnSgHttpRequestCallback;
import tb.v7t;
import java.util.List;
import org.json.JSONObject;
import java.util.Iterator;
import anetwork.channel.Header;
import java.lang.Throwable;
import android.taobao.windvane.extra.uc.AliNetworkAdapter$AliNetCallback;
import android.taobao.windvane.extra.uc.interfaces.IRequest;
import android.os.Handler;
import anetwork.channel.NetworkListener;
import java.util.concurrent.Future;
import anetwork.channel.Network;
import tb.abq;
import tb.tz8;
import com.taobao.analysis.v3.FalcoGlobalTracer;
import tb.raq;
import java.util.Map;
import tb.x74;
import tb.gtw;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.taobao.windvane.extra.performance2.WVWPData;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import android.taobao.windvane.extra.performance2.WVPageTracker;
import java.lang.Integer;
import java.lang.Boolean;
import java.lang.StringBuilder;
import android.taobao.windvane.extra.uc.interfaces.IRequestTiming;
import android.taobao.windvane.extra.uc.AliNetworkAdapter$1;
import anetwork.channel.IBodyHandler;
import android.os.SystemClock;
import java.util.Set;
import anetwork.channel.Response;
import anetwork.channel.statist.StatisticData;
import java.lang.Long;
import android.taobao.windvane.extra.uc.Escape;
import tb.og8;
import tb.wqw;
import tb.trw;
import android.taobao.windvane.extra.uc.WVHeaderManager;
import org.apache.http.ParseException;
import java.net.SocketTimeoutException;
import java.net.SocketException;
import java.io.IOException;
import java.lang.IllegalStateException;
import java.net.UnknownHostException;
import java.util.concurrent.atomic.AtomicInteger;

public class AliNetworkAdapter implements INetwork, urb	// class@0001fc from classes.dex
{
    private int BUFFER_SIZE;
    private String bizCode;
    public boolean isReload;
    public boolean isStop;
    private boolean isUseWebpImg;
    private Network mAliNetwork;
    private Context mContext;
    private int mNetworkType;
    private WVUCWebView mWebView;
    private int mWorkingMode;
    public HashSet mainRequest;
    private String pid;
    private String uid;
    public static IpChange $ipChange;
    private static final String LOGTAG;
    public static final int NETWORK_WORKING_MODE_ASYNC;
    public static final int NETWORK_WORKING_MODE_SYNC;
    private static String RVLOG_NETWORK_MODEL;
    private static boolean enableAir;
    private static IOnSgHttpRequestCallback iOnSgHttpRequestCallback;

    static {
       t2o.a(0x3d80012f);
       t2o.a(0x3d8001a7);
       t2o.a(0x3d8001da);
       AliNetworkAdapter.RVLOG_NETWORK_MODEL = "WindVane/Network";
       AliNetworkAdapter.enableAir = false;
    }
    public void AliNetworkAdapter(Context p0){
       super(p0, "windvane");
    }
    public void AliNetworkAdapter(Context p0,int p1){
       super(p0, p1, "windvane");
    }
    public void AliNetworkAdapter(Context p0,int p1,String p2){
       AliNetworkAdapter tmNetworkTyp;
       super();
       this.mNetworkType = -1;
       this.mWorkingMode = 1;
       this.BUFFER_SIZE = 1024;
       this.isUseWebpImg = true;
       this.bizCode = "";
       this.isReload = false;
       this.isStop = false;
       this.pid = "";
       this.uid = "";
       this.mainRequest = new HashSet();
       this.mContext = p0;
       this.mNetworkType = p1;
       this.bizCode = p2;
       this.isUseWebpImg = ((Math.random() - vpw.commonConfig.d) > 0)? true: false;
       if ((tmNetworkTyp = this.mNetworkType) != null) {
          if (tmNetworkTyp == 1 || tmNetworkTyp == 2) {
             this.mAliNetwork = new DegradableNetwork(p0);
          }
       }else {
          this.mAliNetwork = new HttpNetwork(p0);
       }
       return;
    }
    public void AliNetworkAdapter(Context p0,String p1){
       super(p0, 2, p1);
    }
    public void AliNetworkAdapter(Context p0,String p1,WVUCWebView p2){
       super(p0, 2, p1);
       this.mWebView = p2;
    }
    public static WVUCWebView access$000(AliNetworkAdapter p0){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mWebView;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("c1792d92", objArray);
    }
    public static void access$100(AliNetworkAdapter p0,Request p1){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("45e8d294", objArray);
          return;
       }else {
          p0.injectTraceContext(p1);
          return;
       }
    }
    public static void access$200(AliNetworkAdapter p0,AliRequestAdapter p1){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("b5448b36", objArray);
          return;
       }else {
          p0.monitorRequest(p1);
          return;
       }
    }
    public static void access$300(AliNetworkAdapter p0,AliRequestAdapter p1,Request p2,EventHandler p3){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2,p3};
          $ipChange.ipc$dispatch("dac5b9ad", objArray);
          return;
       }else {
          p0.syncSendRequest(p1, p2, p3);
          return;
       }
    }
    public static String access$400(){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return AliNetworkAdapter.RVLOG_NETWORK_MODEL;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("60292585", objArray);
    }
    public static int access$500(AliNetworkAdapter p0){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.BUFFER_SIZE;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("25a886bd", objArray).intValue();
    }
    public static Context access$600(AliNetworkAdapter p0){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mContext;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("cb9b8183", objArray);
    }
    public static void addHttpRequestCallback(IOnSgHttpRequestCallback p0){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("c00f21c1", objArray);
          return;
       }else {
          AliNetworkAdapter.iOnSgHttpRequestCallback = p0;
          v7t.d("AliNetwork", "addHttpRequestCallback");
          return;
       }
    }
    private void asyncSendRequest(AliRequestAdapter p0,Request p1,EventHandler p2){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("a49edb37", objArray);
          return;
       }else if(p1.getHeaders() != null && p1.getHeaders().size()){
          JSONObject jSONObject = new JSONObject();
          Iterator iterator = p1.getHeaders().iterator();
          while (iterator.hasNext()) {
             Header header = iterator.next();
             try{
                jSONObject.put(header.getName(), header.getValue());
             }catch(org.json.JSONException e2){
                e2.printStackTrace();
             }
          }
       }
       AliNetworkAdapter$AliNetCallback uAliNetCallb = new AliNetworkAdapter$AliNetCallback(this);
       uAliNetCallb.setId(this);
       uAliNetCallb.setEventHandler(p2);
       uAliNetCallb.setURL(p0.getUrl());
       uAliNetCallb.setRequest(p0);
       p0.setFutureResponse(this.mAliNetwork.asyncSend(p1, p0.originalUrl, null, uAliNetCallb));
       return;
    }
    public static void clearHttpRequestCallback(){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("de1fdfc6", objArray);
          return;
       }else {
          AliNetworkAdapter.iOnSgHttpRequestCallback = null;
          v7t.d("AliNetwork", "clearHttpRequestCallback");
          return;
       }
    }
    private void injectTraceContext(Request p0){
       AliNetworkAdapter tmWebView;
       tz8 otz8;
       String str = "AliNetwork";
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d3f2f3b0", objArray);
          return;
       }else if((tmWebView = this.mWebView) != null && tmWebView.getSpanWrapper() != null){
          if ((otz8 = FalcoGlobalTracer.get()) != null) {
             p0.setTraceContext(otz8.c(this.mWebView.getSpanWrapper().a()));
          }
       }else if(this.mWebView == null){
          v7t.i(str, "webView == null");
       }else {
          v7t.i(str, "webView.getFalcoSpan\(\) == null");
       }
       return;
    }
    private void monitorRequest(AliRequestAdapter p0){
       String url;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("ee01600b", objArray);
          return;
       }else if((url = p0.getUrl()) != null && TextUtils.equals(gtw.i(url), gtw.i(this.mWebView.getCachedUrl()))){
          i1 = 1;
       }
       if (i1) {
          this.mWebView.wpData.setPageCurrentStatus("requestHtml");
       }else if(url != null && url.endsWith(".js")){
          this.mWebView.wpData.setPageCurrentStatus("requestJs");
       }
       if (vpw.commonConfig.c1 != null) {
          icn oicn = lcn.a(RVLLevel.Info, AliNetworkAdapter.RVLOG_NETWORK_MODEL).k("request", this.getCurId()).m(this.mWebView.pageTracker.getPageIdentifier());
          if (i1) {
             oicn.a("isPage", Integer.valueOf(i));
          }
          oicn.a("url", url).a("method", p0.getMethod()).a("header", p0.getHeaders());
          oicn.f();
       }
       return;
    }
    private boolean sendRequestInternal(AliRequestAdapter p0){
       IRequestTiming requestTimin;
       AliNetworkAdapter tmWorkingMod;
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("99a0d8fe", objArray).booleanValue();
       }else {
          Request aliRequest = p0.getAliRequest();
          this.injectTraceContext(aliRequest);
          this.monitorRequest(p0);
          EventHandler eventHandler = p0.getEventHandler();
          v7t.i("AliNetwork", "requestURL eventId="+p0.getEventHandler().hashCode()+", url="+p0.getUrl()+",isSync="+eventHandler.isSynchronous());
          if ((requestTimin = p0.getRequestTiming()) != null) {
             requestTimin.markNativeRequestSendTime();
          }
          if ((tmWorkingMod = this.mWorkingMode) == null) {
             this.syncSendRequest(p0, aliRequest, eventHandler);
          }else if(tmWorkingMod == 1){
             this.asyncSendRequest(p0, aliRequest, eventHandler);
          }
          return 1;
       }
    }
    public static void setEnableAir(boolean p0){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Boolean(p0)};
          $ipChange.ipc$dispatch("9ef20fc8", objArray);
          return;
       }else {
          AliNetworkAdapter.enableAir = p0;
          return;
       }
    }
    private void setRequestBodyHandler(Request p0,AliRequestAdapter p1){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("313a83f", objArray);
          return;
       }else if(p1.getUploadFileTotalLen()){
          Map uploadFileMa = p1.getUploadFileMap();
          Map uploadDataMa = p1.getUploadDataMap();
          p0.setBodyHandler(new AliNetworkAdapter$1(this, (uploadFileMa.size() + uploadDataMa.size()), uploadFileMa, uploadDataMa));
       }
       return;
    }
    private void sgRequestCheck(String p0,String p1,boolean p2,Map p3,Map p4,Map p5,Map p6,int p7){
       Map map;
       object oobject = p0;
       object oobject1 = p1;
       boolean b = p2;
       object oobject2 = p3;
       object oobject3 = p4;
       object oobject4 = p5;
       object oobject5 = p6;
       int i = p7;
       int i1 = 1;
       int i2 = 0;
       String str = "sgRequestCheck\(\) called with: url = [";
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[9];
          objArray[i2] = this;
          objArray[i1] = oobject;
          objArray[2] = oobject1;
          objArray[3] = new Boolean(b);
          objArray[4] = oobject2;
          objArray[5] = oobject3;
          objArray[6] = oobject4;
          objArray[7] = oobject5;
          objArray[8] = new Integer(i);
          $ipChange.ipc$dispatch("9decb137", objArray);
          return;
       }else if(AliNetworkAdapter.iOnSgHttpRequestCallback != null){
          wpw commonConfig = vpw.commonConfig;
          if (commonConfig.K != null) {
             long l = SystemClock.uptimeMillis();
             if (commonConfig.L != null) {
                if (i && i != i1) {
                   i1 = 0;
                }else {
                   v7t.l("AliNetwork", str+oobject+"], method = ["+oobject1+"], isUCProxyReq = ["+b+"], headers = ["+oobject2+"], ucHeaders = ["+oobject3+"], uploadFileMap = ["+oobject4+"], uploadDataMap = ["+oobject5+"], requestType = ["+i+"]");
                   map = AliNetworkAdapter.iOnSgHttpRequestCallback.onSgHttpRequest(p0, p1, p2, p3, p4, p5, p6);
                }
             }else {
                map = AliNetworkAdapter.iOnSgHttpRequestCallback.onSgHttpRequest(p0, p1, p2, p3, p4, p5, p6);
             }
             if (i1) {
                v7t.d("AliNetwork", "onSgHttpRequest use time:"+(SystemClock.uptimeMillis() - l));
             }
             if (map != null) {
                Iterator iterator = map.keySet().iterator();
                while (iterator.hasNext()) {
                   String str1 = iterator.next();
                   if (!oobject2.containsKey(str1)) {
                      String str2 = map.get(str1);
                      oobject2.put(str1, str2);
                      v7t.l("AliNetwork", "".append("add header key:").append(str1).append(" value:").append(str2).toString());
                   }else {
                      v7t.d("AliNetwork", "".append("add header conflict key:").append(str1).toString());
                   }
                }
             }
          }
       }
       return;
    }
    private void syncSendRequest(AliRequestAdapter p0,Request p1,EventHandler p2){
       Throwable error;
       byte[] bytedata;
       int i = 2;
       int i1 = 0;
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("10da0038", objArray);
          return;
       }else {
          Response response = this.mAliNetwork.syncSend(p1, null);
          if ((error = response.getError()) != null) {
             p2.error(this.getErrorFromException(error), error.toString());
          }else {
             int statusCode = response.getStatusCode();
             StatisticData connectionTy = response.getStatisticData().connectionType;
             if (!TextUtils.isEmpty(connectionTy) && connectionTy.startsWith("http2")) {
                p2.status(i, i1, statusCode, "");
             }else {
                p2.status(i1, i1, statusCode, "");
             }
             if (v7t.h()) {
                v7t.a("AliNetwork", "status code="+statusCode);
             }
             p2.headers(response.getConnHeadFields());
             if ((bytedata = response.getBytedata()) != null) {
                p2.data(bytedata, bytedata.length);
             }
             p0.cancelPhase = "enddata";
             p2.endData();
          }
          return;
       }
    }
    public static boolean willLog(EventHandler p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("a80d945", objArray).booleanValue();
       }else {
          int resourceType = p0.getResourceType();
          boolean b = p0.isSynchronous();
          if (!resourceType || (resourceType == 14 || b)) {
             i = true;
          }
          return i;
       }
    }
    public void destoryWebView(){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("78625e6c", objArray);
          return;
       }else {
          this.mWebView = null;
          return;
       }
    }
    public IRequest formatRequest(EventHandler p0,String p1,String p2,boolean p3,Map p4,Map p5,Map p6,Map p7,long p8,int p9,int p10){
       object oobject = this;
       object oobject1 = p2;
       object oobject2 = p4;
       int i = p9;
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[12];
          objArray[0] = oobject;
          objArray[1] = p0;
          objArray[2] = p1;
          objArray[3] = oobject1;
          objArray[4] = new Boolean(p3);
          objArray[5] = oobject2;
          objArray[6] = p5;
          objArray[7] = p6;
          objArray[8] = p7;
          objArray[9] = new Long(p8);
          objArray[10] = new Integer(i);
          objArray[11] = new Integer(p10);
          return $ipChange.ipc$dispatch("5c346fdd", objArray);
       }else {
          this.updateCurId();
          String str = Escape.tryDecodeUrl(p1);
          if (og8.b() && AliNetworkAdapter.enableAir) {
             oobject2.put("x-air-grey", "true");
          }
          if (trw.getWVNetWorkMonitorInterface() != null) {
             String str1 = oobject2.get("Referer");
             wqw wVNetWorkMon = trw.getWVNetWorkMonitorInterface();
             if (str1 == null) {
                str1 = "unknown";
             }
             wVNetWorkMon.onFormatRequest(str, str1, oobject1);
          }
          WVHeaderManager.getInstance().addCustomRequestHeaderCheck(str, oobject2);
          this.sgRequestCheck(str, p2, p3, p4, p5, p6, p7, p9);
          int i1 = i;
          EventHandler uEventHandle = p0;
          super(p0, str, p2, p3, p4, p5, p6, p7, p8, p9, p10, oobject.isUseWebpImg, oobject.bizCode, oobject.mContext);
          if (vpw.commonConfig.c1 != null) {
             this.setCurId(this.getCurId());
          }
          this.setId(oobject.pid);
          if (oobject.isReload != null) {
             this.cancelPhase = "reload";
          }
          oobject.setRequestBodyHandler(this.getAliRequest(), this);
          uEventHandle.setRequest(this);
          uEventHandle.setResourceType(i1);
          return this;
       }
    }
    public String getCurId(){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "WVNet_"+this.uid;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("24aaca54", objArray);
    }
    public int getErrorFromException(Throwable p0){
       int i;
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("9892708e", objArray).intValue();
       }else if(p0 instanceof ParseException){
          i = -43;
       }else if(p0 instanceof SocketTimeoutException){
          i = -46;
       }else if(p0 instanceof SocketException){
          i = -47;
       }else if(p0 instanceof IOException){
          i = -44;
       }else if(p0 instanceof IllegalStateException){
          i = -45;
       }else if(p0 instanceof UnknownHostException){
          i = -2;
       }else {
          i = -99;
       }
       return i;
    }
    public int getNetworkType(){
       int i = 1;
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          i = $ipChange.ipc$dispatch("700d68cc", objArray).intValue();
       }
       return i;
    }
    public String getPId(){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.pid;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("dc67dba4", objArray);
    }
    public String getVersion(){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "1.0";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2a8fef97", objArray);
    }
    public boolean requestURL(EventHandler p0,String p1,String p2,boolean p3,Map p4,Map p5,Map p6,Map p7,long p8,int p9,int p10){
       object oobject = this;
       object oobject1 = p1;
       boolean b = p3;
       object oobject2 = p4;
       int i = p9;
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[12];
          objArray[0] = oobject;
          objArray[1] = p0;
          objArray[2] = oobject1;
          objArray[3] = p2;
          objArray[4] = new Boolean(b);
          objArray[5] = oobject2;
          objArray[6] = p5;
          objArray[7] = p6;
          objArray[8] = p7;
          objArray[9] = new Long(p8);
          objArray[10] = new Integer(i);
          objArray[11] = new Integer(p10);
          return $ipChange.ipc$dispatch("85230ab7", objArray).booleanValue();
       }else {
          this.updateCurId();
          v7t.i("AliNetwork", "requestURL:"+oobject1+" isUCProxyReq:"+b+" requestType:"+i);
          String str = Escape.tryDecodeUrl(p1);
          if (og8.b() && AliNetworkAdapter.enableAir) {
             oobject2.put("x-air-grey", "true");
          }
          AliRequestAdapter uAliRequestA = v15;
          AliRequestAdapter uAliRequestA1 = v15;
          if ((uAliRequestA = new AliRequestAdapter(p0, str, p2, p3, p4, p5, p6, p7, p8, p9, p10, oobject.isUseWebpImg, oobject.bizCode, oobject.mContext)) != null) {
             uAliRequestA1.setCurId(this.getCurId());
          }
          uAliRequestA = uAliRequestA1;
          AliNetworkAdapter uAliNetworkA = this;
          uAliRequestA.setId(uAliNetworkA.pid);
          if (uAliNetworkA.isReload != null) {
             uAliRequestA.cancelPhase = "reload";
          }
          uAliNetworkA.setRequestBodyHandler(uAliRequestA.getAliRequest(), uAliRequestA);
          p0.setRequest(uAliRequestA);
          return uAliNetworkA.sendRequestInternal(uAliRequestA);
       }
    }
    public boolean sendRequest(IRequest p0){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.sendRequestInternal(p0);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("f55a9b04", objArray).booleanValue();
    }
    public void setBizCode(String p0){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d29306ef", objArray);
          return;
       }else {
          this.bizCode = p0;
          return;
       }
    }
    public void setId(urb p0){
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("799f6d32", objArray);
          return;
       }else if(p0 == null){
          return;
       }else {
          this.pid = p0.getCurId();
          return;
       }
    }
    public void updateCurId(){
       int i = 1;
       IpChange $ipChange = AliNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("28ea03b9", objArray);
          return;
       }else {
          this.uid = urb.id.addAndGet(i);
          return;
       }
    }
}
