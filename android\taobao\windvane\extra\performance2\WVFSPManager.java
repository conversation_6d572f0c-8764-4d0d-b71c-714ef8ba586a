package android.taobao.windvane.extra.performance2.WVFSPManager;
import java.io.Serializable;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.bsw;
import tb.asw;
import tb.csw;
import tb.trw;
import java.lang.StringBuilder;
import tb.v7t;
import java.lang.System;
import java.lang.Long;
import java.util.Iterator;
import java.util.List;
import android.taobao.windvane.extra.performance2.WVFSPManager$FSPCallback;
import java.util.ArrayList;
import android.taobao.windvane.extra.performance2.WVFSPManager$CompletionHandler;

public class WVFSPManager implements Serializable	// class@0001d9 from classes.dex
{
    private List fspCallbacks;
    private long startTime;
    private int state;
    private long time;
    public long time_H5Pages;
    private String url;
    public static IpChange $ipChange;
    private static final String TAG;
    public static final int WV_FSP_STATE_Initialize;
    public static final int WV_FSP_STATE_NavigationDidEnd;
    public static final int WV_FSP_STATE_NavigationDidStart;
    public static final int WV_FSP_STATE_UnitFinished;

    static {
       t2o.a(0x3d80010c);
    }
    public void WVFSPManager(){
       super();
    }
    private void commitStat(){
       IpChange $ipChange = WVFSPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("230ca732", objArray);
          return;
       }else if(!bsw.b().a().e()){
          return;
       }else if(trw.getPerformanceMonitor() != null){
          trw.getPerformanceMonitor().didPageStartInFSP(this.url, (this.time - this.startTime));
       }
       v7t.i("FSP", "FSP_URL: "+this.url+"\nFSP_Time: "+(this.time - this.startTime));
       return;
    }
    public String eventForFSPStop(){
       IpChange $ipChange = WVFSPManager.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "Event_FSP_Stop";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5f110fa3", objArray);
    }
    public String jsCodeForFSP(){
       IpChange $ipChange = WVFSPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("40f7b2c9", objArray);
       }else if(!bsw.b().a().e()){
          return "";
       }else {
          return "javascript:\(function\(\){var badCaseMetaName=undefined;var classNames={};var ids={};var plainTextLabels={\'SPAN\':1,\'I\':1};var badCaseDOMName=\"data-observe-windvane-FSP-badcase-domname\";var previousTime=0;var callback=function\(records\){if\(badCaseMetaName==undefined\){if\(document.querySelector\(\'meta[name=\"windvane-AIT-badcase-metaname\"]\'\)\){badCaseMetaName=document.querySelector\(\'meta[name=\"windvane-AIT-badcase-metaname\"]\'\).getAttribute\(\'content\'\);if\(badCaseMetaName\){var metaNames=[];metaNames=badCaseMetaName.split\(\'|\'\);for\(var i=0;i<metaNames.length;i++\){var metaName=metaNames[i].substring\(1,metaNames[i].length\);if\(metaNames[i].substring\(0,1\)==\'.\'\){classNames[metaName]=true}else if\(metaNames[i].substring\(0,1\)==\'#\'\){ids[metaName]=true}}}}}var visibleTop=0;var visibleBottom=document.documentElement.clientHeight;var visibleLeft=0;var visibleRight=document.documentElement.clientWidth;var isAlreadySendMessage=false;var show=undefined;var hasBadCase=undefined;for\(var record of records\){var domNode=record.target;if\(domNode==undefined\){continue}if\(record.addedNodes.length==0\){continue}if\(domNode.nodeType==1\){}else if\(domNode.nodeType==3\){var parentNode=domNode.parentElement;if\(parentNode.nodeType==1\){domNode=parentNode}else{continue}}else{continue}var isPlainText=true;for\(var i=0;i<record.addedNodes.length;i++\){var addedDom=record.addedNodes[i];if\(addedDom.nodeType==1&&plainTextLabels[addedDom.tagName]!=1\){isPlainText=false;break}}if\(isPlainText\){continue}if\(show==undefined\){var domNodeTop=domNode.getBoundingClientRect\(\).top;var domNodeBottom=domNode.getBoundingClientRect\(\).bottom;var domNodeLeft=domNode.getBoundingClientRect\(\).left;var domNodeRight=domNode.getBoundingClientRect\(\).right;show=domNodeTop<visibleBottom&&domNodeBottom>visibleTop&&domNodeLeft<visibleRight&&domNodeRight>visibleLeft;if\(record.addedNodes.length>0\){var firstAddedDom=record.addedNodes[0];if\(firstAddedDom.nodeType==1\){var addedDomNodeTop=firstAddedDom.getBoundingClientRect\(\).top;var addedDomNodeBottom=firstAddedDom.getBoundingClientRect\(\).bottom;var addedDomNodeLeft=firstAddedDom.getBoundingClientRect\(\).left;var addedDomNodeRight=firstAddedDom.getBoundingClientRect\(\).right;show=addedDomNodeTop<visibleBottom&&addedDomNodeBottom>visibleTop&&addedDomNodeLeft<visibleRight&&addedDomNodeRight>visibleLeft}}}var filter=domNode.tagName!=\'HTML\'&&domNode.tagName!=\'BODY\'&&domNode.tagName!=\'HEAD\'&&domNode.tagName!=\'SCRIPT\'&&domNode.tagName!=\'STYLE\';if\(!show&&filter\){break}if\(show&&filter\){if\(window.getComputedStyle&&"+bsw.b().a().c()+"\){var domNodeCssStyle=window.getComputedStyle\(domNode
    ,null\);if\(\(domNodeCssStyle.getPropertyValue\(\"transform\"\)!=\"\"&&domNodeCssStyle.getPropertyValue\(\"transform\"\)!=\"none\"\)||\(domNodeCssStyle.getPropertyValue\(\"animation-name\"\)!=\"\"&&domNodeCssStyle.getPropertyValue\(\"animation-name\"\)!=\"none\"\)\){continue}var allHasAnimation=true;for\(var i=0;i<record.addedNodes.length;i++\){var addedDom=record.addedNodes[i];var addedDomCssStyle=window.getComputedStyle\(addedDom,null\);if\(addedDom.nodeType==1\){if\(\(addedDomCssStyle.getPropertyValue\(\"transform\"\)!=\"\"&&addedDomCssStyle.getPropertyValue\(\"transform\"\)!=\"none\"\)||\(addedDomCssStyle.getPropertyValue\(\"animation-name\"\)!=\"\"&&addedDomCssStyle.getPropertyValue\(\"animation-name\"\)!=\"none\"\)\){}else{allHasAnimation=false;break}}}if\(allHasAnimation==true\){continue}}if\(badCaseMetaName==undefined\){var currentTime=\(new Date\(\)\).getTime\(\);if\(currentTime-previousTime>10\){console.log\(\'hybrid://WVPerformance:FSP/receiveFSPTime?{\"time\":\'+currentTime+\'}\'\);previousTime=currentTime}break}var isInIds=ids[domNode.id]==true;var isInClassNames=false;for\(var i=0;i<domNode.classList.length;i++\){if\(classNames[domNode.classList[i]]==true\){isInClassNames=true;break}}if\(isInClassNames||isInIds\){domNode.setAttribute\(badCaseDOMName,\'true\'\);hasBadCase=true;continue}if\(domNode.parentNode!=document\){if\(domNode.parentNode.getAttribute\(badCaseDOMName\)==\'true\'\){domNode.setAttribute\(badCaseDOMName,\'true\'\);hasBadCase=true;continue}}if\(hasBadCase==undefined&&isAlreadySendMessage==false\){var currentTime=\(new Date\(\)\).getTime\(\);if\(currentTime-previousTime>10\){console.log\(\'hybrid://WVPerformance:FSP/receiveFSPTime?{\"time\":\'+currentTime+\'}\'\);previousTime=currentTime;isAlreadySendMessage=true}}}}};var mo=new MutationObserver\(callback\);var options={\'childList\':true,\'subtree\':true};mo.observe\(document.body,options\);document.addEventListener\(\'"+this.eventForFSPStop()+"\',function\(\){mo.disconnect\(\);mo.takeR
    ecords\(\)}\)}\)\(\)";
       }
    }
    public void navigationDidFinishWithURL(String p0){
       int i = 1;
       int i1 = 2;
       IpChange $ipChange = WVFSPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[i] = p0;
          $ipChange.ipc$dispatch("d841dafc", objArray);
          return;
       }else if(!bsw.b().a().e()){
          return;
       }else if(this.state == i){
          long l = System.currentTimeMillis();
          this.time = l;
          this.time_H5Pages = l;
          this.url = p0;
          this.state = i1;
          v7t.i("FSP", "navigationDidFinishWithURL: "+p0);
       }
       return;
    }
    public void navigationDidStart(){
       WVFSPManager tstate;
       int i = 1;
       IpChange $ipChange = WVFSPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("58a6625e", objArray);
          return;
       }else if(!bsw.b().a().e()){
          return;
       }else if((tstate = this.state) != null && (tstate != 2 && tstate != 3)){
          this.state = i;
          this.startTime = System.currentTimeMillis();
          v7t.i("FSP", "navigationDidStart");
       }
       return;
    }
    public void receiveJSMessage(long p0){
       WVFSPManager tfspCallback;
       IpChange $ipChange = WVFSPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("f0c75102", objArray);
          return;
       }else if(!bsw.b().a().e()){
          return;
       }else {
          this.time = p0;
          if ((tfspCallback = this.fspCallbacks) != null) {
             Iterator iterator = tfspCallback.iterator();
             while (iterator.hasNext()) {
                iterator.next().onFSPBack(p0);
             }
          }
          v7t.i("FSP", "sendTime: "+p0);
          return;
       }
    }
    public void setFspCallback(WVFSPManager$FSPCallback p0){
       IpChange $ipChange = WVFSPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("42a179d", objArray);
          return;
       }else if(this.fspCallbacks == null){
          this.fspCallbacks = new ArrayList();
       }
       if (!this.fspCallbacks.contains(p0)) {
          this.fspCallbacks.add(p0);
       }
       return;
    }
    public void unitDidFinish(WVFSPManager$CompletionHandler p0){
       int i = 2;
       IpChange $ipChange = WVFSPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          objArray[1] = p0;
          $ipChange.ipc$dispatch("91c33a0f", objArray);
          return;
       }else if(bsw.b().a().e() && (p0 != null && this.state == i)){
          this.state = 3;
          this.commitStat();
          v7t.i("FSP", "unitDidFinish");
          p0.stopObserving();
          p0.uploadFSPInfo(this.url, this.time);
          this.time_H5Pages = this.time;
       }
       return;
    }
}
