package android.support.v4.os.IResultReceiver$_Parcel;
import java.lang.Object;
import android.os.Parcel;
import android.os.Parcelable$Creator;
import android.os.Parcelable;

public class IResultReceiver$_Parcel	// class@000131 from classes.dex
{

    public void IResultReceiver$_Parcel(){
       super();
    }
    public static Object access$000(Parcel p0,Parcelable$Creator p1){
       return IResultReceiver$_Parcel.readTypedObject(p0, p1);
    }
    public static void access$100(Parcel p0,Parcelable p1,int p2){
       IResultReceiver$_Parcel.writeTypedObject(p0, p1, p2);
    }
    private static Object readTypedObject(Parcel p0,Parcelable$Creator p1){
       if (p0.readInt()) {
          return p1.createFromParcel(p0);
       }
       return null;
    }
    private static void writeTypedObject(Parcel p0,Parcelable p1,int p2){
       if (p1 != null) {
          p0.writeInt(1);
          p1.writeToParcel(p0, p2);
       }else {
          p0.writeInt(0);
       }
       return;
    }
}
