package android.taobao.windvane.extra.uc.WVUCWebViewClient$8;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.WVUCWebViewClient;
import com.uc.webview.export.WebView;
import com.uc.webview.export.extension.RenderProcessGoneDetail;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.uc.WVUCWebView;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.extra.performance2.WVPageTracker;
import java.lang.StringBuilder;
import com.alibaba.mtl.appmonitor.AppMonitor$Alarm;

public class WVUCWebViewClient$8 implements Runnable	// class@00026a from classes.dex
{
    public final WVUCWebViewClient this$0;
    public final RenderProcessGoneDetail val$detail;
    public final WebView val$webview;
    public static IpChange $ipChange;

    public void WVUCWebViewClient$8(WVUCWebViewClient p0,WebView p1,RenderProcessGoneDetail p2){
       this.this$0 = p0;
       this.val$webview = p1;
       this.val$detail = p2;
       super();
    }
    public void run(){
       IpChange $ipChange = WVUCWebViewClient$8.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else if(!this.val$webview.isDestroied()){
          WVUCWebViewClient$8 tval$webview = this.val$webview;
          if (tval$webview instanceof WVUCWebView && vpw.commonConfig.c1 != null) {
             String currentUrl = tval$webview.getCurrentUrl();
             tval$webview.pageTracker.onPageRenderProcessTerminate(currentUrl);
             AppMonitor$Alarm.commitFail("WindVane", "webProcessTerminated", currentUrl, "1", this.val$detail.didCrash()+":"+this.val$detail.rendererPriorityAtExit());
          }
          tval$webview = this.val$webview;
          if (tval$webview instanceof WVUCWebView) {
             tval$webview.refreshWhenForeground();
          }else {
             tval$webview.reload();
          }
       }
       return;
    }
}
