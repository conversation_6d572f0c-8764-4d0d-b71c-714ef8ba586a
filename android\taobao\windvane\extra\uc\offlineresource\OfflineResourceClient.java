package android.taobao.windvane.extra.uc.offlineresource.OfflineResourceClient;
import com.uc.webview.export.extension.IOfflineResourceClient;
import tb.t2o;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import com.uc.webview.export.WebResourceResponse;
import anetwork.channel.degrade.DegradableNetwork;
import tb.yaa;
import android.content.Context;
import anetwork.channel.entity.RequestImpl;
import anetwork.channel.Request;
import anetwork.channel.Response;
import anetwork.channel.aidl.adapter.NetworkProxy;
import android.taobao.windvane.util.MimeTypeEnum;
import java.io.ByteArrayInputStream;
import java.io.InputStream;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.lang.Integer;
import tb.v7t;
import tb.y8o;
import com.taobao.zcache.ResourceResponse;
import tb.yox;
import com.taobao.zcache.Error;
import tb.vpw;
import tb.wpw;
import java.util.Map;
import java.lang.Boolean;

public final class OfflineResourceClient extends IOfflineResourceClient	// class@000276 from classes.dex
{
    public static IpChange $ipChange;
    public static final OfflineResourceClient INSTANCE;
    private static final String TAG;
    private static boolean enabled;

    static {
       t2o.a(0x3d8001ab);
       OfflineResourceClient.INSTANCE = new OfflineResourceClient();
       OfflineResourceClient.TAG = "OfflineResourceClient";
    }
    private void OfflineResourceClient(){
       super();
    }
    public static final OfflineResourceClient getInstance(){
       IpChange $ipChange = OfflineResourceClient.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return OfflineResourceClient.INSTANCE;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("969a6f93", objArray);
    }
    private final WebResourceResponse getResourceFromNetwork(String p0){
       Response response;
       IpChange $ipChange = OfflineResourceClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("54ae3b51", objArray);
       }else {
          RequestImpl requestImpl = new RequestImpl(p0);
          requestImpl.setMethod("GET");
          WebResourceResponse webResourceR = null;
          if ((response = new DegradableNetwork(yaa.n).syncSend(requestImpl, webResourceR)) != null && response.getStatusCode() == 200) {
             webResourceR = new WebResourceResponse(MimeTypeEnum.JS.getMimeType(), "UTF-8", new ByteArrayInputStream(response.getBytedata()));
          }
          return webResourceR;
       }
    }
    public static Object ipc$super(OfflineResourceClient p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/offlineresource/OfflineResourceClient");
    }
    public WebResourceResponse getResource(int p0,String p1){
       ResourceResponse resourceResp;
       WebResourceResponse vpw.commonConfig.G3;
       int i = 1;
       IpChange $ipChange = OfflineResourceClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          return $ipChange.ipc$dispatch("c8cffca2", objArray);
       }else {
          String tAG = OfflineResourceClient.TAG;
          v7t.i(tAG, "getResource, type:"+p0+",url:"+p1);
          if (p0 == i && p1 != null) {
             if ((resourceResp = yox.h(new y8o(p1))) != null && (resourceResp.getError() == null && resourceResp.getData() != null)) {
                v7t.i(tAG, "use zcache resource for offline resource:".concat(p1));
                return new WebResourceResponse(MimeTypeEnum.JS.getMimeType(), "UTF-8", new ByteArrayInputStream(resourceResp.getData()));
             }else if(vpw.commonConfig.G3 != null){
                vpw.commonConfig.G3 = new WebResourceResponse(MimeTypeEnum.JS.getMimeType(), "UTF-8", 404, "Not Found", null, null);
                return vpw.commonConfig.G3;
             }
          }
          return null;
       }
    }
    public final boolean isEnabled(){
       IpChange $ipChange = OfflineResourceClient.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return OfflineResourceClient.enabled;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("56f023c2", objArray).booleanValue();
    }
    public final void setEnabled(boolean p0){
       IpChange $ipChange = OfflineResourceClient.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("4bb1a20e", objArray);
          return;
       }else {
          OfflineResourceClient.enabled = p0;
          return;
       }
    }
}
