package android.taobao.windvane.extra.uc.ExtImgDecoder$DecoderListener;
import com.uc.webview.export.extension.ExtImageDecoder$ImageDecoderListener;
import tb.t2o;
import android.taobao.windvane.extra.uc.ExtImgDecoder;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.Integer;
import java.lang.StringBuilder;
import tb.v7t;
import tb.y71;

public class ExtImgDecoder$DecoderListener implements ExtImageDecoder$ImageDecoderListener	// class@000210 from classes.dex
{
    private String format;
    private boolean imageDecodeSuccess;
    public final ExtImgDecoder this$0;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800146);
    }
    public void ExtImgDecoder$DecoderListener(ExtImgDecoder p0,String p1){
       this.this$0 = p0;
       super();
       this.imageDecodeSuccess = false;
       this.format = p1;
    }
    public static boolean access$000(ExtImgDecoder$DecoderListener p0){
       IpChange $ipChange = ExtImgDecoder$DecoderListener.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.imageDecodeSuccess;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("2abfbfd2", objArray).booleanValue();
    }
    public Object invoke(int p0,Object[] p1){
       IpChange $ipChange = ExtImgDecoder$DecoderListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          return $ipChange.ipc$dispatch("759fd805", objArray);
       }else {
          v7t.d("ExtImgDecoder", "invoke\(\) called with: methodID = ["+p0+"], params = ["+p1+"]");
          return null;
       }
    }
    public void onDecode(String p0,String p1,int p2){
       IpChange $ipChange = ExtImgDecoder$DecoderListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2)};
          $ipChange.ipc$dispatch("4be3f23b", objArray);
          return;
       }else if(p2){
          v7t.d("ExtImgDecoder", "onDecode url = "+p0+" format = "+p1+" result = "+p2);
          y71.commitFail("DecodeImgFailURL", p2, p1, p0);
       }else {
          y71.commitCounter("DecodeImg", "success", 1.00f);
       }
       return;
    }
    public void onInit(int p0){
       int i = 1;
       int i1 = 2;
       IpChange $ipChange = ExtImgDecoder$DecoderListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[i] = new Integer(p0);
          $ipChange.ipc$dispatch("ebf9012d", objArray);
          return;
       }else {
          v7t.d("ExtImgDecoder", "DecoderListener onInit "+p0);
          if (p0) {
             if (p0 != i) {
                if (p0 != i1) {
                   if (p0 != 3) {
                      v7t.d("ExtImgDecoder", "unknown");
                      y71.commitFail("DecoderInit", p0, "unknown", this.format);
                   }else {
                      v7t.d("ExtImgDecoder", "uc core not support");
                      y71.commitFail("DecoderInit", p0, "uc core not support", this.format);
                   }
                }else {
                   v7t.d("ExtImgDecoder", "can\'t load function");
                   y71.commitFail("DecoderInit", p0, "can\'t load function", this.format);
                }
             }else {
                v7t.d("ExtImgDecoder", "can\'t load library");
                y71.commitFail("DecoderInit", p0, "can\'t load library", this.format);
             }
          }else {
             v7t.d("ExtImgDecoder", "ok");
             this.imageDecodeSuccess = i;
             y71.commitSuccess("DecoderInit", this.format);
          }
          return;
       }
    }
}
