package android.taobao.windvane.extra.uc.MtopSsrNetworkAdapter;
import android.taobao.windvane.extra.uc.interfaces.INetwork;
import tb.t2o;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import android.taobao.windvane.extra.uc.MtopSsrNetworkAdapter$1;
import android.taobao.windvane.extra.uc.MtopSsrNetworkAdapter$MtopRequestListener;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import java.util.Map;
import android.taobao.windvane.extra.uc.interfaces.IRequest;
import java.lang.Boolean;
import java.lang.Long;
import java.lang.Integer;
import android.taobao.windvane.extra.uc.MTopSSRRequest;
import java.lang.Number;
import tb.bgq;
import tb.nnf;
import android.taobao.windvane.extra.uc.MtopSsrServiceFactory;
import android.taobao.windvane.extra.uc.MtopSsrNetworkAdapter$2;
import tb.mnf;
import android.os.Handler;

public class MtopSsrNetworkAdapter implements INetwork	// class@000219 from classes.dex
{
    private final INetwork mDowngradeNetwork;
    private MtopSsrNetworkAdapter$MtopRequestListener mMtopRequestListener;
    private String mParentId;
    private long mRequestId;
    private WVUCWebView mWebView;
    public static IpChange $ipChange;
    public static final int EXCEPTION_FAILED_CODE;
    private static final String MODULE;

    static {
       t2o.a(0x3d80014b);
       t2o.a(0x3d8001a7);
    }
    public void MtopSsrNetworkAdapter(INetwork p0,WVUCWebView p1){
       super();
       this.mRequestId = 0;
       this.mParentId = "";
       this.mDowngradeNetwork = p0;
       this.mWebView = p1;
       this.setRequestListener(new MtopSsrNetworkAdapter$1(this));
    }
    public static String access$000(MtopSsrNetworkAdapter p0){
       IpChange $ipChange = MtopSsrNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mParentId;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("34c2ccc7", objArray);
    }
    public static INetwork access$100(MtopSsrNetworkAdapter p0){
       IpChange $ipChange = MtopSsrNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mDowngradeNetwork;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("e24952f", objArray);
    }
    private void setRequestListener(MtopSsrNetworkAdapter$MtopRequestListener p0){
       IpChange $ipChange = MtopSsrNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e2f05f13", objArray);
          return;
       }else {
          this.mMtopRequestListener = p0;
          return;
       }
    }
    public IRequest formatRequest(EventHandler p0,String p1,String p2,boolean p3,Map p4,Map p5,Map p6,Map p7,long p8,int p9,int p10){
       IpChange $ipChange = MtopSsrNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[12];
          objArray[0] = this;
          objArray[1] = p0;
          objArray[2] = p1;
          objArray[3] = p2;
          objArray[4] = new Boolean(p3);
          objArray[5] = p4;
          objArray[6] = p5;
          objArray[7] = p6;
          objArray[8] = p7;
          objArray[9] = new Long(p8);
          objArray[10] = new Integer(p9);
          objArray[11] = new Integer(p10);
          return $ipChange.ipc$dispatch("5c346fdd", objArray);
       }else {
          MTopSSRRequest $ipChange1 = new MTopSSRRequest(p0, p1, p2, p3, p4, p5, p6, p7, p8, p9, p10);
          return $ipChange;
       }
    }
    public int getNetworkType(){
       int i = 1;
       IpChange $ipChange = MtopSsrNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          i = $ipChange.ipc$dispatch("700d68cc", objArray).intValue();
       }
       return i;
    }
    public String getVersion(){
       IpChange $ipChange = MtopSsrNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "1.0";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2a8fef97", objArray);
    }
    public boolean requestURL(EventHandler p0,String p1,String p2,boolean p3,Map p4,Map p5,Map p6,Map p7,long p8,int p9,int p10){
       IpChange $ipChange = MtopSsrNetworkAdapter.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.sendRequest(this.formatRequest(p0, p1, p2, p3, p4, p5, p6, p7, p8, p9, p10));
       }
       Object[] objArray = new Object[12];
       objArray[0] = this;
       objArray[1] = p0;
       objArray[2] = p1;
       objArray[3] = p2;
       objArray[4] = new Boolean(p3);
       objArray[5] = p4;
       objArray[6] = p5;
       objArray[7] = p6;
       objArray[8] = p7;
       objArray[9] = new Long(p8);
       objArray[10] = new Integer(p9);
       objArray[11] = new Integer(p10);
       return $ipChange.ipc$dispatch("85230ab7", objArray).booleanValue();
    }
    public boolean sendRequest(IRequest p0){
       bgq ssrRequest;
       MtopSsrNetworkAdapter tmMtopReques;
       int i = 0;
       IpChange $ipChange = MtopSsrNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("f55a9b04", objArray).booleanValue();
       }else if(p0 instanceof MTopSSRRequest){
          if ((ssrRequest = p0.getSsrRequest()) == null) {
             return i;
          }else {
             nnf onnf = MtopSsrServiceFactory.createSsrService();
             EventHandler eventHandler = p0.getEventHandler();
             if (onnf != null && eventHandler != null) {
                long l = this.mRequestId + 1;
                this.mRequestId = l;
                if ((tmMtopReques = this.mMtopRequestListener) != null) {
                   tmMtopReques.beforeRequest(l, ssrRequest.a, ssrRequest.c);
                }
                MtopSsrNetworkAdapter$2 i1 = new MtopSsrNetworkAdapter$2(this, eventHandler, tmMtopReques, l, p0);
                return onnf.asyncSend(ssrRequest, i, null);
             }
          }
       }
       return i;
    }
    public void setParentId(String p0){
       IpChange $ipChange = MtopSsrNetworkAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d3aaf138", objArray);
          return;
       }else {
          this.mParentId = p0;
          return;
       }
    }
}
