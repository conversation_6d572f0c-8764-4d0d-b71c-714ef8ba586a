package android.taobao.windvane.extra.uc.APIContextHelper;
import tb.t2o;
import java.lang.Object;
import android.net.Uri;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import android.os.Looper;
import java.util.concurrent.CountDownLatch;
import android.os.Handler;
import android.taobao.windvane.extra.uc.APIContextHelper$1;
import java.lang.Runnable;
import java.util.concurrent.TimeUnit;
import java.lang.RuntimeException;
import java.lang.Throwable;
import android.util.Base64;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSON;
import com.alibaba.ability.hub.AbilityHubAdapter;
import tb.zq;
import tb.yaa;
import android.content.Context;
import tb.ldb;
import java.util.Iterator;
import tb.xq;
import tb.kdb;
import com.alibaba.fastjson.JSONObject;
import android.taobao.windvane.extra.uc.APIContextHelper$2;
import java.util.Map;
import tb.s2d;
import com.alibaba.ability.result.ExecuteResult;

public class APIContextHelper	// class@0001f9 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d80012c);
    }
    public void APIContextHelper(){
       super();
    }
    public static String access$000(Uri p0,String p1){
       IpChange $ipChange = APIContextHelper.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return APIContextHelper.getAPICallRecordsInternal(p0, p1);
       }
       Object[] objArray = new Object[]{p0,p1};
       return $ipChange.ipc$dispatch("ccb44f66", objArray);
    }
    public static String getAPICallRecords(String p0){
       Uri uri;
       String queryParamet;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = APIContextHelper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("ab9bfdaf", objArray);
       }else {
          String str = null;
          if (p0 == null) {
             return str;
          }
          if ((uri = Uri.parse(p0)) == null) {
             return str;
          }
          if ((queryParamet = uri.getQueryParameter("api-info")) == null) {
             return str;
          }
          if (Looper.getMainLooper() == Looper.myLooper()) {
             return APIContextHelper.getAPICallRecordsInternal(uri, queryParamet);
          }
          CountDownLatch uCountDownLa = new CountDownLatch(i1);
          String[] stringArray = new String[]{str};
          Handler handler = new Handler(Looper.getMainLooper());
          APIContextHelper$1 u1 = new APIContextHelper$1(stringArray, uri, queryParamet, uCountDownLa);
          try{
             handler.post(u1);
             uCountDownLa.await(1, TimeUnit.SECONDS);
             return stringArray[i];
          }catch(java.lang.InterruptedException e6){
             throw new RuntimeException(e6);
          }
       }
    }
    private static String getAPICallRecordsInternal(Uri p0,String p1){
       JSONArray jSONArray;
       String str1;
       String[] stringArray;
       ExecuteResult uExecuteResu;
       int i = 2;
       String str = "WindVane";
       IpChange $ipChange = APIContextHelper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          objArray[1] = p1;
          return $ipChange.ipc$dispatch("f22dd4d3", objArray);
       }else if((jSONArray = JSON.parseArray(new String(Base64.decode(p1, 0)))) != null){
          JSONArray jSONArray1 = new JSONArray();
          AbilityHubAdapter uAbilityHubA = new AbilityHubAdapter(new zq(str, str).a(yaa.n));
          Iterator iterator = jSONArray.iterator();
          while (true) {
             if (iterator.hasNext()) {
                Object obj = iterator.next();
                if (obj instanceof JSONArray) {
                   if (obj.size() < i) {
                      return null;
                   }else if((str1 = obj.getString(0)) == null){
                      return null;
                   }else if((stringArray = str1.split("\\.")) != null && stringArray.length >= i){
                      xq oxq = new xq();
                      oxq.d("url", p0.toString());
                      if ((uExecuteResu = uAbilityHubA.z(stringArray[0], stringArray[1], oxq, obj.getJSONObject(1), new APIContextHelper$2())) != null) {
                         obj.add(uExecuteResu.toFormattedData());
                         jSONArray1.add(obj);
                      }
                   }else {
                      return null;
                   }
                }
             }else if(jSONArray1.size() > 0){
                return jSONArray1.toJSONString();
             }
          }
       }
       return null;
    }
}
