package androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VideoOnly;
import androidx.activity.result.contract.ActivityResultContracts$PickVisualMedia$VisualMediaType;
import java.lang.Object;

public final class ActivityResultContracts$PickVisualMedia$VideoOnly implements ActivityResultContracts$PickVisualMedia$VisualMediaType	// class@0004d5 from classes.dex
{
    public static final ActivityResultContracts$PickVisualMedia$VideoOnly INSTANCE;

    static {
       ActivityResultContracts$PickVisualMedia$VideoOnly.INSTANCE = new ActivityResultContracts$PickVisualMedia$VideoOnly();
    }
    private void ActivityResultContracts$PickVisualMedia$VideoOnly(){
       super();
    }
}
