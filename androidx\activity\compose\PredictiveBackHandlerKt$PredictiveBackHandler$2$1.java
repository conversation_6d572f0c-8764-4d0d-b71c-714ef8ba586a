package androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$2$1;
import tb.u1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import androidx.activity.compose.PredictiveBackHandlerCallback;
import tb.ar4;
import java.lang.Object;
import tb.uu4;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;

public final class PredictiveBackHandlerKt$PredictiveBackHandler$2$1 extends SuspendLambda implements u1a	// class@00048e from classes.dex
{
    public final PredictiveBackHandlerCallback $backCallBack;
    public final boolean $enabled;
    public int label;

    public void PredictiveBackHandlerKt$PredictiveBackHandler$2$1(PredictiveBackHandlerCallback p0,boolean p1,ar4 p2){
       this.$backCallBack = p0;
       this.$enabled = p1;
       super(2, p2);
    }
    public final ar4 create(Object p0,ar4 p1){
       return new PredictiveBackHandlerKt$PredictiveBackHandler$2$1(this.$backCallBack, this.$enabled, p1);
    }
    public Object invoke(Object p0,Object p1){
       return this.invoke(p0, p1);
    }
    public final Object invoke(uu4 p0,ar4 p1){
       return this.create(p0, p1).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       dkf.d();
       if (this.label != null) {
          throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
       }
       b.b(p0);
       this.$backCallBack.setIsEnabled(this.$enabled);
       return xhv.INSTANCE;
    }
}
