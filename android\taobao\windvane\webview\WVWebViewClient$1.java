package android.taobao.windvane.webview.WVWebViewClient$1;
import android.webkit.ValueCallback;
import android.taobao.windvane.webview.WVWebViewClient;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;
import tb.v7t;

public class WVWebViewClient$1 implements ValueCallback	// class@000315 from classes.dex
{
    public final WVWebViewClient this$0;
    public static IpChange $ipChange;

    public void WVWebViewClient$1(WVWebViewClient p0){
       this.this$0 = p0;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       IpChange $ipChange = WVWebViewClient$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else {
          v7t.d("WVWebViewClient", "JSTfsp receiveValue "+p0);
          return;
       }
    }
}
