package android.taobao.windvane.export.prerender.TMSPrerenderService$setup$1;
import tb.obk;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import com.taobao.orange.OrangeConfig;
import java.lang.CharSequence;
import android.text.TextUtils;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import android.taobao.windvane.export.prerender.TMSPrerenderService;
import android.taobao.windvane.export.prerender.TMSPrerenderModel;
import java.lang.Class;
import com.alibaba.fastjson.JSON;
import android.taobao.windvane.export.prerender.TMSPrerenderService$setup$1$1$1;
import tb.g1a;
import java.lang.Boolean;

public final class TMSPrerenderService$setup$1 implements obk	// class@00017f from classes.dex
{
    public static IpChange $ipChange;
    public static final TMSPrerenderService$setup$1 INSTANCE;

    static {
       TMSPrerenderService$setup$1.INSTANCE = new TMSPrerenderService$setup$1();
    }
    public void TMSPrerenderService$setup$1(){
       super();
    }
    public final void onConfigUpdate(String p0,Map p1){
       TMSPrerenderModel tMSPrerender;
       int i = 1;
       IpChange $ipChange = TMSPrerenderService$setup$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4f2fc4ea", objArray);
          return;
       }else if(ckf.b("themis_prerender_config", p0)){
          p0 = OrangeConfig.getInstance().getCustomConfig("themis_prerender_config", null);
          if (TextUtils.isEmpty(p0)) {
             lcn.a(RVLLevel.Info, "Themis/Performance/Prerender").j("orangeUpdate").a("msg", "configValue is empty").f();
             return;
          }else {
             TMSPrerenderService iNSTANCE = TMSPrerenderService.INSTANCE;
             if (TextUtils.equals(p0, TMSPrerenderService.a(iNSTANCE))) {
                lcn.a(RVLLevel.Info, "Themis/Performance/Prerender").j("orangeUpdate").a("msg", "configValue is same").f();
                return;
             }else {
                icn oicn = lcn.a(RVLLevel.Info, "Themis/Performance/Prerender").j("orangeUpdate");
                try{
                   _monitor_enter(iNSTANCE);
                   TMSPrerenderService.c(iNSTANCE, p0);
                   TMSPrerenderService.d(iNSTANCE, JSON.parseObject(p0, TMSPrerenderModel.class));
                   if ((tMSPrerender = TMSPrerenderService.b(iNSTANCE)) != null && tMSPrerender.getAutoStart() == i) {
                      iNSTANCE.f(TMSPrerenderService$setup$1$1$1.INSTANCE);
                   }
                   oicn.a("updateSuccess", Boolean.TRUE);
                }catch(java.lang.Exception e0){
                   oicn.a("updateFail", Boolean.FALSE);
                }
                _monitor_exit(iNSTANCE);
                oicn.f();
             }
          }
       }
       return;
    }
}
