package android.taobao.windvane.extra.jsbridge.WVMega$1;
import java.lang.Runnable;
import android.taobao.windvane.extra.jsbridge.WVMega;
import java.lang.String;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;

public class WVMega$1 implements Runnable	// class@0001a4 from classes.dex
{
    public final WVMega this$0;
    public final WVCallBackContext val$callback;
    public final String val$params;
    public static IpChange $ipChange;

    public void WVMega$1(WVMega p0,String p1,WVCallBackContext p2){
       this.this$0 = p0;
       this.val$params = p1;
       this.val$callback = p2;
       super();
    }
    public void run(){
       IpChange $ipChange = WVMega$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          WVMega.access$000(this.this$0, this.val$params, this.val$callback);
          return;
       }
    }
}
