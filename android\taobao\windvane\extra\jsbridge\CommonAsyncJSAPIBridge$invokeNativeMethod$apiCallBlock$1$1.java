package android.taobao.windvane.extra.jsbridge.CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import android.taobao.windvane.extra.jsbridge.CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1;
import android.taobao.windvane.webview.IWVWebView;
import android.taobao.windvane.extra.jsbridge.WVMegaBridgeContext;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.extra.jsbridge.CommonAsyncJSAPIBridge;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import tb.ckf;
import com.uc.webview.export.extension.JSInterface$JSRoute;
import tb.nsw;

public final class CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1 extends WVCallBackContext	// class@000193 from classes.dex
{
    public final CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 this$0;
    public static IpChange $ipChange;

    public void CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1(CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 p0,IWVWebView p1){
       this.this$0 = p0;
       super(p1);
       this.currentUrlFromAsyncChannel = p0.$currentUrl;
       this.wvMegaBridgeContext = new WVMegaBridgeContext(vpw.commonConfig.C2, p0.$callToken, CommonAsyncJSAPIBridge.access$getParentId$p(p0.this$0));
    }
    public static Object ipc$super(CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/jsbridge/CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1");
    }
    public void error(){
       IpChange $ipChange = CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("1392128f", objArray);
          return;
       }else {
          CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1 tthis$0 = this.this$0;
          CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 $jsRoute = tthis$0.$jsRoute;
          ckf.f($jsRoute, "jsRoute");
          CommonAsyncJSAPIBridge.sendJSResult$default(tthis$0.this$0, tthis$0.$callToken, $jsRoute, false, null, false, 24, null);
          return;
       }
    }
    public void error(String p0){
       IpChange $ipChange = CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("72e35699", objArray);
          return;
       }else {
          CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1 tthis$0 = this.this$0;
          CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 $jsRoute = tthis$0.$jsRoute;
          ckf.f($jsRoute, "jsRoute");
          CommonAsyncJSAPIBridge.sendJSResult$default(tthis$0.this$0, tthis$0.$callToken, $jsRoute, false, new nsw(p0).m(), false, 16, null);
          return;
       }
    }
    public void error(String p0,String p1){
       IpChange $ipChange = CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4a936c23", objArray);
          return;
       }else {
          nsw onsw = new nsw("HY_FAILED");
          onsw.b(p0, p1);
          CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1 tthis$0 = this.this$0;
          CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 $jsRoute = tthis$0.$jsRoute;
          ckf.f($jsRoute, "jsRoute");
          CommonAsyncJSAPIBridge.sendJSResult$default(tthis$0.this$0, tthis$0.$callToken, $jsRoute, false, onsw.m(), false, 16, null);
          return;
       }
    }
    public void error(nsw p0){
       IpChange $ipChange = CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5660aa7d", objArray);
          return;
       }else if(p0 != null){
          p0.j("HY_FAILED");
       }
       CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1 tthis$0 = this.this$0;
       CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 this$0 = tthis$0.this$0;
       CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 $callToken = tthis$0.$callToken;
       CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 $jsRoute = tthis$0.$jsRoute;
       ckf.f($jsRoute, "jsRoute");
       String str = (p0 != null)? p0.m(): 0;
       String str1 = str;
       CommonAsyncJSAPIBridge.sendJSResult$default(this$0, $callToken, $jsRoute, false, str1, false, 16, null);
       return;
    }
    public void onFailure(nsw p0){
       IpChange $ipChange = CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4c5c5b7a", objArray);
          return;
       }else {
          this.error(p0);
          return;
       }
    }
    public void onSuccess(nsw p0){
       IpChange $ipChange = CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("43640fe1", objArray);
          return;
       }else {
          this.success(p0);
          return;
       }
    }
    public void success(){
       IpChange $ipChange = CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("b4550b0a", objArray);
          return;
       }else {
          CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1 tthis$0 = this.this$0;
          CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 $jsRoute = tthis$0.$jsRoute;
          ckf.f($jsRoute, "jsRoute");
          CommonAsyncJSAPIBridge.sendJSResult$default(tthis$0.this$0, tthis$0.$callToken, $jsRoute, false, nsw.RET_SUCCESS.m(), false, 20, null);
          return;
       }
    }
    public void success(String p0){
       IpChange $ipChange = CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("6b54654", objArray);
          return;
       }else {
          CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1 tthis$0 = this.this$0;
          CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 $jsRoute = tthis$0.$jsRoute;
          ckf.f($jsRoute, "jsRoute");
          CommonAsyncJSAPIBridge.sendJSResult$default(tthis$0.this$0, tthis$0.$callToken, $jsRoute, false, p0, false, 20, null);
          return;
       }
    }
    public void success(nsw p0){
       int i = 0;
       IpChange $ipChange = CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3e095322", objArray);
          return;
       }else if(p0 != null){
          p0.j("HY_SUCCESS");
       }
       CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1 tthis$0 = this.this$0;
       CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 this$0 = tthis$0.this$0;
       CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 $callToken = tthis$0.$callToken;
       CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 $jsRoute = tthis$0.$jsRoute;
       ckf.f($jsRoute, "jsRoute");
       String str = (p0 != null)? p0.m(): 0;
       String str1 = str;
       boolean b = (p0 != null)? p0.f(): false;
       CommonAsyncJSAPIBridge.sendJSResult$default(this$0, $callToken, $jsRoute, false, str1, b, 4, null);
       return;
    }
}
