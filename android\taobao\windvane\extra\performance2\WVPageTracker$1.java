package android.taobao.windvane.extra.performance2.WVPageTracker$1;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.performance2.WVPageTracker;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;
import org.json.JSONObject;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import java.lang.Throwable;

public class WVPageTracker$1 implements ValueCallback	// class@0001da from classes.dex
{
    public final WVPageTracker this$0;
    public static IpChange $ipChange;

    public void WVPageTracker$1(WVPageTracker p0){
       this.this$0 = p0;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       String str3;
       object oobject = this;
       int i = 1;
       String str = "le";
       String str1 = "ls";
       String str2 = "ds";
       IpChange $ipChange = WVPageTracker$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else if(TextUtils.isEmpty(p0)){
          str3 = "{}";
       }else {
          str3 = p0;
       }
       String str4 = "\"";
       if (str3.startsWith(str4) && str3.endsWith(str4)) {
          str3 = str3.substring(i, (str3.length() - i));
       }
       try{
          JSONObject i1 = new JSONObject(str3.replace("\\", ""));
          long l = i1.optLong("ns");
          long l1 = i1.optLong("rs");
          long l2 = i1.optLong("re");
          long l3 = i1.optLong("dc");
          long l4 = i1.optLong(str1);
          String str5 = str1;
          String str6 = str2;
          long l5 = i1.optLong(str);
          RVLLevel info = RVLLevel.Info;
          String str7 = str;
          JSONObject jSONObject = i1;
          long l6 = l5;
          lcn.a(info, WVPageTracker.RVLOG_PAGE_MODEL).k("navigationStart", WVPageTracker.access$100(oobject.this$0)).m(WVPageTracker.access$000(oobject.this$0)).n(l).f();
          lcn.a(info, WVPageTracker.RVLOG_PAGE_MODEL).k("requestStart", WVPageTracker.access$100(oobject.this$0)).n(l1).f();
          lcn.a(info, WVPageTracker.RVLOG_PAGE_MODEL).k("responseEnd", WVPageTracker.access$100(oobject.this$0)).n(l2).f();
          lcn.a(info, WVPageTracker.RVLOG_PAGE_MODEL).k("domComplete", WVPageTracker.access$100(oobject.this$0)).n(l3).f();
          lcn.a(info, WVPageTracker.RVLOG_PAGE_MODEL).k("domContentLoadedEventStart", WVPageTracker.access$100(oobject.this$0)).n(i1.optLong(str2)).f();
          lcn.a(info, WVPageTracker.RVLOG_PAGE_MODEL).k("loadEventStart", WVPageTracker.access$100(oobject.this$0)).n(l4).f();
          lcn.a(info, WVPageTracker.RVLOG_PAGE_MODEL).k("loadEventEnd", WVPageTracker.access$100(oobject.this$0)).n(l6).f();
          if ((WVPageTracker.access$200(oobject.this$0)) > 0) {
             lcn.a(info, WVPageTracker.RVLOG_PAGE_MODEL).k("firstPaint", WVPageTracker.access$100(oobject.this$0)).n((WVPageTracker.access$200(oobject.this$0) + l)).f();
          }else {
             WVPageTracker.access$302(oobject.this$0, l);
          }
          WVPageTracker$1 this$0 = oobject.this$0;
          WVPageTracker.access$500(this$0, WVPageTracker.access$400(this$0)[6], l);
          this$0 = oobject.this$0;
          JSONObject jSONObject1 = jSONObject;
          WVPageTracker.access$500(this$0, WVPageTracker.access$400(this$0)[7], jSONObject1.optLong("fs"));
          this$0 = oobject.this$0;
          WVPageTracker.access$500(this$0, WVPageTracker.access$400(this$0)[8], l2);
          this$0 = oobject.this$0;
          WVPageTracker.access$500(this$0, WVPageTracker.access$400(this$0)[9], jSONObject1.optLong(str6));
          this$0 = oobject.this$0;
          WVPageTracker.access$500(this$0, WVPageTracker.access$400(this$0)[10], jSONObject1.optLong(str5));
          this$0 = oobject.this$0;
          WVPageTracker.access$500(this$0, WVPageTracker.access$400(this$0)[11], jSONObject1.optLong(str7));
       }catch(org.json.JSONException e0){
          e0.printStackTrace();
       }
       return;
    }
}
