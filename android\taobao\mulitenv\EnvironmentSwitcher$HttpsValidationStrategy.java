package android.taobao.mulitenv.EnvironmentSwitcher$HttpsValidationStrategy;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class EnvironmentSwitcher$HttpsValidationStrategy extends Enum	// class@00013f from classes.dex
{
    private static final EnvironmentSwitcher$HttpsValidationStrategy[] $VALUES;
    public static IpChange $ipChange;
    public static final EnvironmentSwitcher$HttpsValidationStrategy DISABLE_DEGRADE;
    public static final EnvironmentSwitcher$HttpsValidationStrategy ENABLE_DEGRADE;

    static {
       EnvironmentSwitcher$HttpsValidationStrategy httpsValidat = new EnvironmentSwitcher$HttpsValidationStrategy("ENABLE_DEGRADE", 0);
       EnvironmentSwitcher$HttpsValidationStrategy.ENABLE_DEGRADE = httpsValidat;
       EnvironmentSwitcher$HttpsValidationStrategy httpsValidat1 = new EnvironmentSwitcher$HttpsValidationStrategy("DISABLE_DEGRADE", 1);
       EnvironmentSwitcher$HttpsValidationStrategy.DISABLE_DEGRADE = httpsValidat1;
       EnvironmentSwitcher$HttpsValidationStrategy[] httpsValidat2 = new EnvironmentSwitcher$HttpsValidationStrategy[]{httpsValidat,httpsValidat1};
       EnvironmentSwitcher$HttpsValidationStrategy.$VALUES = httpsValidat2;
    }
    private void EnvironmentSwitcher$HttpsValidationStrategy(String p0,int p1){
       super(p0, p1);
    }
    public static Object ipc$super(EnvironmentSwitcher$HttpsValidationStrategy p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/mulitenv/EnvironmentSwitcher$HttpsValidationStrategy");
    }
    public static EnvironmentSwitcher$HttpsValidationStrategy valueOf(String p0){
       IpChange $ipChange = EnvironmentSwitcher$HttpsValidationStrategy.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(EnvironmentSwitcher$HttpsValidationStrategy.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("6f683a13", objArray);
    }
    public static EnvironmentSwitcher$HttpsValidationStrategy[] values(){
       IpChange $ipChange = EnvironmentSwitcher$HttpsValidationStrategy.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return EnvironmentSwitcher$HttpsValidationStrategy.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("bf463842", objArray);
    }
}
