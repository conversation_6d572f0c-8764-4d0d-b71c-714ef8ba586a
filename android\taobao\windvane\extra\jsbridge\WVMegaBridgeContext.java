package android.taobao.windvane.extra.jsbridge.WVMegaBridgeContext;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;

public final class WVMegaBridgeContext	// class@0001aa from classes.dex
{
    private final String callToken;
    private final boolean enableLoggingAPIInvocation;
    private final String parentId;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000df);
    }
    public void WVMegaBridgeContext(boolean p0,String p1,String p2){
       super();
       this.enableLoggingAPIInvocation = p0;
       this.callToken = p1;
       this.parentId = p2;
    }
    public final String getCallToken(){
       IpChange $ipChange = WVMegaBridgeContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.callToken;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7c637894", objArray);
    }
    public final boolean getEnableLoggingAPIInvocation(){
       IpChange $ipChange = WVMegaBridgeContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.enableLoggingAPIInvocation;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("4efa51cf", objArray).booleanValue();
    }
    public final String getParentId(){
       IpChange $ipChange = WVMegaBridgeContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.parentId;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("2ba388fe", objArray);
    }
}
