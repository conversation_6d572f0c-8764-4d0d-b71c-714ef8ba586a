package android.taobao.windvane.extra.jsbridge.WVMegaBridge$1;
import java.lang.Runnable;
import android.taobao.windvane.extra.jsbridge.WVMegaBridge;
import java.lang.String;
import java.util.concurrent.CountDownLatch;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import tb.xq;
import android.taobao.windvane.extra.uc.WVUCWebView;
import tb.ace;
import tb.ace$b;
import java.util.Map;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import tb.kdb;
import android.view.View;
import tb.vpw;
import tb.wpw;
import android.content.Context;
import tb.ldb;
import tb.x74;
import android.os.SystemClock;
import com.alibaba.ability.hub.AbilityHubAdapter;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import android.taobao.windvane.extra.jsbridge.WVMegaBridge$1$1;
import tb.s2d;
import com.alibaba.ability.result.ExecuteResult;
import java.lang.StringBuilder;
import tb.v7t;
import java.lang.Throwable;
import com.alibaba.ability.result.ErrorResult;
import com.alibaba.ability.result.ErrorResult$a;

public class WVMegaBridge$1 implements Runnable	// class@0001a8 from classes.dex
{
    public final WVMegaBridge this$0;
    public final String val$ability;
    public final String val$action;
    public final CountDownLatch val$countDownLatch;
    public final String val$options;
    public final String[] val$res;
    public static IpChange $ipChange;

    public void WVMegaBridge$1(WVMegaBridge p0,String p1,String p2,String p3,String[] p4,CountDownLatch p5){
       this.this$0 = p0;
       this.val$ability = p1;
       this.val$action = p2;
       this.val$options = p3;
       this.val$res = p4;
       this.val$countDownLatch = p5;
       super();
    }
    public void run(){
       ace$b megaUserData;
       Map map;
       String externalCont;
       ExecuteResult uExecuteResu;
       object oobject;
       int i = 0;
       String str = "ut_page_object";
       IpChange $ipChange = WVMegaBridge$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          xq oxq = new xq();
          if (WVMegaBridge.access$000(this.this$0) != null) {
             String url = WVMegaBridge.access$000(this.this$0).getUrl();
             if ((megaUserData = WVMegaBridge.access$000(this.this$0).getWebViewContext().getMegaUserDataMapAdapter()) != null && (map = megaUserData.a(this.val$ability, this.val$action, this.val$options)) != null) {
                Iterator iterator = map.entrySet().iterator();
                while (iterator.hasNext()) {
                   Map$Entry uEntry = iterator.next();
                   String key = uEntry.getKey();
                   oxq.d(key, uEntry.getValue());
                }
             }
             oxq.d("url", url);
             oxq.d("pageId", WVMegaBridge.access$000(this.this$0).getCurId());
             if ((externalCont = WVMegaBridge.access$000(this.this$0).getExternalContext(str)) != null) {
                oxq.d(str, externalCont);
             }
             oxq.p(WVMegaBridge.access$000(this.this$0));
             str = WVMegaBridge.access$000(this.this$0).getWebViewContext().getCustomMegaBizId();
             externalCont = WVMegaBridge.access$000(this.this$0).getWebViewContext().getCustomMegaNamespace();
             if (vpw.commonConfig.o2 != null) {
                oxq.j(x74.b(url, str, externalCont, WVMegaBridge.access$000(this.this$0).getContext()));
             }
          }
          long l = SystemClock.uptimeMillis();
          if ((uExecuteResu = WVMegaBridge.access$100(this.this$0).z(this.val$ability, this.val$action, oxq, JSON.parseObject(this.val$options), new WVMegaBridge$1$1(this))) != null) {
             this.val$res[i] = new JSONObject(uExecuteResu.toFormattedData()).toJSONString();
          }
          long l1 = SystemClock.uptimeMillis();
          if (vpw.commonConfig.A2 != null) {
             l1 = l1 - l;
             if ((l1 - 100) > 0 || this.val$res[i] == null) {
                int i1 = ((oobject = this.val$res[i]) == null)? 0: oobject.length();
                v7t.d("WVMegaBridge", "API: "+this.val$ability+"."+this.val$action+" cost: "+l1+" resLen: "+i1);
             }
          }
          this.val$countDownLatch.countDown();
          return;
       }
    }
}
