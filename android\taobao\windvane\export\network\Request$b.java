package android.taobao.windvane.export.network.Request$b;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.util.Map;
import tb.ecd;
import tb.ikd;
import android.taobao.windvane.export.network.Request;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.util.HashMap;
import android.taobao.windvane.export.network.Request$a;
import java.lang.IllegalStateException;
import java.lang.Boolean;

public class Request$b	// class@000168 from classes.dex
{
    public String a;
    public String b;
    public Map c;
    public boolean d;
    public ecd e;
    public ikd f;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d80008a);
    }
    public void Request$b(){
       super();
       this.d = false;
    }
    public static String a(Request$b p0){
       IpChange $ipChange = Request$b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.a;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("3e767e08", objArray);
    }
    public static String b(Request$b p0){
       IpChange $ipChange = Request$b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.b;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("1c69e3e7", objArray);
    }
    public static Map c(Request$b p0){
       IpChange $ipChange = Request$b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.c;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("4df53ecf", objArray);
    }
    public static ecd d(Request$b p0){
       IpChange $ipChange = Request$b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.e;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("c67d51a2", objArray);
    }
    public static ikd e(Request$b p0){
       IpChange $ipChange = Request$b.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.f;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("79f5a00d", objArray);
    }
    public Request f(){
       IpChange $ipChange = Request$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("525f1bd9", objArray);
       }else if(this.a != null){
          if (TextUtils.isEmpty(this.b)) {
             this.b = "GET";
          }
          if (this.c == null) {
             this.c = new HashMap();
          }
          return new Request(this, null);
       }else {
          throw new IllegalStateException("url = null");
       }
    }
    public Request$b g(Map p0){
       IpChange $ipChange = Request$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ad0aa75d", objArray);
       }else {
          this.c = p0;
          return this;
       }
    }
    public Request$b h(boolean p0){
       IpChange $ipChange = Request$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          return $ipChange.ipc$dispatch("126d99d5", objArray);
       }else {
          this.d = p0;
          return this;
       }
    }
    public Request$b i(String p0){
       IpChange $ipChange = Request$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("228024c3", objArray);
       }else {
          this.b = p0;
          return this;
       }
    }
    public Request$b j(ecd p0){
       IpChange $ipChange = Request$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6ac9f4c0", objArray);
       }else {
          this.e = p0;
          return this;
       }
    }
    public Request$b k(ikd p0){
       IpChange $ipChange = Request$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("d79af56c", objArray);
       }else {
          this.f = p0;
          return this;
       }
    }
    public Request$b l(String p0){
       IpChange $ipChange = Request$b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ebe83739", objArray);
       }else {
          this.a = p0;
          return this;
       }
    }
}
