package android.taobao.windvane.extra.launch.InitOnceTask;
import android.taobao.windvane.extra.launch.ILaunchInitTask;
import tb.t2o;
import java.lang.Object;
import java.util.concurrent.atomic.AtomicBoolean;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import android.app.Application;
import java.util.HashMap;
import tb.r9u;

public abstract class InitOnceTask implements ILaunchInitTask	// class@0001b3 from classes.dex
{
    private final AtomicBoolean mInitialized;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000e8);
       t2o.a(0x3d8000e7);
    }
    public void InitOnceTask(){
       super();
       this.mInitialized = new AtomicBoolean(false);
    }
    public String getName(){
       IpChange $ipChange = InitOnceTask.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "InitOnceTask";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7c09e698", objArray);
    }
    public final void init(Application p0,HashMap p1){
       int i = 1;
       IpChange $ipChange = InitOnceTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("dddb138b", objArray);
          return;
       }else if(this.mInitialized.compareAndSet(0, i)){
          r9u.b(this.getName());
          this.initImpl(p0, p1);
          r9u.d();
       }
       return;
    }
    public abstract void initImpl(Application p0,HashMap p1);
}
