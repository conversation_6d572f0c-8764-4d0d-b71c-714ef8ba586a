package android.taobao.windvane.extra.performance.action.AddPropertyVisitor;
import android.taobao.windvane.extra.performance.action.IPerformanceVisitor;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import tb.cce;
import com.android.alibaba.ip.runtime.IpChange;

public class AddPropertyVisitor implements IPerformanceVisitor	// class@0001d2 from classes.dex
{
    public final String name;
    public final Object value;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800107);
       t2o.a(0x3d80010a);
    }
    public void AddPropertyVisitor(String p0,Object p1){
       super();
       this.name = p0;
       this.value = p1;
    }
    public void accept(cce p0){
       IpChange $ipChange = AddPropertyVisitor.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("987066e1", objArray);
          return;
       }else {
          p0.onProperty(this.name, this.value);
          return;
       }
    }
}
