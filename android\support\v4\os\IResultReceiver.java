package android.support.v4.os.IResultReceiver;
import android.os.IInterface;
import java.lang.String;
import android.os.Bundle;

public interface abstract IResultReceiver implements IInterface	// class@000137 from classes.dex
{
    public static final String DESCRIPTOR;

    static {
       IResultReceiver.DESCRIPTOR = "android$support$v4$os$IResultReceiver".replace('$', '.');
    }
    void send(int p0,Bundle p1);
}
