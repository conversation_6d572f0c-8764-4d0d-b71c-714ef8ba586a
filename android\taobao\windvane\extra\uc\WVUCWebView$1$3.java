package android.taobao.windvane.extra.uc.WVUCWebView$1$3;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.WVUCWebView$1;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.uc.WVUCWebView;
import android.os.Handler;

public class WVUCWebView$1$3 implements Runnable	// class@00024f from classes.dex
{
    public final WVUCWebView$1 this$1;
    public static IpChange $ipChange;

    public void WVUCWebView$1$3(WVUCWebView$1 p0){
       this.this$1 = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = WVUCWebView$1$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          this.this$1.this$0.mHandler.sendEmptyMessage(405);
          return;
       }
    }
}
