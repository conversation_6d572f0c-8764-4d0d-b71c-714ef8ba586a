package android.taobao.windvane.extra.storage.FirstChunkStorage;
import tb.t2o;
import java.lang.String;
import android.taobao.windvane.extra.storage.IStorage;
import android.taobao.windvane.extra.storage.StorageFactory;
import java.lang.Object;
import android.net.Uri;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.net.Uri$Builder;
import tb.vpw;
import tb.wpw;
import tb.jpw;
import tb.jrd;
import java.lang.Class;
import java.lang.StringBuilder;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;
import android.taobao.windvane.extra.storage.strategy.FccStrategy;
import com.android.alibaba.ip.runtime.IpChange;
import java.util.List;
import android.taobao.windvane.extra.storage.FirstChunkStorage$MetaItemResult;
import android.taobao.windvane.extra.storage.FirstChunkStorage$1;
import java.util.Comparator;
import java.util.Collections;
import android.taobao.windvane.extra.storage.strategy.FccStrategyType;
import com.alibaba.fastjson.JSONObject;
import android.taobao.windvane.extra.storage.FirstChunkStorage$HtmlStorageResult;
import android.taobao.windvane.extra.storage.FccStorageType;
import java.lang.Boolean;
import java.lang.Throwable;
import tb.v7t;
import android.taobao.windvane.extra.storage.ResponseContext;
import com.alibaba.fastjson.JSON;
import java.lang.Integer;
import tb.y71;
import java.util.LinkedList;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import tb.icn;
import java.util.Arrays;
import android.taobao.windvane.util.DeviceUtils;
import tb.yaa;
import tb.oba;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.util.HashMap;
import java.lang.Long;
import java.lang.System;

public class FirstChunkStorage	// class@0001e8 from classes.dex
{
    private final FccStrategy mFccStrategy;
    private final String mStorageKey;
    private final Uri mUri;
    private final String mUrl;
    private boolean reportMetaSizeFlag;
    public static IpChange $ipChange;
    private static final IStorage HTML_STORAGE;
    private static final IStorage META_STORAGE;

    static {
       t2o.a(0x3d80011a);
       FirstChunkStorage.META_STORAGE = StorageFactory.createStorageInstance("WindVaneFirstChunkV2");
       FirstChunkStorage.HTML_STORAGE = StorageFactory.createStorageInstance("WindVaneFirstChunkV2-HTML");
    }
    public void FirstChunkStorage(String p0){
       Uri uri;
       jrd ojrd;
       super();
       this.reportMetaSizeFlag = false;
       try{
          this.mUrl = p0;
          uri = Uri.parse(p0);
       }catch(java.lang.Exception e0){
          uri = Uri.EMPTY;
       }
       this.mUri = uri;
       String queryParamet = uri.getQueryParameter("fcc_match_query");
       uri = (!TextUtils.isEmpty(queryParamet))? uri.buildUpon().clearQuery().appendQueryParameter(queryParamet, uri.getQueryParameter(queryParamet)).toString(): uri.buildUpon().clearQuery().toString();
       if (vpw.commonConfig.f2 != null) {
          if ((ojrd = jpw.c().a(jrd.class)) != null) {
             uri = ojrd.a()+"_"+uri;
          }
          lcn.f(RVLLevel.Info, "WindVane/NetworkSSRCache", "snapshot storage key: "+uri);
       }
       this.mFccStrategy = new FccStrategy();
       this.mStorageKey = uri;
       return;
    }
    public static String[] access$000(FirstChunkStorage p0,String[] p1){
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.sortRules(p1);
       }
       Object[] objArray = new Object[]{p0,p1};
       return $ipChange.ipc$dispatch("18bb8b2b", objArray);
    }
    private FirstChunkStorage$MetaItemResult chooseOneFromCandidates(List p0){
       int i = 1;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("1ba252f2", objArray);
       }else if(p0 != null && !p0.isEmpty()){
          if (p0.size() > i) {
             Collections.sort(p0, new FirstChunkStorage$1(this));
          }
          if (!p0.isEmpty()) {
             return p0.get(0);
          }
       }
       return null;
    }
    private String getHtmlByKey(String p0){
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("76cb7568", objArray);
       }else if(TextUtils.isEmpty(p0)){
          return null;
       }else {
          return FirstChunkStorage.HTML_STORAGE.read(p0);
       }
    }
    private FirstChunkStorage$HtmlStorageResult getHtmlByStrategy(FccStrategyType p0,JSONObject p1){
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("507a02de", objArray);
       }else {
          String str = p1.getString("fcc-html-cache");
          String str1 = p1.getString("fcc-html-snapshot");
          int intValue = p1.getIntValue("html-byte-size-cache");
          int intValue1 = p1.getIntValue("html-byte-size-snapshot");
          if (p0 == FccStrategyType.CACHE) {
             return new FirstChunkStorage$HtmlStorageResult(FccStorageType.CACHE, this.getHtmlByKey(str), intValue, str);
          }
          if (p0 == FccStrategyType.SNAPSHOT) {
             return new FirstChunkStorage$HtmlStorageResult(FccStorageType.SNAPSHOT, this.getHtmlByKey(str1), intValue1, str1);
          }
          if (p0 == FccStrategyType.LEGACY) {
             return new FirstChunkStorage$HtmlStorageResult(this.getStorageTypeFromMeta(p1), p1.getString("fcc-html"), p1.getIntValue("html-byte-size"), "fcc-html");
          }
          String htmlByKey = this.getHtmlByKey(str1);
          if (!TextUtils.isEmpty(htmlByKey)) {
             return new FirstChunkStorage$HtmlStorageResult(FccStorageType.SNAPSHOT, htmlByKey, intValue1, str1);
          }
          return new FirstChunkStorage$HtmlStorageResult(FccStorageType.CACHE, this.getHtmlByKey(str), intValue, str);
       }
    }
    private FccStorageType getStorageTypeFromMeta(JSONObject p0){
       FccStorageType sNAPSHOT;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("e6ccc8a4", objArray);
       }else if(p0.getIntValue("cache-priority") == 1){
          sNAPSHOT = FccStorageType.SNAPSHOT;
       }else {
          sNAPSHOT = FccStorageType.CACHE;
       }
       return sNAPSHOT;
    }
    private boolean inWhiteList(String p0,String p1){
       Object[] objArray1;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("995398e1", objArray).booleanValue();
       }else if(p0 != null && p1 != null){
          String[] stringArray = p1.split(",");
          int len = stringArray.length;
          int i = 0;
          while (i < len) {
             if (p0.contains(stringArray[i])) {
                return 1;
             }
             i = i + 1;
          }
       }
    }
    private boolean isValidResponseContext(ResponseContext p0){
       int i = 0;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6ec16aab", objArray).booleanValue();
       }else if(p0 == null){
          return i;
       }else if(TextUtils.isEmpty(p0.getHtml())){
          return i;
       }else if(p0.getHtmlLength() > 0 && p0.getHtml().length() != p0.getHtmlLength()){
          return i;
       }else if(vpw.commonConfig.f3 != null && p0.isHtmlEmpty()){
          return i;
       }else {
          return p0.isEnable();
       }
    }
    private String makeHtmlStorageKey(String p0,String p1,FccStorageType p2){
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mStorageKey+"_"+p0+"_"+p1+"_"+p2.name;
       }
       Object[] objArray = new Object[]{this,p0,p1,p2};
       return $ipChange.ipc$dispatch("a51a027f", objArray);
    }
    private String makeQueryRuleValueKey(String p0,Uri p1){
       int i = 0;
       String str = ",";
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("c89f376e", objArray);
       }else {
          try{
             String[] stringArray = this.sortRules(p0.split(str));
             StringBuilder str1 = "";
             int len = stringArray.length;
             while (i < len) {
                object oobject = stringArray[i];
                String queryParamet = p1.getQueryParameter(oobject);
                if (!TextUtils.isEmpty(oobject) && queryParamet != null) {
                   str1 = str1.append(oobject).append("=").append(queryParamet).append(str);
                }
                i = i + 1;
             }
             return str1;
          }catch(java.lang.Exception e0){
             return "";
          }
       }
    }
    private synchronized ResponseContext readInternal(){
       FirstChunkStorage$MetaItemResult metaItemResu;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("b07f854", objArray);
       }else if((metaItemResu = this.readMetaItemInfo()) == null){
          return null;
       }else {
          return this.readResponseContext(metaItemResu);
       }
    }
    private JSONObject readMetaContext(){
       int i = 1;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          return $ipChange.ipc$dispatch("2e15961b", objArray);
       }else {
          String str = FirstChunkStorage.META_STORAGE.read(this.mStorageKey);
          if (TextUtils.isEmpty(str)) {
             return null;
          }
          try{
             JSONObject jSONObject = JSON.parseObject(str);
             if (vpw.commonConfig.o3 != null && this.reportMetaSizeFlag == null) {
                JSONObject jSONObject1 = new JSONObject();
                jSONObject1.put("metaSize", Integer.valueOf(str.length()));
                jSONObject1.put("url", this.mUrl);
                if (jSONObject != null) {
                   jSONObject1.put("keyCount", Integer.valueOf(jSONObject1.size()));
                }
                y71.commitSuccess("firstChunkMetaSize", jSONObject1);
                this.reportMetaSizeFlag = i;
             }
             return jSONObject;
          }catch(java.lang.Exception e0){
          }
       }
    }
    private FirstChunkStorage$MetaItemResult readMetaItemInfo(){
       JSONObject jSONObject;
       Map$Entry uEntry;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("1c32c430", objArray);
       }else if((jSONObject = this.readMetaContext()) == null){
          return null;
       }else {
          LinkedList linkedList = new LinkedList();
          Iterator iterator = jSONObject.entrySet().iterator();
          while (iterator.hasNext()) {
             if ((uEntry = iterator.next()) != null) {
                String key = uEntry.getKey();
                Object value = uEntry.getValue();
                if (value instanceof JSONObject) {
                   try{
                      String str = value.getString("fcc-url-query-rule");
                      String str1 = this.makeQueryRuleValueKey(str, this.mUri);
                      if (vpw.commonConfig.n3 != null && TextUtils.equals(key, str1)) {
                         linkedList.add(new FirstChunkStorage$MetaItemResult(key, value));
                      }else if(TextUtils.equals(key, str)){
                         String str2 = value.getString("fcc-url");
                         if (!TextUtils.isEmpty(str2) && TextUtils.equals(str1, this.makeQueryRuleValueKey(str, Uri.parse(str2)))) {
                            linkedList.add(new FirstChunkStorage$MetaItemResult(key, value));
                         }
                      }
                   }catch(java.lang.Exception e2){
                      lcn.a(RVLLevel.Warn, "WindVane/NetworkSSRCache").j("readMetaItemError").a("storageKey", this.mStorageKey).a("key", key).a("msg", e2.getMessage()).f();
                   }
                }
             }
          }
          return this.chooseOneFromCandidates(linkedList);
       }
    }
    private ResponseContext readResponseContext(FirstChunkStorage$MetaItemResult p0){
       FirstChunkStorage$MetaItemResult meatItem;
       icn oicn;
       object oobject = this;
       object oobject1 = p0;
       int i = 0;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,oobject1};
          return $ipChange.ipc$dispatch("730f596", objArray);
       }else if((meatItem = oobject1.meatItem) == null){
          return null;
       }else {
          ResponseContext responseCont = new ResponseContext();
          responseCont.setRule(meatItem.getString("fcc-url-query-rule"));
          responseCont.setVersion(meatItem.getString("fcc-version"));
          responseCont.setUrl(meatItem.getString("fcc-url"));
          responseCont.setExpiredTime(meatItem.getLongValue("fcc-expire-time"));
          responseCont.setEnable(meatItem.getBooleanValue("fcc-enable"));
          responseCont.setPriority(meatItem.getIntValue("cache-priority"));
          String str = "type";
          String str1 = "read";
          if (vpw.commonConfig.n3 != null) {
             responseCont.setStrategyCache(meatItem.getString("fcc-strategy-cache"));
             FirstChunkStorage$HtmlStorageResult htmlByStrate = (i = oobject.mFccStrategy.useFccStrategyCache(responseCont.getStrategyCache()))? oobject.getHtmlByStrategy(FccStrategyType.CACHE, meatItem): oobject.getHtmlByStrategy(FccStrategyType.DEFAULT, meatItem);
             if (htmlByStrate.isEmpty()) {
                htmlByStrate = oobject.getHtmlByStrategy(FccStrategyType.LEGACY, meatItem);
             }
             responseCont.setStorageType(htmlByStrate.type);
             responseCont.setHtml(htmlByStrate.html);
             responseCont.setHtmlLength(htmlByStrate.htmlLength);
             oicn = lcn.a(RVLLevel.Info, "WindVane/NetworkSSRCache").j(str1).a(str, htmlByStrate.type.name).a("strategyCacheResult", Boolean.valueOf(i)).a("strategyCache", responseCont.getStrategyCache()).a("storageKey", oobject.mStorageKey).a("metaKey", oobject1.metaKey).a("htmlKey", htmlByStrate.htmlKey).a("rule", responseCont.getRule()).a("length", Integer.valueOf(responseCont.getHtmlLength()));
             i = (responseCont.getHtml() != null)? responseCont.getHtml().length(): 0;
             oicn.a("size", Integer.valueOf(i)).f();
          }else {
             String str2 = "fcc-html";
             responseCont.setHtml(meatItem.getString(str2));
             responseCont.setHtmlLength(meatItem.getIntValue("html-byte-size"));
             responseCont.setStorageType(oobject.getStorageTypeFromMeta(meatItem));
             icn oicn1 = lcn.a(RVLLevel.Info, "WindVane/NetworkSSRCache").j(str1);
             FccStorageType name = (responseCont.getStorageType() != null)? responseCont.getStorageType().name: "";
             oicn = oicn1.a(str, name).a("storageKey", oobject.mStorageKey).a("metaKey", oobject1.metaKey).a("htmlKey", str2).a("rule", responseCont.getRule()).a("length", Integer.valueOf(responseCont.getHtmlLength()));
             i = (responseCont.getHtml() != null)? responseCont.getHtml().length(): 0;
             oicn.a("size", Integer.valueOf(i)).f();
          }
          if (!oobject.isValidResponseContext(responseCont)) {
             lcn.f(RVLLevel.Error, "WindVane/NetworkSSRCache", "read invalid ssr response context.");
             return null;
          }else {
             return responseCont;
          }
       }
    }
    private void removeAllMetaItemInfo(){
       JSONObject jSONObject;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("d26b3daa", objArray);
          return;
       }else if((jSONObject = this.readMetaContext()) != null){
          Iterator iterator = jSONObject.entrySet().iterator();
          while (iterator.hasNext()) {
             Object value = iterator.next().getValue();
             if (value instanceof JSONObject) {
                this.removeMetaItemHtml(value);
             }
          }
       }
       return;
    }
    private boolean removeHtmlByKey(String p0){
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("b9cfa74e", objArray).booleanValue();
       }else {
          lcn.a(RVLLevel.Info, "WindVane/NetworkSSRCache").j("removeHtml").a("htmlKey", p0).f();
          return FirstChunkStorage.HTML_STORAGE.remove(p0);
       }
    }
    private void removeMetaItemHtml(JSONObject p0){
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("2d329e82", objArray);
          return;
       }else {
          String str = p0.getString("fcc-html-snapshot");
          if (!TextUtils.isEmpty(str)) {
             this.removeHtmlByKey(str);
          }
          String str1 = p0.getString("fcc-html-cache");
          if (!TextUtils.isEmpty(str1)) {
             this.removeHtmlByKey(str1);
          }
          return;
       }
    }
    private boolean saveHtmlByKey(String p0,String p1){
       int i = 0;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("b8fa1ebf", objArray).booleanValue();
       }else {
          icn oicn = lcn.a(RVLLevel.Info, "WindVane/NetworkSSRCache").j("writeHtml").a("htmlKey", p0);
          if (p1 != null) {
             i = p1.length();
          }
          oicn.a("size", Integer.valueOf(i)).f();
          return FirstChunkStorage.HTML_STORAGE.write(p0, p1);
       }
    }
    private String[] sortRules(String[] p0){
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("11d4592e", objArray);
       }else {
          Arrays.sort(p0);
          return p0;
       }
    }
    public ResponseContext read(){
       FirstChunkStorage tmUrl;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("457501b7", objArray);
       }else if((tmUrl = this.mUrl) != null && tmUrl.contains("disableFcc=true")){
          return null;
       }else if(this.shouldDisableSnapshot()){
          return null;
       }else {
          return this.readInternal();
       }
    }
    public synchronized void remove(){
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("41689b0b", objArray);
          return;
       }else {
          this.removeAllMetaItemInfo();
          FirstChunkStorage.META_STORAGE.remove(this.mStorageKey);
          lcn.a(RVLLevel.Info, "WindVane/NetworkSSRCache").j("remove").a("storageKey", this.mStorageKey).f();
          return;
       }
    }
    public boolean shouldDisableSnapshot(){
       int i2;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("c5d3aaa4", objArray).booleanValue();
       }else {
          wpw commonConfig = vpw.commonConfig;
          wpw b3 = commonConfig.b3;
          if (TextUtils.isEmpty(b3)) {
             return i;
          }
          if (!this.inWhiteList(this.mUrl, b3)) {
             return i;
          }
          if ((i2 = DeviceUtils.a()) < 0) {
             return i;
          }
          if (yaa.n == null) {
             return i;
          }
          if (!oba.g("wvEnableSkipSnapshot")) {
             return i;
          }
          if (i2 <= commonConfig.c3) {
             return i1;
          }
          if (commonConfig.d3 != null && WVUCWebView.sWebViewFirstAttached) {
             return i1;
          }
          return i;
       }
    }
    public synchronized boolean write(ResponseContext p0){
       HashMap obj;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = FirstChunkStorage.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("fdde2bc8", objArray).booleanValue();
       }else if(p0 == null){
          lcn.f(RVLLevel.Error, "WindVane/NetworkSSRCache", "responseContext = null");
          return i1;
       }else if(TextUtils.isEmpty(p0.getHtml())){
          lcn.f(RVLLevel.Error, "WindVane/NetworkSSRCache", "responseContext.html is empty");
          return i1;
       }else if(p0.getHtmlLength() > 0 && p0.getHtml().length() != p0.getHtmlLength()){
          lcn.f(RVLLevel.Error, "WindVane/NetworkSSRCache", "responseContext.html length is unexpected.");
          return i1;
       }else {
          wpw commonConfig = vpw.commonConfig;
          int i2 = (commonConfig.e3 != null && p0.isNoStorageCache())? 1: 0;
          wpw n3 = commonConfig.n3;
          String str = this.makeQueryRuleValueKey(p0.getRule(), this.mUri);
          JSONObject jSONObject = null;
          JSONObject jSONObject1 = (n3 == null && (!i2 && commonConfig.w2 != null))? jSONObject: this.readMetaContext();
          if (jSONObject1 == null) {
             jSONObject1 = new JSONObject();
          }
          if (n3 != null) {
             obj = jSONObject1.get(str);
             if (obj instanceof JSONObject) {
                jSONObject = obj;
             }
             if (jSONObject == null) {
                obj = jSONObject1.get(p0.getRule());
                if (obj instanceof JSONObject) {
                   if (!TextUtils.isEmpty(p0.getRule())) {
                      jSONObject1.remove(p0.getRule());
                   }
                   jSONObject = obj;
                }
             }
          }else if(i2){
             Object obj2 = jSONObject1.get(p0.getRule());
             if (obj2 instanceof JSONObject) {
                jSONObject = obj2;
             }
          }
          i = i ^ i2;
          obj = new HashMap();
          if (p0.isEnable()) {
             FccStorageType storageType = p0.getStorageType();
             if (jSONObject == null) {
                jSONObject = new JSONObject();
             }
             jSONObject.put("fcc-enable", Boolean.valueOf(p0.isEnable()));
             jSONObject.put("fcc-strategy-cache", p0.getStrategyCache());
             jSONObject.put("fcc-url-query-rule", p0.getRule());
             jSONObject.put("fcc-version", p0.getVersion());
             jSONObject.put("fcc-url", p0.getUrl());
             jSONObject.put("fcc-expire-time", Long.valueOf(p0.getExpiredTime()));
             jSONObject.put("cache-priority", Integer.valueOf(p0.getPriority()));
             jSONObject.put("last-mdf-time", Long.valueOf(System.currentTimeMillis()));
             if (n3 != null) {
                if (!i2) {
                   FccStorageType cACHE = FccStorageType.CACHE;
                   String str1 = (storageType == cACHE)? "html-byte-size-cache": "html-byte-size-snapshot";
                   jSONObject.put(str1, Integer.valueOf(p0.getHtmlLength()));
                   str1 = this.makeHtmlStorageKey(p0.getVersion(), str, storageType);
                   obj.put("htmlKey", str1);
                   String str2 = (storageType == cACHE)? "fcc-html-cache": "fcc-html-snapshot";
                   jSONObject.put(str2, str1);
                   this.saveHtmlByKey(str1, p0.getHtml());
                   jSONObject.remove("fcc-html");
                }
                jSONObject1.put(str, jSONObject);
                obj.put("metaKey", str);
             }else if(!i2){
                jSONObject.put("html-byte-size", Integer.valueOf(p0.getHtmlLength()));
                jSONObject.put("fcc-html", p0.getHtml());
                obj.put("htmlKey", "fcc-html");
             }
             jSONObject1.put(p0.getRule(), jSONObject);
             obj.put("metaKey", p0.getRule());
          }else if(n3 != null){
             Object obj1 = jSONObject1.remove(str);
             if (obj1 instanceof JSONObject) {
                this.removeMetaItemHtml(obj1);
             }
          }else {
             jSONObject1.remove(p0.getRule());
          }
          icn oicn = lcn.a(RVLLevel.Info, "WindVane/NetworkSSRCache");
          icn oicn1 = oicn.j("write");
          str = "type";
          FccStorageType name = (p0.getStorageType() != null)? p0.getStorageType().name: "";
          oicn1 = oicn1.a(str, name).a("enable", Boolean.valueOf(p0.isEnable())).a("rule", p0.getRule());
          str = "size";
          if (p0.getHtml() != null) {
             i1 = p0.getHtml().length();
          }
          oicn1.a(str, Integer.valueOf(i1)).a("length", Integer.valueOf(p0.getHtmlLength())).a("version", p0.getVersion()).a("strategyCache", p0.getStrategyCache()).a("storageKey", this.mStorageKey).a("expireTime", Long.valueOf(p0.getExpiredTime())).a("storageHtml", Boolean.valueOf(i));
          Iterator iterator = obj.entrySet().iterator();
          while (iterator.hasNext()) {
             Map$Entry uEntry = iterator.next();
             String key = uEntry.getKey();
             oicn.a(key, uEntry.getValue());
          }
          oicn.f();
          return FirstChunkStorage.META_STORAGE.write(this.mStorageKey, jSONObject1.toJSONString());
       }
    }
}
