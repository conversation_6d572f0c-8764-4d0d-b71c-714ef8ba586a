package android.taobao.windvane.extra.storage.ResponseContext;
import tb.t2o;
import java.lang.Object;
import android.taobao.windvane.extra.storage.FccStorageType;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Number;
import java.lang.System;
import tb.vpw;
import tb.wpw;
import java.lang.Boolean;
import java.lang.Long;
import java.lang.Integer;

public class ResponseContext	// class@0001eb from classes.dex
{
    private boolean enable;
    private long expiredTime;
    private String html;
    private int htmlLength;
    private boolean noStorageCache;
    private int priority;
    private String rule;
    private FccStorageType storageType;
    private String strategyCache;
    private String url;
    private String version;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800120);
    }
    public void ResponseContext(){
       super();
       this.enable = false;
       this.rule = "";
       this.version = "0";
       this.expiredTime = 0;
       this.storageType = FccStorageType.CACHE;
       this.noStorageCache = false;
       this.priority = 0;
    }
    public static long getDefaultExpiredTime(){
       IpChange $ipChange = ResponseContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return (System.currentTimeMillis() + ((long)(vpw.commonConfig.q2 * 0x15180) * 1000));
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("f5a9ad82", objArray).longValue();
    }
    public long getExpiredTime(){
       IpChange $ipChange = ResponseContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.expiredTime;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("da9b90d7", objArray).longValue();
    }
    public String getHtml(){
       ResponseContext thtml;
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("c4adcd78", objArray);
       }else if((thtml = this.html) == null){
          thtml = "";
       }
       return thtml;
    }
    public int getHtmlLength(){
       IpChange $ipChange = ResponseContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.htmlLength;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("97213361", objArray).intValue();
    }
    public int getPriority(){
       IpChange $ipChange = ResponseContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.priority;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("49b31e94", objArray).intValue();
    }
    public String getRule(){
       ResponseContext trule;
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("f2b8aac7", objArray);
       }else if((trule = this.rule) == null){
          trule = "";
       }
       return trule;
    }
    public FccStorageType getStorageType(){
       IpChange $ipChange = ResponseContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.storageType;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("966c6459", objArray);
    }
    public String getStrategyCache(){
       IpChange $ipChange = ResponseContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.strategyCache;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("53966080", objArray);
    }
    public String getUrl(){
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("de8f0660", objArray);
       }else if(this.html == null){
          return "";
       }else {
          return this.url;
       }
    }
    public String getVersion(){
       ResponseContext tversion;
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("2a8fef97", objArray);
       }else if((tversion = this.version) == null){
          tversion = "0";
       }
       return tversion;
    }
    public boolean isEnable(){
       IpChange $ipChange = ResponseContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.enable;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("f2312d58", objArray).booleanValue();
    }
    public boolean isExpired(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("a65eada6", objArray).booleanValue();
       }else if((System.currentTimeMillis() - this.expiredTime) > 0){
          i = true;
       }
       return i;
    }
    public boolean isHtmlEmpty(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("7a117ae3", objArray).booleanValue();
       }else if(!this.getHtmlLength()){
          i = true;
       }
       return i;
    }
    public boolean isNoStorageCache(){
       IpChange $ipChange = ResponseContext.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.noStorageCache;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("cbed5d7d", objArray).booleanValue();
    }
    public void setEnable(boolean p0){
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("183c4dc8", objArray);
          return;
       }else {
          this.enable = p0;
          return;
       }
    }
    public void setExpiredTime(long p0){
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Long(p0)};
          $ipChange.ipc$dispatch("1cd3974d", objArray);
          return;
       }else {
          this.expiredTime = p0;
          return;
       }
    }
    public void setHtml(String p0){
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("658b21fe", objArray);
          return;
       }else {
          this.html = p0;
          return;
       }
    }
    public void setHtmlLength(int p0){
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("83dc46c9", objArray);
          return;
       }else {
          this.htmlLength = p0;
          return;
       }
    }
    public void setNoStorageCache(boolean p0){
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0)};
          $ipChange.ipc$dispatch("d50fda43", objArray);
          return;
       }else {
          this.noStorageCache = p0;
          return;
       }
    }
    public void setPriority(int p0){
       int i = 0;
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("7f878ef6", objArray);
          return;
       }else if(p0 < 0){
          p0 = 0;
       }
       this.priority = p0;
       return;
    }
    public void setRule(String p0){
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f8dbee8f", objArray);
          return;
       }else {
          this.rule = p0;
          return;
       }
    }
    public void setStorageType(FccStorageType p0){
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("a23061c3", objArray);
          return;
       }else {
          this.storageType = p0;
          return;
       }
    }
    public void setStrategyCache(String p0){
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("53d5915e", objArray);
          return;
       }else {
          this.strategyCache = p0;
          return;
       }
    }
    public void setUrl(String p0){
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e1dea87e", objArray);
          return;
       }else {
          this.url = p0;
          return;
       }
    }
    public void setVersion(String p0){
       IpChange $ipChange = ResponseContext.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("2e718c27", objArray);
          return;
       }else {
          this.version = p0;
          return;
       }
    }
}
