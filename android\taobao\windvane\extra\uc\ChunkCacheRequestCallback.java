package android.taobao.windvane.extra.uc.ChunkCacheRequestCallback;
import tb.mnf;
import java.lang.Object;
import tb.bgq;
import tb.egq;
import java.util.Map;

public interface abstract ChunkCacheRequestCallback implements mnf	// class@00020b from classes.dex
{

    void onCustomCallback(int p0,Object[] p1);
    void onError(bgq p0,egq p1);
    void onFinish(bgq p0);
    void onNetworkResponse(int p0,Map p1);
    void onReceiveData(bgq p0,byte[] p1);
    void onResponse(bgq p0,int p1,Map p2);
}
