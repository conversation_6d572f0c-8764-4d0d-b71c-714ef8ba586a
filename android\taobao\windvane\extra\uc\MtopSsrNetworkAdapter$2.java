package android.taobao.windvane.extra.uc.MtopSsrNetworkAdapter$2;
import android.taobao.windvane.extra.uc.ChunkCacheRequestCallback;
import android.taobao.windvane.extra.uc.MtopSsrNetworkAdapter;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import android.taobao.windvane.extra.uc.MtopSsrNetworkAdapter$MtopRequestListener;
import android.taobao.windvane.extra.uc.interfaces.IRequest;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import tb.bgq;
import tb.egq;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import tb.y71;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;
import android.taobao.windvane.extra.uc.MTopSSRRequest;
import android.taobao.windvane.extra.uc.interfaces.INetwork;
import tb.icn;
import java.util.Map;
import java.util.List;

public class MtopSsrNetworkAdapter$2 implements ChunkCacheRequestCallback	// class@000217 from classes.dex
{
    private long mTotalSize;
    public final MtopSsrNetworkAdapter this$0;
    public final EventHandler val$eventHandler;
    public final MtopSsrNetworkAdapter$MtopRequestListener val$mtopRequestListener;
    public final IRequest val$request;
    public final long val$requestId;
    public static IpChange $ipChange;

    public void MtopSsrNetworkAdapter$2(MtopSsrNetworkAdapter p0,EventHandler p1,MtopSsrNetworkAdapter$MtopRequestListener p2,long p3,IRequest p4){
       this.this$0 = p0;
       this.val$eventHandler = p1;
       this.val$mtopRequestListener = p2;
       this.val$requestId = p3;
       this.val$request = p4;
       super();
       this.mTotalSize = 0;
    }
    public void onCustomCallback(int p0,Object[] p1){
       IpChange $ipChange = MtopSsrNetworkAdapter$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("c344bdea", objArray);
       }
       return;
    }
    public void onError(bgq p0,egq p1){
       MtopSsrNetworkAdapter$2 tval$mtopReq;
       IpChange $ipChange = MtopSsrNetworkAdapter$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("7a95834", objArray);
          return;
       }else if(p0 != null && p1 != null){
          egq a = p1.a;
          String str = "url";
          if (a == 417) {
             JSONObject jSONObject = new JSONObject();
             jSONObject.put(str, p0.a);
             y71.commitSuccess("mtopSSRDowngrade", jSONObject.toJSONString());
             lcn.f(RVLLevel.Error, "WindVane/NetworkSSR", "downgrade network to normal request");
             MtopSsrNetworkAdapter.access$100(this.this$0).sendRequest(this.val$request.copyRequest(MtopSsrNetworkAdapter.access$100(this.this$0)));
             return;
          }else {
             this.val$eventHandler.error(a, p1.c);
             lcn.a(RVLLevel.Error, "WindVane/NetworkSSR").j("ssr request").a(str, p0.a).g(p1.a, p1.c).f();
             if ((tval$mtopReq = this.val$mtopRequestListener) != null) {
                tval$mtopReq.onFinish(this.val$requestId, p0.a, this.mTotalSize, false);
             }
          }
       }
       return;
    }
    public void onFinish(bgq p0){
       MtopSsrNetworkAdapter$2 tval$mtopReq;
       IpChange $ipChange = MtopSsrNetworkAdapter$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4d53f97", objArray);
          return;
       }else {
          this.val$eventHandler.endData();
          if ((tval$mtopReq = this.val$mtopRequestListener) != null && p0 != null) {
             tval$mtopReq.onFinish(this.val$requestId, p0.a, this.mTotalSize, true);
          }
          return;
       }
    }
    public void onNetworkResponse(int p0,Map p1){
       IpChange $ipChange = MtopSsrNetworkAdapter$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("92d71559", objArray);
       }
       return;
    }
    public void onReceiveData(bgq p0,byte[] p1){
       IpChange $ipChange = MtopSsrNetworkAdapter$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("150b5e1a", objArray);
          return;
       }else {
          this.val$eventHandler.data(p1, p1.length);
          this.mTotalSize = this.mTotalSize + (long)p1.length;
          return;
       }
    }
    public void onResponse(bgq p0,int p1,Map p2){
       MtopSsrNetworkAdapter$2 tval$mtopReq;
       int i2;
       int i = 2;
       int i1 = 0;
       IpChange $ipChange = MtopSsrNetworkAdapter$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
          $ipChange.ipc$dispatch("1e09d3a7", objArray);
          return;
       }else {
          String str = "x-protocol";
          String str1 = "";
          if (p2.containsKey(str) && p2.get(str).size()) {
             str = p2.get(str).get(i1);
             if (!str.equals("http") && !str.equals("https")) {
                this.val$eventHandler.status(i, i1, p1, str1);
             }else {
                this.val$eventHandler.status(i1, i1, p1, str1);
             }
          }else if(p2.containsKey(":status")){
             this.val$eventHandler.status(i, i1, p1, str1);
          }else {
             this.val$eventHandler.status(i1, i1, p1, str1);
          }
          this.val$eventHandler.headers(p2);
          if ((tval$mtopReq = this.val$mtopRequestListener) != null) {
             p0 = (p0 != null)? p0.a: 0;
             bgq uobgq = p0;
             tval$mtopReq.onResponse(this.val$requestId, uobgq, p1, p2);
          }
          return;
       }
    }
}
