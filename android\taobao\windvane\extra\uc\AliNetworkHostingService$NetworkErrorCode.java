package android.taobao.windvane.extra.uc.AliNetworkHostingService$NetworkErrorCode;

public interface abstract AliNetworkHostingService$NetworkErrorCode	// class@000203 from classes.dex
{
    public static final int ERROR_LOOKUP = 254;
    public static final int READ_ERROR_ILLEGAL_STATE = 211;
    public static final int READ_ERROR_IO = 212;
    public static final int READ_ERROR_PARSE = 213;
    public static final int READ_ERROR_SOCKET_ERROR = 209;
    public static final int READ_ERROR_SOCKET_TIMEOUT = 210;

}
