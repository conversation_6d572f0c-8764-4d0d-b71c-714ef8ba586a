package android.taobao.windvane.extra.uc.WVUCWebView$4;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import java.lang.Long;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.taobao.android.riverlogger.RVLLevel;
import java.lang.StringBuilder;
import tb.lcn;

public class WVUCWebView$4 implements ValueCallback	// class@000256 from classes.dex
{
    public final WVUCWebView this$0;
    public static IpChange $ipChange;

    public void WVUCWebView$4(WVUCWebView p0){
       this.this$0 = p0;
       super();
    }
    public void onReceiveValue(Long p0){
       IpChange $ipChange = WVUCWebView$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("2bfdf613", objArray);
          return;
       }else {
          lcn.f(RVLLevel.Info, "WindVane/Cache", "GetHttpCacheSize:"+p0);
          return;
       }
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
}
