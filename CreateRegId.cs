using System;
using System.Text;

public class CreateRegId
{
    private static readonly Random PaddingRandom = new Random();
    private static readonly string Base62Chars = "0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ";

    /// <summary>
    /// 创建注册ID，与Java版本完全一致
    /// </summary>
    /// <returns>格式为 "reg0" + 编码值的注册ID</returns>
    public static string CreateRegIdString()
    {
        string str = "reg0";
        
        // 第一部分：编码时间戳相关的随机值
        long timestampValue = PaddingRandom.Next(int.MaxValue) + 0x176b986fc01L;
        string part1 = LeftPadding(EncodeBase62(timestampValue), true, 14);
        
        // 第二部分：编码随机IP地址
        string randomIp = $"{PaddingRandom.Next(256)}.{PaddingRandom.Next(256)}.{PaddingRandom.Next(256)}.{PaddingRandom.Next(256)}";
        long ipLong = IpToLong(randomIp);
        string ipEncoded = LeftPadding(EncodeBase62(ipLong), false, 6);
        string part2 = LeftPadding(ipEncoded, true, 12);
        
        // 第三部分：编码随机值
        long randomValue = PaddingRandom.Next(3844);
        string part3 = LeftPadding(EncodeBase62(randomValue), false, 2);
        
        return str + part1 + part2 + part3;
    }

    /// <summary>
    /// 左填充字符串
    /// </summary>
    /// <param name="input">输入字符串</param>
    /// <param name="useRandomChars">是否使用随机字符填充（true=使用Base62字符，false=使用'0'）</param>
    /// <param name="targetLength">目标长度</param>
    /// <returns>填充后的字符串</returns>
    private static string LeftPadding(string input, bool useRandomChars, int targetLength)
    {
        if (string.IsNullOrEmpty(input))
        {
            input = "";
        }
        
        int currentLength = input.Length;
        if (currentLength >= targetLength)
        {
            return input;
        }
        
        int paddingLength = targetLength - currentLength;
        StringBuilder padding = new StringBuilder();
        
        for (int i = 0; i < paddingLength; i++)
        {
            char paddingChar;
            if (useRandomChars)
            {
                paddingChar = Base62Chars[PaddingRandom.Next(62)];
            }
            else
            {
                paddingChar = '0';
            }
            padding.Append(paddingChar);
        }
        
        return padding.ToString() + input;
    }

    /// <summary>
    /// 将IP地址字符串转换为长整型
    /// </summary>
    /// <param name="ip">IP地址字符串，格式如 "***********"</param>
    /// <returns>IP地址对应的长整型值</returns>
    private static long IpToLong(string ip)
    {
        string[] parts = ip.Split('.');
        if (parts.Length != 4)
        {
            return 0;
        }
        
        try
        {
            long result = 0;
            result |= (long.Parse(parts[0]) & 255L) << 24;
            result |= (long.Parse(parts[1]) & 255L) << 16;
            result |= (long.Parse(parts[2]) & 255L) << 8;
            result |= (long.Parse(parts[3]) & 255L);
            
            return result;
        }
        catch
        {
            return 0;
        }
    }

    /// <summary>
    /// 将长整型编码为Base62字符串（完全模拟sp1.a方法）
    /// </summary>
    /// <param name="value">要编码的长整型值</param>
    /// <returns>Base62编码的字符串</returns>
    private static string EncodeBase62(long value)
    {
        if (value <= 0)
        {
            return "0";
        }

        StringBuilder str = new StringBuilder();

        while (value > 0)
        {
            int remainder = (int)(value % 62);
            str.Append(Base62Chars[remainder]);
            value = value / 62;
        }

        // 反转字符串，完全模拟Java的str.reverse()
        char[] chars = str.ToString().ToCharArray();
        Array.Reverse(chars);
        return new string(chars);
    }
}

// 使用示例
public class Program
{
    public static void Main()
    {
        // 生成注册ID
        string regId = CreateRegId.CreateRegIdString();
        Console.WriteLine($"Generated RegId: {regId}");
        
        // 生成多个示例
        for (int i = 0; i < 5; i++)
        {
            Console.WriteLine($"RegId {i + 1}: {CreateRegId.CreateRegIdString()}");
        }
    }
}
