package android.taobao.windvane.extra.uc.FirstTruckCacheSSRService$1;
import tb.mnf;
import android.taobao.windvane.extra.uc.FirstTruckCacheSSRService;
import android.taobao.windvane.extra.storage.ResponseContext;
import java.lang.String;
import java.util.concurrent.atomic.AtomicBoolean;
import android.taobao.windvane.extra.storage.FirstChunkStorage;
import java.io.ByteArrayOutputStream;
import java.util.concurrent.atomic.AtomicInteger;
import java.lang.Object;
import tb.bgq;
import tb.egq;
import com.android.alibaba.ip.runtime.IpChange;
import java.util.Arrays;
import java.io.OutputStream;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;
import java.nio.charset.StandardCharsets;
import java.nio.charset.Charset;
import java.lang.StringBuilder;
import java.lang.Throwable;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.icn;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.extra.uc.ChunkCacheRequestCallback;
import android.taobao.windvane.extra.storage.FccStorageType;
import android.os.SystemClock;
import java.lang.Integer;
import java.lang.Long;
import java.util.Map;
import java.lang.Boolean;

public class FirstTruckCacheSSRService$1 implements mnf	// class@000212 from classes.dex
{
    public final FirstTruckCacheSSRService this$0;
    public final ByteArrayOutputStream val$byteArrayOutputStream;
    public final ResponseContext val$cachedResponseContext;
    public final long val$firstChunkCacheSize;
    public final String val$firstChunkCacheType;
    public final FirstChunkStorage val$firstChunkStorage;
    public final AtomicBoolean val$hasSkippedCachedTrunk;
    public final boolean val$isExpired;
    public final long val$loadChunkCost;
    public final AtomicBoolean val$needInvalidatingCache;
    public final AtomicInteger val$offset;
    public final mnf val$requestCallback;
    public final ResponseContext val$responseContext;
    public final boolean val$useFirstChunkCache;
    public static final boolean $assertionsDisabled;
    public static IpChange $ipChange;

    public void FirstTruckCacheSSRService$1(FirstTruckCacheSSRService p0,ResponseContext p1,boolean p2,ResponseContext p3,boolean p4,long p5,mnf p6,long p7,String p8,AtomicBoolean p9,FirstChunkStorage p10,AtomicBoolean p11,ByteArrayOutputStream p12,AtomicInteger p13){
       int i = this;
       i.this$0 = p0;
       i.val$responseContext = p1;
       i.val$useFirstChunkCache = p2;
       i.val$cachedResponseContext = p3;
       i.val$isExpired = p4;
       i.val$loadChunkCost = p5;
       i.val$requestCallback = p6;
       i.val$firstChunkCacheSize = p7;
       i.val$firstChunkCacheType = p8;
       i.val$needInvalidatingCache = p9;
       i.val$firstChunkStorage = p10;
       i.val$hasSkippedCachedTrunk = p11;
       i.val$byteArrayOutputStream = p12;
       i.val$offset = p13;
       super();
    }
    public void onError(bgq p0,egq p1){
       FirstTruckCacheSSRService$1 tval$request;
       IpChange $ipChange = FirstTruckCacheSSRService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("7a95834", objArray);
          return;
       }else if((tval$request = this.val$requestCallback) != null){
          tval$request.onError(p0, p1);
       }
       return;
    }
    public void onFinish(bgq p0){
       FirstTruckCacheSSRService$1 tval$request;
       IpChange $ipChange = FirstTruckCacheSSRService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4d53f97", objArray);
          return;
       }else if((tval$request = this.val$requestCallback) != null){
          tval$request.onFinish(p0);
       }
       return;
    }
    public void onReceiveData(bgq p0,byte[] p1){
       String str1;
       int i2;
       String str2;
       byte[] bytes;
       int i = 2;
       int i1 = 0;
       String str = "WindVane/NetworkSSRCache";
       IpChange $ipChange = FirstTruckCacheSSRService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("150b5e1a", objArray);
          return;
       }else if(p1 == null){
          return;
       }else if(!this.val$responseContext.isEnable() && (!this.val$needInvalidatingCache.get() || this.val$hasSkippedCachedTrunk.get())){
          this.val$requestCallback.onReceiveData(p0, p1);
       }else {
          try{
             this.val$byteArrayOutputStream.write(Arrays.copyOf(p1, p1.length));
          }catch(java.io.IOException e0){
             lcn.f(RVLLevel.Error, str, "failed to write bytes.");
          }
          $ipChange = 0;
          try{
             str1 = this.val$byteArrayOutputStream.toString(StandardCharsets.UTF_8.name());
          }catch(java.io.UnsupportedEncodingException e5){
             lcn.f(RVLLevel.Error, str, "toString error: "+e5.getMessage());
             str1 = $ipChange;
          }
          if (!TextUtils.isEmpty(str1) && (i2 = str1.indexOf("<div><!--fcc--></div>")) >= 0) {
             i2 = i2 + 21;
             try{
                str2 = str1.substring(i1, i2);
             }catch(java.lang.Exception e0){
                lcn.f(RVLLevel.Error, str, "failed to substring firstTrunkHtml");
             }
             if (str2 != null) {
                RVLLevel info = RVLLevel.Info;
                lcn.a(info, str).j("fccIndexed").f();
                wpw commonConfig = vpw.commonConfig;
                if (commonConfig.y2 != null) {
                   FirstTruckCacheSSRService$1 tval$request = this.val$requestCallback;
                   if (tval$request instanceof ChunkCacheRequestCallback) {
                      Object[] objArray1 = new Object[i1];
                      tval$request.onCustomCallback(e0, objArray1);
                   }
                }
                Charset uTF_8 = StandardCharsets.UTF_8;
                bytes = str2.getBytes(uTF_8);
                if (this.val$needInvalidatingCache.get()) {
                   lcn.a(info, "Themis/Performance/Snapshot").j("invalidateCache").f();
                   if (!FirstTruckCacheSSRService.access$400(this.this$0, p0.a)) {
                      this.val$requestCallback.onReceiveData(p0, "<script>document.getRootNode\(\).activeElement.innerHTML=null;document.head.innerHTML=null</script>".getBytes(uTF_8));
                   }
                   this.val$requestCallback.onReceiveData(p0, bytes);
                }
                i = bytes.length - this.val$offset.get();
                if (this.val$useFirstChunkCache != null && (i >= 0 && i < p1.length)) {
                   byte[] uobyteArray = Arrays.copyOfRange(p1, i, p1.length);
                   if (uobyteArray.length > 0) {
                      this.val$requestCallback.onReceiveData(p0, uobyteArray);
                   }
                }else {
                   lcn.f(RVLLevel.Error, str, "no remaining bytes fccByteIndex="+i+", bytes.length="+p1.length);
                }
                this.val$responseContext.setHtml(str2);
                this.val$responseContext.setHtmlLength(str2.length());
                this.val$responseContext.setStorageType(FccStorageType.CACHE);
                this.val$hasSkippedCachedTrunk.set(1);
                long l = SystemClock.uptimeMillis();
                str2 = "index";
                if (commonConfig.n3 != null) {
                   this.val$firstChunkStorage.write(this.val$responseContext);
                   lcn.a(info, str).j("writeCache").a(str2, Integer.valueOf(i2)).a("cost", Long.valueOf((SystemClock.uptimeMillis() - l))).f();
                }else if(commonConfig.p2 != null){
                   if (this.val$useFirstChunkCache != null && !this.val$needInvalidatingCache.get()) {
                      if (this.val$responseContext.getPriority() >= this.val$cachedResponseContext.getPriority()) {
                         this.val$firstChunkStorage.write(this.val$responseContext);
                         lcn.a(info, str).j("updateCache").a("cost", Long.valueOf((SystemClock.uptimeMillis() - l))).f();
                      }else {
                         lcn.a(info, str).j("ignoreCacheUpdate").a("cost", Long.valueOf((SystemClock.uptimeMillis() - l))).f();
                      }
                   }else {
                      this.val$firstChunkStorage.write(this.val$responseContext);
                      lcn.a(info, str).j("writeCache").a(str2, Integer.valueOf(i2)).a("cost", Long.valueOf((SystemClock.uptimeMillis() - l))).f();
                   }
                }else {
                   this.val$firstChunkStorage.write(this.val$responseContext);
                   lcn.a(info, str).j("writeCache").a(str2, Integer.valueOf(i2)).a("cost", Long.valueOf((SystemClock.uptimeMillis() - l))).f();
                }
                try{
                   this.val$byteArrayOutputStream.close();
                }catch(java.io.IOException e0){
                   lcn.f(RVLLevel.Info, str, "failed to close byteArrayOutputStream");
                }
             }
          }
          if (this.val$useFirstChunkCache == null) {
             this.val$requestCallback.onReceiveData(p0, p1);
          }
       }
       this.val$offset.addAndGet(p1.length);
       return;
    }
    public void onResponse(bgq p0,int p1,Map p2){
       int i5;
       bgq a;
       object oobject = this;
       object oobject1 = p0;
       int i = p1;
       object oobject2 = p2;
       int i1 = 3;
       boolean i2 = 2;
       int i3 = 1;
       int i4 = 0;
       IpChange $ipChange = FirstTruckCacheSSRService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,oobject1,new Integer(i),oobject2};
          $ipChange.ipc$dispatch("1e09d3a7", objArray);
          return;
       }else {
          wpw commonConfig = vpw.commonConfig;
          if (commonConfig.V2 != null) {
             FirstTruckCacheSSRService.access$000(oobject.this$0, oobject.val$responseContext, oobject2);
          }else {
             FirstTruckCacheSSRService.access$100(oobject.this$0, oobject.val$responseContext, oobject2);
          }
          if (oobject.val$useFirstChunkCache != null) {
             i5 = (oobject.val$responseContext.isEnable() && TextUtils.equals(oobject.val$responseContext.getVersion(), oobject.val$cachedResponseContext.getVersion()))? 1: 2;
          }else if(oobject.val$isExpired != null){
             i5 = 3;
          }else {
             i5 = 0;
          }
          RVLLevel info = RVLLevel.Info;
          icn oicn = lcn.a(info, "Themis/Performance/Snapshot").j("access").a("message", FirstTruckCacheSSRService.access$200(oobject.this$0, i5)).a("enable", Boolean.valueOf(oobject.val$responseContext.isEnable())).a("url", oobject1.a).a("cost", Long.valueOf(oobject.val$loadChunkCost)).a("responseHeaders", oobject2).a("serverVersion", oobject.val$responseContext.getVersion()).a("serverRule", oobject.val$responseContext.getRule());
          FirstTruckCacheSSRService$1 val$cachedRe = oobject.val$cachedResponseContext;
          String str = "";
          String str1 = (val$cachedRe == null)? str: val$cachedRe.getRule();
          oicn = oicn.a("localRule", str1);
          str1 = ((val$cachedRe = oobject.val$cachedResponseContext) == null)? str: val$cachedRe.getVersion();
          oicn = oicn.a("localVersion", str1);
          int i6 = ((val$cachedRe = oobject.val$cachedResponseContext) == null)? 0: val$cachedRe.getPriority();
          oicn = oicn.a("localPriority", Integer.valueOf(i6));
          if ((val$cachedRe = oobject.val$cachedResponseContext) != null) {
             str = val$cachedRe.getStorageType().name;
          }
          oicn.a("localStorageType", str).f();
          FirstTruckCacheSSRService$1 val$requestC = oobject.val$requestCallback;
          if (val$requestC instanceof ChunkCacheRequestCallback) {
             if (commonConfig.y2 != null) {
                Object[] objArray1 = new Object[i3];
                objArray1[i4] = Integer.valueOf(i5);
                val$requestC.onCustomCallback(i4, objArray1);
             }
             oobject.val$requestCallback.onNetworkResponse(i, oobject2);
          }
          FirstTruckCacheSSRService.access$300(oobject.this$0, oobject1.a, i5, oobject.val$loadChunkCost, oobject.val$firstChunkCacheSize, oobject.val$firstChunkCacheType);
          FirstTruckCacheSSRService$1 val$needInva = oobject.val$needInvalidatingCache;
          i2 = (oobject.val$useFirstChunkCache != null && (oobject.val$responseContext.isEnable() && !TextUtils.equals(oobject.val$responseContext.getVersion(), oobject.val$cachedResponseContext.getVersion())))? true: false;
          val$needInva.set(i2);
          if (oobject.val$useFirstChunkCache != null) {
             if (!oobject.val$responseContext.isEnable()) {
                String str2 = "WindVane/NetworkSSRCache";
                lcn.a(info, str2).j("disableCache").f();
                oobject.val$firstChunkStorage.remove();
                if ((a = oobject1.a) != null && (a.contains("fcc-downgrade-failover=true") && !FirstTruckCacheSSRService.access$400(oobject.this$0, a))) {
                   i6 = i5;
                   FirstTruckCacheSSRService.access$500(oobject.this$0, oobject.val$requestCallback, p0, p1, p2, i5);
                }else {
                   i6 = i5;
                   lcn.a(info, str2).j("downgradeDefault").f();
                   oobject.val$requestCallback.onReceiveData(oobject1, "<script>document.getRootNode\(\).activeElement.innerHTML=null;document.head.innerHTML=null</script>".getBytes(StandardCharsets.UTF_8));
                }
             }else {
                i6 = i5;
             }
             if (FirstTruckCacheSSRService.access$400(oobject.this$0, oobject1.a) && oobject.val$needInvalidatingCache.get()) {
                FirstTruckCacheSSRService.access$500(oobject.this$0, oobject.val$requestCallback, p0, p1, p2, i6);
             }
             if (oobject.val$needInvalidatingCache.get()) {
                oobject.val$firstChunkStorage.remove();
             }
          }else {
             oobject.val$requestCallback.onResponse(oobject1, i, oobject2);
          }
          return;
       }
    }
}
