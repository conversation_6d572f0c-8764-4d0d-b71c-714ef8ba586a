package android.taobao.windvane.extra.uc.WVCoreSettings;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Integer;
import tb.vpw;
import tb.wpw;
import tb.xsw;
import tb.ysw;
import tb.psu;
import tb.au4;
import java.util.Iterator;
import java.util.List;
import tb.yt4;
import tb.zt4;
import android.taobao.windvane.extra.core.WVCore;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.ArrayList;
import android.taobao.windvane.extra.uc.WVUCWebView;

public class WVCoreSettings	// class@00022d from classes.dex
{
    public List coreEventCallbacks;
    public List coreEventCallbacks2;
    public static IpChange $ipChange;
    public static final int DOWNLOAD;
    public static final int INNER;
    public static final int U420;
    public static final int U430;
    private static WVCoreSettings instance;

    static {
       t2o.a(0x3d800162);
    }
    public void WVCoreSettings(){
       super();
    }
    public static WVCoreSettings getInstance(){
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("b02c5aa4", objArray);
       }else if(WVCoreSettings.instance == null){
          WVCoreSettings wVCoreSettin = WVCoreSettings.class;
          _monitor_enter(wVCoreSettin);
          if (WVCoreSettings.instance == null) {
             WVCoreSettings.instance = new WVCoreSettings();
          }
          _monitor_exit(wVCoreSettin);
       }
       return WVCoreSettings.instance;
    }
    public static void setCorePolicy(int p0){
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Integer(p0)};
          $ipChange.ipc$dispatch("eb550389", objArray);
          return;
       }else {
          vpw.commonConfig.r = p0;
          return;
       }
    }
    public static void setDownloadCore(int p0){
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Integer(p0)};
          $ipChange.ipc$dispatch("345bbf33", objArray);
          return;
       }else {
          vpw.commonConfig.y = p0;
          return;
       }
    }
    public static void setGpuMultiPolicy(int p0){
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Integer(p0)};
          $ipChange.ipc$dispatch("9414677b", objArray);
          return;
       }else {
          xsw.configData.b = p0;
          return;
       }
    }
    public static void setInputSupportedDomains(String p0){
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("efe5c21a", objArray);
          return;
       }else {
          vpw.commonConfig.i.a = p0;
          return;
       }
    }
    public static void setWebMultiPolicy(int p0){
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Integer(p0)};
          $ipChange.ipc$dispatch("ffda5223", objArray);
          return;
       }else {
          xsw.configData.a = p0;
          return;
       }
    }
    public void notifyCoreEventCallback2Fail(au4 p0){
       WVCoreSettings tcoreEventCa;
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5bba60fe", objArray);
          return;
       }else if((tcoreEventCa = this.coreEventCallbacks2) != null){
          Iterator iterator = tcoreEventCa.iterator();
          while (iterator.hasNext()) {
             iterator.next().onUCCoreInitFailed(p0);
          }
          this.coreEventCallbacks2.clear();
       }
       return;
    }
    public void notifyCoreEventCallback2Fail(yt4 p0,au4 p1){
       WVCoreSettings tcoreEventCa;
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("294201fb", objArray);
          return;
       }else if(p0 != null){
          p0.onUCCoreInitFailed(p1);
          if ((tcoreEventCa = this.coreEventCallbacks2) != null) {
             tcoreEventCa.remove(p0);
          }
       }
       return;
    }
    public void notifyCoreEventCallback2Success(){
       WVCoreSettings tcoreEventCa;
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("d159904f", objArray);
          return;
       }else if((tcoreEventCa = this.coreEventCallbacks2) != null){
          Iterator iterator = tcoreEventCa.iterator();
          while (iterator.hasNext()) {
             iterator.next().onUCCorePrepared();
          }
          this.coreEventCallbacks2.clear();
       }
       return;
    }
    public void notifyCoreEventCallbackSuccess(){
       WVCoreSettings tcoreEventCa;
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5ba52b33", objArray);
          return;
       }else if((tcoreEventCa = this.coreEventCallbacks2) != null){
          Iterator iterator = tcoreEventCa.iterator();
          while (iterator.hasNext()) {
             iterator.next().onUCCorePrepared();
          }
          this.coreEventCallbacks2.clear();
       }
       return;
    }
    public void removeEventCallback2(yt4 p0){
       WVCoreSettings tcoreEventCa;
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("ae980f21", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if((tcoreEventCa = this.coreEventCallbacks2) != null){
          tcoreEventCa.remove(p0);
       }
       return;
    }
    public void setCoreEventCallback(zt4 p0){
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("96d36116", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if(WVCore.getInstance().isUCSupport()){
          p0.onUCCorePrepared();
          return;
       }else if(this.coreEventCallbacks == null){
          this.coreEventCallbacks = (vpw.commonConfig.Q != null)? new CopyOnWriteArrayList(): new ArrayList();
       }
       if (!this.coreEventCallbacks.contains(p0)) {
          this.coreEventCallbacks.add(p0);
       }
       return;
    }
    public void setCoreEventCallback2(yt4 p0){
       IpChange $ipChange = WVCoreSettings.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("57ae63fe", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if(WVCore.getInstance().isUCSupport()){
          p0.onUCCorePrepared();
          return;
       }else if(this.coreEventCallbacks2 == null){
          this.coreEventCallbacks2 = new CopyOnWriteArrayList();
       }
       if (!this.coreEventCallbacks2.contains(p0)) {
          this.coreEventCallbacks2.add(p0);
       }
       return;
    }
}
