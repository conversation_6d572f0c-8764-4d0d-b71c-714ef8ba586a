package android.taobao.windvane.cache.WVFileCache;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import android.taobao.windvane.cache.WVFileCache$FixedSizeLinkedHashMap;
import java.util.Map;
import java.util.Collections;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;
import java.nio.channels.FileChannel;
import java.lang.Boolean;
import java.io.File;
import java.lang.System;
import java.nio.ByteBuffer;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.v7t;
import java.io.ByteArrayOutputStream;
import tb.oqw;
import tb.pqw;
import java.nio.Buffer;
import java.io.RandomAccessFile;
import java.nio.channels.spi.AbstractInterruptibleChannel;
import android.os.Process;
import java.util.ArrayList;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import java.lang.Integer;
import tb.sb9;

public class WVFileCache	// class@00014a from classes.dex
{
    public final String a;
    public final String b;
    public boolean c;
    public final Map d;
    public RandomAccessFile e;
    public FileChannel f;
    public final boolean g;
    public final int h;
    public static IpChange $ipChange;
    public static final int CREATE;
    public static final int DELETE;
    public static final int READ;
    public static final int WRITE;
    public static final String i;

    static {
       t2o.a(0x3d800013);
       WVFileCache.i = "WVFileCache";
    }
    public void WVFileCache(String p0,String p1,int p2,boolean p3){
       super();
       this.d = Collections.synchronizedMap(new WVFileCache$FixedSizeLinkedHashMap(this));
       this.g = true;
       this.a = p0;
       this.b = p1;
       this.h = p2;
       this.c = false;
    }
    public static int a(WVFileCache p0){
       IpChange $ipChange = WVFileCache.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.h;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("bb9493e7", objArray).intValue();
    }
    public static String b(){
       IpChange $ipChange = WVFileCache.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVFileCache.i;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("6c2478e8", objArray);
    }
    public static String c(WVFileCache p0){
       IpChange $ipChange = WVFileCache.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.a;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("7f8c3eae", objArray);
    }
    public static FileChannel d(WVFileCache p0){
       IpChange $ipChange = WVFileCache.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.f;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("e8eaf1d2", objArray);
    }
    public boolean e(){
       String[] stringArray;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = WVFileCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("b42d4c58", objArray).booleanValue();
       }else if(this.c != null && (stringArray = new File(this.a).list()) != null){
          int len = stringArray.length;
          int i2 = 1;
          for (; i < len; i = i + i1) {
             i2 = i2 & this.g(stringArray[i]);
          }
          return i2;
       }else {
          return i;
       }
    }
    public final boolean f(){
       byte[] uobyteArray;
       oqw ooqw;
       int i = 60;
       int i1 = 0;
       int i2 = 1;
       IpChange $ipChange = WVFileCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i2];
          objArray[i1] = this;
          return $ipChange.ipc$dispatch("93289978", objArray).booleanValue();
       }else {
          long l = System.currentTimeMillis();
          try{
             ByteBuffer uByteBuffer = ByteBuffer.allocate((int)this.f.size());
             this.f.read(uByteBuffer);
             uobyteArray = uByteBuffer.array();
          }catch(java.io.IOException e5){
             v7t.d(WVFileCache.i, "collectFiles fInfoChannel.read error:"+e5.getMessage());
             uobyteArray = null;
          }
          if (v7t.h()) {
             v7t.a(WVFileCache.i, "collectFiles read fileinfo:"+(System.currentTimeMillis() - l));
          }
          l = System.currentTimeMillis();
          if (uobyteArray != null) {
             v7t.a("collectFiles", "read fileinfo success");
             ByteArrayOutputStream uByteArrayOu = new ByteArrayOutputStream();
             int i3 = 60;
             int i4 = 0;
             int i5 = 0;
             while (i3 < uobyteArray.length) {
                if (uobyteArray[i3] == 10) {
                   int i6 = i3 - i5;
                   if ((ooqw = pqw.b(uobyteArray, i5, i6)) != null) {
                      oqw c = ooqw.c;
                      if (!this.d.containsKey(c)) {
                         ooqw.h = (long)uByteArrayOu.size();
                         this.d.put(c, ooqw);
                         i6 = i6 + i2;
                         uByteArrayOu.write(uobyteArray, i5, i6);
                      label_00af :
                         i5 = i3 + 1;
                         i3 = i3 + i;
                      }
                   }
                   i4 = 1;
                   goto label_00af ;
                }
                i3 = i3 + i2;
             }
             if (v7t.h()) {
                v7t.a(WVFileCache.i, "parse fileinfo:"+(System.currentTimeMillis() - l));
             }
             l = System.currentTimeMillis();
             if (i4) {
                try{
                   this.f.truncate(0);
                   this.f.position(0);
                   ByteBuffer uByteBuffer1 = ByteBuffer.wrap(uByteArrayOu.toByteArray());
                   uByteBuffer1.position(i1);
                   this.f.write(uByteBuffer1);
                }catch(java.io.IOException e0){
                   v7t.d(WVFileCache.i, "collectFiles fInfoChannel.write error:"+e0.getMessage());
                }
             }
             try{
                uByteArrayOu.close();
             }catch(java.io.IOException e0){
                e0.printStackTrace();
             }
             if (v7t.h()) {
                v7t.a(WVFileCache.i, "write fileinfo:"+(System.currentTimeMillis() - l));
             }
             return i2;
          }else {
             return i1;
          }
       }
    }
    public void finalize(){
       WVFileCache te;
       IpChange $ipChange = WVFileCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("a35321a5", objArray);
          return;
       }else if((te = this.e) != null){
          try{
             te.close();
          }catch(java.lang.Exception e0){
             e0.printStackTrace();
          }
       }
       if ((te = this.f) != null) {
          try{
             te.close();
          }catch(java.lang.Exception e0){
             e0.printStackTrace();
          }
       }
       super.finalize();
       return;
    }
    public boolean g(String p0){
       oqw ooqw;
       int i = 0;
       IpChange $ipChange = WVFileCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ea815e00", objArray).booleanValue();
       }else if(this.c != null && p0 != null){
          long l = System.currentTimeMillis();
          File uFile = new File(this.a, p0);
          if (uFile.isFile()) {
             i = uFile.delete();
          }
          if (i || (!uFile.exists() && (ooqw = this.d.get(p0)) != null)) {
             String i1 = WVFileCache.i;
             v7t.a(i1, "delete success");
             pqw.d(3, ooqw, this.f);
             this.d.remove(p0);
             if (v7t.h()) {
                v7t.a(i1, "delete time cost:"+(System.currentTimeMillis() - l));
             }
             return 1;
          }
       }
       return i;
    }
    public String h(){
       IpChange $ipChange = WVFileCache.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7a504e5d", objArray);
    }
    public synchronized boolean i(){
       int i = 0;
       int i1 = 1;
       String str = "lock success process is ";
       IpChange $ipChange = WVFileCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("fede19b", objArray).booleanValue();
       }else if(this.c == null){
          File uFile = new File(this.b, "wv_web_info.dat");
          if (!uFile.exists()) {
             File uFile1 = new File(this.b);
             try{
                uFile1.mkdirs();
                uFile.createNewFile();
             }catch(java.io.IOException e1){
                v7t.d(WVFileCache.i, "init createNewFile:"+e1.getMessage());
                return i;
             }
          }
          File uFile2 = new File(this.a);
          try{
             uFile2.mkdirs();
             RandomAccessFile randomAccess = new RandomAccessFile(uFile.getAbsolutePath(), "rw");
             this.e = randomAccess;
             if (this.f == null) {
                this.f = randomAccess.getChannel();
             }
             if (v7t.h()) {
                v7t.a(WVFileCache.i, str+Process.myPid());
             }
             long l = System.currentTimeMillis();
             if (!this.f()) {
                return i;
             }else if(v7t.h()){
                v7t.a(WVFileCache.i, "init time cost:"+(System.currentTimeMillis() - l));
             }
             this.c = i1;
             this.k(this.h);
             if (!this.d.size()) {
                this.e();
             }
          }catch(java.lang.Exception e1){
             v7t.d(WVFileCache.i, "init fInfoOs RandomAccessFile:"+e1.getMessage());
             return i;
          }
       }
       return i1;
    }
    public final void j(){
       oqw value;
       IpChange $ipChange = WVFileCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("b1f70b24", objArray);
          return;
       }else {
          v7t.a(WVFileCache.i, "onFileOverflow");
          ArrayList uArrayList = new ArrayList();
          int i = this.d.size();
          Iterator iterator = this.d.entrySet().iterator();
          while (true) {
             if (iterator.hasNext()) {
                Map$Entry uEntry = iterator.next();
                if (i >= this.h) {
                   if ((value = uEntry.getValue()) != null) {
                      uArrayList.add(value);
                   }
                   i = i - 1;
                }else {
                label_004d :
                   Iterator iterator1 = uArrayList.iterator();
                   while (iterator1.hasNext()) {
                      this.g(iterator1.next().c);
                   }
                   return;
                }
             }else {
                goto label_004d ;
             }
          }
       }
    }
    public final void k(int p0){
       IpChange $ipChange = WVFileCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("e64e71a0", objArray);
          return;
       }else if(this.d.size() > p0){
          this.j();
       }
       return;
    }
    public boolean l(oqw p0,ByteBuffer p1){
       oqw c;
       File uFile;
       boolean b;
       oqw ooqw;
       int i = 2;
       int i1 = 0;
       IpChange $ipChange = WVFileCache.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("6426fda", objArray).booleanValue();
       }else if(p0 != null && (c = p0.c) != null){
          if (v7t.h()) {
             v7t.a(WVFileCache.i, "write:".concat(c));
          }
          if (this.c != null) {
             WVFileCache ta = this.a;
             try{
                uFile = new File(ta, c);
                b = sb9.g(uFile, p1);
             label_007b :
                if (b) {
                   if ((ooqw = this.d.get(c)) != null) {
                      v7t.a(WVFileCache.i, "writed success, file exist");
                      p0.h = ooqw.h;
                      this.d.put(c, pqw.d(i, p0, this.f).b());
                   }else {
                      v7t.a(WVFileCache.i, "writed success, file do not exist");
                      this.d.put(c, pqw.d(4, p0, this.f).b());
                   }
                   return 1;
                }
             }catch(android.taobao.windvane.file.NotEnoughSpace e5){
                v7t.d(WVFileCache.i, "write error. fileName="+c+". NotEnoughSpace: "+e5.getMessage());
                if (this.g != null) {
                   try{
                      this.e();
                      b = sb9.g(uFile, b);
                      goto label_007b ;
                   }catch(android.taobao.windvane.file.NotEnoughSpace e11){
                      e11.printStackTrace();
                   }
                   b = false;
                   goto label_007b ;
                }else {
                }
             }
          }
       }
       return i1;
    }
}
