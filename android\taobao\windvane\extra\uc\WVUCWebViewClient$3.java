package android.taobao.windvane.extra.uc.WVUCWebViewClient$3;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.uc.WVUCWebViewClient;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;
import tb.v7t;

public class WVUCWebViewClient$3 implements ValueCallback	// class@000265 from classes.dex
{
    public final WVUCWebViewClient this$0;
    public static IpChange $ipChange;

    public void WVUCWebViewClient$3(WVUCWebViewClient p0){
       this.this$0 = p0;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       IpChange $ipChange = WVUCWebViewClient$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else {
          v7t.d("WVUCWebViewClient", "JSTfsp receiveValue "+p0);
          return;
       }
    }
}
