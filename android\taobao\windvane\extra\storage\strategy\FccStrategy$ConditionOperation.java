package android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionOperation;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class FccStrategy$ConditionOperation extends Enum	// class@0001f0 from classes.dex
{
    public final String operationName;
    private static final FccStrategy$ConditionOperation[] $VALUES;
    public static IpChange $ipChange;
    public static final FccStrategy$ConditionOperation EQUAL;

    static {
       FccStrategy$ConditionOperation uConditionOp = new FccStrategy$ConditionOperation("EQUAL", 0, "=");
       FccStrategy$ConditionOperation.EQUAL = uConditionOp;
       FccStrategy$ConditionOperation[] uConditionOp1 = new FccStrategy$ConditionOperation[]{uConditionOp};
       FccStrategy$ConditionOperation.$VALUES = uConditionOp1;
    }
    private void FccStrategy$ConditionOperation(String p0,int p1,String p2){
       super(p0, p1);
       this.operationName = p2;
    }
    public static Object ipc$super(FccStrategy$ConditionOperation p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/storage/strategy/FccStrategy$ConditionOperation");
    }
    public static FccStrategy$ConditionOperation valueOf(String p0){
       IpChange $ipChange = FccStrategy$ConditionOperation.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(FccStrategy$ConditionOperation.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("e1ff7b6e", objArray);
    }
    public static FccStrategy$ConditionOperation[] values(){
       IpChange $ipChange = FccStrategy$ConditionOperation.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return FccStrategy$ConditionOperation.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("f4cadd", objArray);
    }
}
