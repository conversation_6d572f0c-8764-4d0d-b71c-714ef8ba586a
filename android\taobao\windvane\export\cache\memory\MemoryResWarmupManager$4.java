package android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$4;
import android.webkit.ValueCallback;
import android.taobao.windvane.export.cache.memory.model.ResourceItemModel;
import java.util.HashMap;
import java.lang.Object;
import java.lang.Integer;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import tb.y71;
import java.lang.StringBuilder;
import tb.v7t;
import java.lang.Throwable;

public final class MemoryResWarmupManager$4 implements ValueCallback	// class@000158 from classes.dex
{
    public final ResourceItemModel val$model;
    public final HashMap val$scriptUrls;
    public static IpChange $ipChange;

    public void MemoryResWarmupManager$4(ResourceItemModel p0,HashMap p1){
       this.val$model = p0;
       this.val$scriptUrls = p1;
       super();
    }
    public void onReceiveValue(Integer p0){
       int i = 0;
       String str = "MemoryResWarmupManager";
       String str1 = "generateCodeCache result:";
       IpChange $ipChange = MemoryResWarmupManager$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("49891c23", objArray);
          return;
       }else {
          try{
             JSONObject $ipChange1 = new JSONObject();
             $ipChange1.put("url", this.val$model.src);
             $ipChange1.put("successCount", Integer.valueOf((p0.intValue() & 0xffff)));
             $ipChange1.put("newCount", Integer.valueOf((p0.intValue() >> 16)));
             $ipChange1.put("count", Integer.valueOf(this.val$scriptUrls.size()));
             y71.commitSuccess("JsAot", JSON.toJSONString($ipChange1));
             v7t.i(str, str1+this.val$model.src+",successCnt:"+(p0.intValue() & 0xffff)+",newCount:"+(p0.intValue() >> 16));
          }catch(java.lang.Exception e8){
             Object[] objArray1 = new Object[i];
             v7t.e(str, "report js aot error", e8, objArray1);
          }
          return;
       }
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
}
