package android.taobao.windvane.extra.uc.UCSetupService$5;
import java.lang.Runnable;
import android.content.Context;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.uc.webview.export.extension.U4Engine$Initializer;
import com.uc.webview.export.extension.U4Engine;
import java.io.File;
import java.lang.Throwable;
import tb.v7t;

public final class UCSetupService$5 implements Runnable	// class@000227 from classes.dex
{
    public final Context val$ctx;
    public static IpChange $ipChange;

    public void UCSetupService$5(Context p0){
       this.val$ctx = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = UCSetupService$5.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          U4Engine.createInitializer();
          U4Engine.getRunningDir(this.val$ctx, 0);
          return;
       }
    }
}
