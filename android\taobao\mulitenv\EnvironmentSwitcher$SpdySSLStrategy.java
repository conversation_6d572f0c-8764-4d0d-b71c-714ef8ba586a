package android.taobao.mulitenv.EnvironmentSwitcher$SpdySSLStrategy;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;

public final class EnvironmentSwitcher$SpdySSLStrategy extends Enum	// class@000140 from classes.dex
{
    private static final EnvironmentSwitcher$SpdySSLStrategy[] $VALUES;
    public static IpChange $ipChange;
    public static final EnvironmentSwitcher$SpdySSLStrategy DISABLE_DEGRADE;
    public static final EnvironmentSwitcher$SpdySSLStrategy ENABLE_DEGRADE;

    static {
       EnvironmentSwitcher$SpdySSLStrategy spdySSLStrat = new EnvironmentSwitcher$SpdySSLStrategy("ENABLE_DEGRADE", 0);
       EnvironmentSwitcher$SpdySSLStrategy.ENABLE_DEGRADE = spdySSLStrat;
       EnvironmentSwitcher$SpdySSLStrategy spdySSLStrat1 = new EnvironmentSwitcher$SpdySSLStrategy("DISABLE_DEGRADE", 1);
       EnvironmentSwitcher$SpdySSLStrategy.DISABLE_DEGRADE = spdySSLStrat1;
       EnvironmentSwitcher$SpdySSLStrategy[] spdySSLStrat2 = new EnvironmentSwitcher$SpdySSLStrategy[]{spdySSLStrat,spdySSLStrat1};
       EnvironmentSwitcher$SpdySSLStrategy.$VALUES = spdySSLStrat2;
    }
    private void EnvironmentSwitcher$SpdySSLStrategy(String p0,int p1){
       super(p0, p1);
    }
    public static Object ipc$super(EnvironmentSwitcher$SpdySSLStrategy p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/mulitenv/EnvironmentSwitcher$SpdySSLStrategy");
    }
    public static EnvironmentSwitcher$SpdySSLStrategy valueOf(String p0){
       IpChange $ipChange = EnvironmentSwitcher$SpdySSLStrategy.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(EnvironmentSwitcher$SpdySSLStrategy.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("e80a649d", objArray);
    }
    public static EnvironmentSwitcher$SpdySSLStrategy[] values(){
       IpChange $ipChange = EnvironmentSwitcher$SpdySSLStrategy.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return EnvironmentSwitcher$SpdySSLStrategy.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("e14501cc", objArray);
    }
}
