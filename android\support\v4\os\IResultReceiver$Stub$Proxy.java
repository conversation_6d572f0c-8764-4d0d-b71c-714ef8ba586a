package android.support.v4.os.IResultReceiver$Stub$Proxy;
import android.support.v4.os.IResultReceiver;
import android.os.IBinder;
import java.lang.Object;
import java.lang.String;
import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;
import android.support.v4.os.IResultReceiver$_Parcel;

class IResultReceiver$Stub$Proxy implements IResultReceiver	// class@00012f from classes.dex
{
    private IBinder mRemote;

    public void IResultReceiver$Stub$Proxy(IBinder p0){
       super();
       this.mRemote = p0;
    }
    public IBinder asBinder(){
       return this.mRemote;
    }
    public String getInterfaceDescriptor(){
       return IResultReceiver.DESCRIPTOR;
    }
    public void send(int p0,Bundle p1){
       Parcel parcel = Parcel.obtain();
       parcel.writeInterfaceToken(IResultReceiver.DESCRIPTOR);
       parcel.writeInt(p0);
       IResultReceiver$_Parcel.access$100(parcel, p1, 0);
       this.mRemote.transact(1, parcel, null, 1);
       parcel.recycle();
    }
}
