package android.taobao.windvane.extra.launch.WindVaneIdleTask$4;
import java.lang.Runnable;
import android.taobao.windvane.extra.launch.WindVaneIdleTask;
import java.io.File;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ak7;

public class WindVaneIdleTask$4 implements Runnable	// class@0001be from classes.dex
{
    public final WindVaneIdleTask this$0;
    public final File val$dexFile;
    public final boolean val$success;
    public static IpChange $ipChange;

    public void WindVaneIdleTask$4(WindVaneIdleTask p0,File p1,boolean p2){
       this.this$0 = p0;
       this.val$dexFile = p1;
       this.val$success = p2;
       super();
    }
    public void run(){
       IpChange $ipChange = WindVaneIdleTask$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          ak7.a(this.val$dexFile, this.val$success);
          return;
       }
    }
}
