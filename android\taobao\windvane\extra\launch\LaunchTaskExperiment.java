package android.taobao.windvane.extra.launch.LaunchTaskExperiment;
import tb.t2o;
import java.lang.Object;
import android.content.Context;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import com.taobao.android.ab.api.ABGlobal;
import com.taobao.android.riverlogger.RVLLevel;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.lcn;

public class LaunchTaskExperiment	// class@0001b4 from classes.dex
{
    public static IpChange $ipChange;
    private static Boolean sValue;

    static {
       t2o.a(0x3d8000e9);
       LaunchTaskExperiment.sValue = null;
    }
    public void LaunchTaskExperiment(){
       super();
    }
    public static boolean enablePrioritizeInitAndDelayPreCreate(Context p0){
       String str = "enablePrioritizeInitAndDelayPreCreate error";
       IpChange $ipChange = LaunchTaskExperiment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("aff2f384", objArray).booleanValue();
       }else if(LaunchTaskExperiment.sValue != null){
          return LaunchTaskExperiment.sValue.booleanValue();
       }else {
          _monitor_enter(LaunchTaskExperiment.class);
          if (LaunchTaskExperiment.sValue != null) {
             _monitor_exit(LaunchTaskExperiment.class);
             return LaunchTaskExperiment.sValue.booleanValue();
          }else {
             try{
                LaunchTaskExperiment.sValue = Boolean.valueOf(ABGlobal.isFeatureOpened(p0, "wvPrioritizeInitAndDelayPreCreate"));
             }catch(java.lang.Exception e5){
                lcn.f(RVLLevel.Error, "WindVane/Launch", str+e5.getMessage());
             }
             _monitor_exit(LaunchTaskExperiment.class);
             return LaunchTaskExperiment.sValue.booleanValue();
          }
       }
    }
}
