package android.taobao.windvane.extra.uc.WVUCWebView$11;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.uc.WVUCWebView;
import android.taobao.windvane.extra.uc.WVUCWebView$whiteScreenCallback;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;

public class WVUCWebView$11 implements ValueCallback	// class@000252 from classes.dex
{
    public final WVUCWebView this$0;
    public final WVUCWebView$whiteScreenCallback val$callback;
    public static IpChange $ipChange;

    public void WVUCWebView$11(WVUCWebView p0,WVUCWebView$whiteScreenCallback p1){
       this.this$0 = p0;
       this.val$callback = p1;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       WVUCWebView$11 tval$callbac;
       IpChange $ipChange = WVUCWebView$11.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else if((tval$callbac = this.val$callback) != null){
          tval$callbac.isPageEmpty(p0);
       }
       return;
    }
}
