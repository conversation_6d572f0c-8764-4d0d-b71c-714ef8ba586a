package androidx.activity.compose.BackHandlerKt$BackHandler$1$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.compose.BackHandlerKt$BackHandler$backCallback$1$1;
import java.lang.Object;
import tb.xhv;
import androidx.activity.OnBackPressedCallback;

public final class BackHandlerKt$BackHandler$1$1 extends Lambda implements d1a	// class@00047c from classes.dex
{
    public final BackHandlerKt$BackHandler$backCallback$1$1 $backCallback;
    public final boolean $enabled;

    public void BackHandlerKt$BackHandler$1$1(BackHandlerKt$BackHandler$backCallback$1$1 p0,boolean p1){
       this.$backCallback = p0;
       this.$enabled = p1;
       super(0);
    }
    public Object invoke(){
       this.invoke();
       return xhv.INSTANCE;
    }
    public final void invoke(){
       this.$backCallback.setEnabled(this.$enabled);
    }
}
