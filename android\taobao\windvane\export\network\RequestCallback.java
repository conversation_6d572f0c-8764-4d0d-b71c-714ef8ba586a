package android.taobao.windvane.export.network.RequestCallback;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import java.util.Map;

public class RequestCallback	// class@00016e from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800092);
    }
    public void RequestCallback(){
       super();
    }
    public void onCustomCallback(int p0,Object[] p1){
       IpChange $ipChange = RequestCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("c344bdea", objArray);
       }
       return;
    }
    public void onError(int p0,String p1){
       IpChange $ipChange = RequestCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("7a671c9d", objArray);
       }
       return;
    }
    public void onFinish(){
       IpChange $ipChange = RequestCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("badeed9", objArray);
       }
       return;
    }
    public void onNetworkResponse(int p0,Map p1){
       IpChange $ipChange = RequestCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("92d71559", objArray);
       }
       return;
    }
    public void onReceiveData(byte[] p0){
       IpChange $ipChange = RequestCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f24c16dc", objArray);
       }
       return;
    }
    public void onResponse(int p0,Map p1){
       IpChange $ipChange = RequestCallback.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("bb214fe9", objArray);
       }
       return;
    }
}
