package androidx.activity.compose.ReportDrawnComposition$checkReporter$1;
import tb.g1a;
import kotlin.jvm.internal.FunctionReferenceImpl;
import java.lang.Object;
import androidx.activity.compose.ReportDrawnComposition;
import java.lang.Class;
import java.lang.String;
import tb.d1a;
import tb.xhv;
import kotlin.jvm.internal.CallableReference;

public final class ReportDrawnComposition$checkReporter$1 extends FunctionReferenceImpl implements g1a	// class@000493 from classes.dex
{

    public void ReportDrawnComposition$checkReporter$1(Object p0){
       super(1, p0, ReportDrawnComposition.class, "observeReporter", "observeReporter\(Lkotlin/jvm/functions/Function0;\)V", 0);
    }
    public Object invoke(Object p0){
       this.invoke(p0);
       return xhv.INSTANCE;
    }
    public final void invoke(d1a p0){
       ReportDrawnComposition.access$observeReporter(this.receiver, p0);
    }
}
