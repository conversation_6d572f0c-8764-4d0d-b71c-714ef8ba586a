package android.taobao.windvane.export.network.Request$StageName;
import java.lang.annotation.Annotation;

public interface abstract Request$StageName implements Annotation	// class@000166 from classes.dex
{
    public static final String DOCUMENT_NETWORK_TTFB = "documentNetworkTTFB";
    public static final String DOCUMENT_PREFETCH_CONSUME = "documentPrefetchHitTime";
    public static final String DOCUMENT_REQUEST_END = "documentRequestEnd";
    public static final String DOCUMENT_REQUEST_HEADER_ADDED = "documentRequestHeaderAdded";
    public static final String DOCUMENT_REQUEST_SENT = "documentRequestSent";
    public static final String DOCUMENT_REQUEST_START = "documentRequestStart";
    public static final String DOCUMENT_REQUEST_THREAD_SWITCHED = "documentRequestThreadSwitched";
    public static final String DOCUMENT_REQUEST_ZCACHE_READ = "documentRequestZCacheRead";
    public static final String DOCUMENT_TTFB = "documentTTFB";
    public static final String MAIN_REQUEST_FINISH = "BottomSheetBehavior_Layout_behavior_expandedOffset";
    public static final String MAIN_REQUEST_RECV_START = "BottomSheetBehavior_Layout_behavior_fitToContents";
    public static final String MAIN_REQUEST_RETRY_START = "BottomSheetBehavior_Layout";
    public static final String MAIN_REQUEST_START = "BottomSheetBehavior_Layout_backgroundTint";
    public static final String MTOP_FETCH_START = "ET_TEMPLATE";
    public static final String MTOP_FINISH = "EVDO_A";
    public static final String MTOP_NET_SEND = "EVENT_BLUETOOTH_ADAPTER_START_DISCOVERY";
    public static final String MTOP_RES_PARSE_FINISH = "EVENT_ERROR_PARSE";
    public static final String MTOP_RES_RECV_END = "EVENT_FG_2_BG";
    public static final String MTOP_RES_RECV_START = "EVENT_FIRST_FRAME";
    public static final String MTOP_RETRY_START = "EVENT_DEFAULT";
    public static final String NETWORK_FETCH_START = "Failed to update tpl ";
    public static final String NETWORK_FINISH = "Failed to write file, uuu:";
    public static final String NETWORK_RESPONSE_START = "FeatureNameSpace_uik_cellAnimatorFeature";
    public static final String NETWORK_RETRY_START = "FeatureNameSpace_uik_clickDrawableMaskFeature";
    public static final String NETWORK_TNET_RESPONSE_END = "MotionEffect_motionEffect_start";
    public static final String NETWORK_TNET_RESPONSE_START = "MotionEffect_motionEffect_strict";
    public static final String NETWORK_TNET_SEND_END = "MotionEffect_motionEffect_translationX";
    public static final String NETWORK_TNET_SEND_START = "MotionEffect_motionEffect_translationY";

}
