package android.taobao.windvane.extra.jsbridge.TBUploadService$2;
import tb.z6e;
import android.taobao.windvane.extra.jsbridge.TBUploadService;
import android.taobao.windvane.jsbridge.api.WVCamera$g;
import java.io.File;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.util.Map;
import java.util.HashMap;
import java.util.Iterator;
import org.json.JSONObject;

public class TBUploadService$2 implements z6e	// class@00019c from classes.dex
{
    public final TBUploadService this$0;
    public final WVCamera$g val$params;
    public final File val$tmpFile;
    public static IpChange $ipChange;

    public void TBUploadService$2(TBUploadService p0,WVCamera$g p1,File p2){
       this.this$0 = p0;
       this.val$params = p1;
       this.val$tmpFile = p2;
       super();
    }
    public String getBizType(){
       IpChange $ipChange = TBUploadService$2.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.val$params.e;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9c07dca2", objArray);
    }
    public String getFilePath(){
       IpChange $ipChange = TBUploadService$2.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.val$tmpFile.getAbsolutePath();
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1bcb7a22", objArray);
    }
    public String getFileType(){
       IpChange $ipChange = TBUploadService$2.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return ".jpg";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("105a7e2d", objArray);
    }
    public Map getMetaInfo(){
       IpChange $ipChange = TBUploadService$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("8d01c005", objArray);
       }else if(this.val$params.g == null){
          return null;
       }else {
          HashMap hashMap = new HashMap();
          Iterator iterator = this.val$params.g.keys();
          while (iterator.hasNext()) {
             String str = iterator.next();
             hashMap.put(str, this.val$params.g.optString(str));
          }
          return hashMap;
       }
    }
}
