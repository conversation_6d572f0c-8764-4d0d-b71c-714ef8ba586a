package android.taobao.windvane.extra.PrefetchInitTask$1;
import java.lang.Runnable;
import java.lang.String;
import android.app.Application;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Thread;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.net.Uri;
import android.taobao.windvane.extra.uc.WVPrefetchTrigger;
import android.content.Context;
import java.lang.Throwable;

public final class PrefetchInitTask$1 implements Runnable	// class@000184 from classes.dex
{
    public final Application val$application;
    public final String val$startupUrl;
    public static IpChange $ipChange;

    public void PrefetchInitTask$1(String p0,Application p1){
       this.val$startupUrl = p0;
       this.val$application = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = PrefetchInitTask$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          PrefetchInitTask$1 tval$startup = this.val$startupUrl;
          Thread.currentThread().setPriority(10);
          if (!TextUtils.isEmpty(this.val$startupUrl)) {
             String queryParamet = Uri.parse(this.val$startupUrl).getQueryParameter("u");
             if (!TextUtils.isEmpty(queryParamet) && Uri.parse(queryParamet).isHierarchical()) {
                tval$startup = queryParamet;
             }
          }
          WVPrefetchTrigger.getInstance().preloadMainHtml(this.val$application, tval$startup);
          return;
       }
    }
}
