package android.taobao.windvane.extra.launch.WVOptimizedStartup$3;
import java.lang.Runnable;
import android.taobao.windvane.extra.launch.WVOptimizedStartup$Params;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.os.SystemClock;
import tb.r9u;
import android.app.Application;
import android.taobao.windvane.extra.launch.WVOptimizedStartup;
import com.taobao.android.riverlogger.RVLLevel;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.lcn;
import tb.icn;
import java.lang.Long;

public final class WVOptimizedStartup$3 implements Runnable	// class@0001b7 from classes.dex
{
    public final WVOptimizedStartup$Params val$params;
    public static IpChange $ipChange;

    public void WVOptimizedStartup$3(WVOptimizedStartup$Params p0){
       this.val$params = p0;
       super();
    }
    public void run(){
       String str = "WindVane/PreStartUp";
       IpChange $ipChange = WVOptimizedStartup$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          long l = SystemClock.uptimeMillis();
          try{
             r9u.b("initConfig");
             WVOptimizedStartup.access$200(WVOptimizedStartup$Params.access$100(this.val$params));
             r9u.d();
          }catch(java.lang.Exception e3){
             lcn.f(RVLLevel.Error, str, "initConfig error"+e3.getMessage());
          }
          long l1 = SystemClock.uptimeMillis();
          l = l1 - l;
          r9u.b("initJSAPIAndEmbed");
          WVOptimizedStartup.access$300();
          r9u.d();
          long l2 = SystemClock.uptimeMillis() - l1;
          lcn.a(RVLLevel.Info, str).j("startup").a("configInitCost", Long.valueOf(l)).a("jsapiInitCost", Long.valueOf(l2)).f();
          return;
       }
    }
}
