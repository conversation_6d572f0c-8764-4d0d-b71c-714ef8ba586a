package android.taobao.windvane.extra.uc.DowngradableSSRService;
import tb.nnf;
import tb.t2o;
import com.alibaba.fastjson.JSONObject;
import java.lang.Object;
import tb.zeq;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.bgq;
import tb.mnf;
import android.os.Handler;
import java.lang.Boolean;
import android.taobao.windvane.extra.uc.DowngradableSSRService$1;

public class DowngradableSSRService implements nnf	// class@00020e from classes.dex
{
    private final nnf mService;
    public static IpChange $ipChange;
    private static final String MODULE;
    private static final JSONObject args;

    static {
       t2o.a(0x3d800141);
       DowngradableSSRService.args = new JSONObject();
    }
    public void DowngradableSSRService(){
       super();
       this.mService = zeq.a();
    }
    public static JSONObject access$000(){
       IpChange $ipChange = DowngradableSSRService.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return DowngradableSSRService.args;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("42811697", objArray);
    }
    public boolean asyncSend(bgq p0,mnf p1,Handler p2){
       IpChange $ipChange = DowngradableSSRService.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mService.asyncSend(p0, new DowngradableSSRService$1(this, p1), p2);
       }
       Object[] objArray = new Object[]{this,p0,p1,p2};
       return $ipChange.ipc$dispatch("ac8307d5", objArray).booleanValue();
    }
    public boolean cancel(bgq p0){
       IpChange $ipChange = DowngradableSSRService.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mService.cancel(p0);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("801a6ac3", objArray).booleanValue();
    }
}
