package android.taobao.windvane.extra.uc.AliNetworkHostingService$OldRequestType;

public interface abstract AliNetworkHostingService$OldRequestType	// class@000207 from classes.dex
{
    public static final int REQUEST_CSS = 3;
    public static final int REQUEST_FAVICON = 13;
    public static final int REQUEST_FONTRESOURCE = 5;
    public static final int REQUEST_IMAGE = 6;
    public static final int REQUEST_JS = 4;
    public static final int REQUEST_MAINFRAME = 0;
    public static final int REQUEST_MEDIA = 8;
    public static final int REQUEST_OBJECT = 7;
    public static final int REQUEST_PREFENDER = 12;
    public static final int REQUEST_PREFETCH = 11;
    public static final int REQUEST_SHAREDWORKER = 10;
    public static final int REQUEST_SUBFRAME = 1;
    public static final int REQUEST_SUBRESOURCE = 2;
    public static final int REQUEST_TEXTTRACK = 15;
    public static final int REQUEST_UNSPECIFIED = 16;
    public static final int REQUEST_WORKER = 9;
    public static final int REQUEST_XHR = 14;

}
