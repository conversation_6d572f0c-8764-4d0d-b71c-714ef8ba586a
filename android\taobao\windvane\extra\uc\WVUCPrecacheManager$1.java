package android.taobao.windvane.extra.uc.WVUCPrecacheManager$1;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.WVUCPrecacheManager;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;

public class WVUCPrecacheManager$1 implements Runnable	// class@00023b from classes.dex
{
    public final WVUCPrecacheManager this$0;
    public final String val$resUrl;
    public static IpChange $ipChange;

    public void WVUCPrecacheManager$1(WVUCPrecacheManager p0,String p1){
       this.this$0 = p0;
       this.val$resUrl = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = WVUCPrecacheManager$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          this.this$0.clearPrecacheDoc(this.val$resUrl);
          return;
       }
    }
}
