package android.taobao.windvane.extra.launch.WindVaneIdleTask$1;
import tb.yt4;
import android.taobao.windvane.extra.launch.WindVaneIdleTask;
import android.app.Application;
import java.util.HashMap;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;

public class WindVaneIdleTask$1 extends yt4	// class@0001bb from classes.dex
{
    public final WindVaneIdleTask this$0;
    public final Application val$application;
    public final HashMap val$params;
    public static IpChange $ipChange;

    public void WindVaneIdleTask$1(WindVaneIdleTask p0,Application p1,HashMap p2){
       this.this$0 = p0;
       this.val$application = p1;
       this.val$params = p2;
       super();
    }
    public static Object ipc$super(WindVaneIdleTask$1 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/launch/WindVaneIdleTask$1");
    }
    public void onUCCorePrepared(){
       IpChange $ipChange = WindVaneIdleTask$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("6e1aa650", objArray);
          return;
       }else {
          WindVaneIdleTask.access$000(this.this$0, this.val$application, this.val$params);
          return;
       }
    }
}
