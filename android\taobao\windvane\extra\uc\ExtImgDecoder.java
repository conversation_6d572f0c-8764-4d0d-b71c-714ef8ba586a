package android.taobao.windvane.extra.uc.ExtImgDecoder;
import tb.t2o;
import java.util.concurrent.atomic.AtomicBoolean;
import java.lang.Object;
import android.taobao.windvane.extra.uc.ExtImgDecoder$DecoderListener;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;
import java.lang.Boolean;
import java.lang.Throwable;
import android.content.Context;
import java.lang.StringBuilder;
import tb.vpw;
import tb.wpw;
import tb.v7t;
import com.uc.webview.export.extension.SettingKeys;
import com.uc.webview.export.extension.GlobalSettings;
import tb.dbn;
import tb.bzn;
import tb.b79;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.taobao.windvane.extra.core.WVCore;
import android.content.pm.ApplicationInfo;
import java.io.File;
import com.uc.webview.export.extension.ExtImageDecoder$ExtImageDecoderParam;
import com.uc.webview.export.extension.ExtImageDecoder$ImageDecoderListener;
import com.uc.webview.export.extension.ExtImageDecoder;
import tb.x74;

public class ExtImgDecoder	// class@000211 from classes.dex
{
    private ExtImgDecoder$DecoderListener heicDecodeListener;
    private boolean mUseAlphaChannelDecoder;
    private ExtImgDecoder$DecoderListener mifDecodeListener;
    public static IpChange $ipChange;
    private static final String TAG;
    private static final AtomicBoolean inited;
    private static int sDecodeErrorCount;
    private static ExtImgDecoder sInstance;
    private static boolean sUcDecoderEnable;

    static {
       t2o.a(0x3d800145);
       ExtImgDecoder.sUcDecoderEnable = true;
       ExtImgDecoder.inited = new AtomicBoolean(false);
       ExtImgDecoder.sDecodeErrorCount = 0;
    }
    public void ExtImgDecoder(){
       super();
       this.heicDecodeListener = new ExtImgDecoder$DecoderListener(this, "ftypheic");
       this.mifDecodeListener = new ExtImgDecoder$DecoderListener(this, "ftypmif1");
    }
    public static int getDecodeErrorCount(){
       IpChange $ipChange = ExtImgDecoder.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return ExtImgDecoder.sDecodeErrorCount;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("7fe4eee5", objArray).intValue();
    }
    public static ExtImgDecoder getInstance(){
       IpChange $ipChange = ExtImgDecoder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("d93cbaf", objArray);
       }else if(ExtImgDecoder.sInstance == null){
          ExtImgDecoder uExtImgDecod = ExtImgDecoder.class;
          _monitor_enter(uExtImgDecod);
          if (ExtImgDecoder.sInstance == null) {
             ExtImgDecoder.sInstance = new ExtImgDecoder();
          }
          _monitor_exit(uExtImgDecod);
       }
       return ExtImgDecoder.sInstance;
    }
    private boolean isExtImgDecoderSuccess(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = ExtImgDecoder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("d7ac64f6", objArray).booleanValue();
       }else if(ExtImgDecoder$DecoderListener.access$000(this.heicDecodeListener) && ExtImgDecoder$DecoderListener.access$000(this.mifDecodeListener)){
          i = true;
       }
       return i;
    }
    public static void markDecodeError(){
       IpChange $ipChange = ExtImgDecoder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("e33d89b4", objArray);
          return;
       }else {
          ExtImgDecoder.sDecodeErrorCount = ExtImgDecoder.sDecodeErrorCount + 1;
          return;
       }
    }
    public boolean canExtImgDecoder(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = ExtImgDecoder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("e361f99d", objArray).booleanValue();
       }else {
          boolean b = this.isExtImgDecoderEnable();
          if (b && this.isExtImgDecoderSuccess()) {
             i = true;
          }
          return i;
       }
    }
    public void init(Context p0){
       int i = 1;
       IpChange $ipChange = ExtImgDecoder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("609fd211", objArray);
          return;
       }else if(ExtImgDecoder.inited.compareAndSet(0, i)){
          this.initInternal(p0);
       }
       return;
    }
    public void initDecoderSwitch(Context p0){
       String str = "initDocederSwitch enable:";
       IpChange $ipChange = ExtImgDecoder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f9ae24b9", objArray);
          return;
       }else {
          v7t.d("ExtImgDecoder", str+vpw.commonConfig.H);
          ExtImgDecoder.sUcDecoderEnable = this.isExtImgDecoderEnable();
          GlobalSettings.set(SettingKeys.ExtImgDecoderOn, ExtImgDecoder.sUcDecoderEnable);
          return;
       }
    }
    public void initInternal(Context p0){
       int i = 1;
       String str = "heic";
       String str1 = "0.1.0";
       String str2 = "!! error ";
       String str3 = "supportAlphaChannel: ";
       IpChange $ipChange = ExtImgDecoder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("db22054", objArray);
          return;
       }else {
          v7t.d("ExtImgDecoder", "init");
          if (!this.isExtImgDecoderEnable()) {
             v7t.d("ExtImgDecoder", "NOT enableExtImgDecoder");
             if (this.isExtImgDecoderSuccess()) {
                v7t.d("ExtImgDecoder", "Close image decoder");
                GlobalSettings.set(SettingKeys.ExtImgDecoderOn, 0);
             }
             return;
          }else if(!ExtImgDecoder.sUcDecoderEnable){
             v7t.d("ExtImgDecoder", "ucDecoder not Enable, abort");
             return;
          }else if(this.isExtImgDecoderSuccess()){
             v7t.d("ExtImgDecoder", "setExtImageDecoderSuccessed, abort");
             return;
          }else {
             String str4 = "ucheif";
             wpw commonConfig = vpw.commonConfig;
             if (commonConfig.s1 != null) {
                str4 = (commonConfig.m3 != null)? "ucheif_alpha1": "ucheif_alpha";
                this.mUseAlphaChannelDecoder = i;
             }
             v7t.d("ExtImgDecoder", str3+this.mUseAlphaChannelDecoder+" soName: "+str4);
             str3 = bzn.b().d(str4).d();
             if (TextUtils.isEmpty(str3)) {
                ExtImgDecoder.inited.set(0);
                v7t.d("ExtImgDecoder", "so don\'t exist");
                return;
             }else if(!WVCore.getInstance().isUCSupport()){
                ExtImgDecoder.inited.set(0);
                v7t.d("ExtImgDecoder", "uc core not ready");
                return;
             }else {
                String[] stringArray = new String[]{p0.getApplicationInfo().nativeLibraryDir+"/libc++_shared.so"};
                if (new File(str3).exists()) {
                   ExtImageDecoder$ExtImageDecoderParam uExtImageDec = new ExtImageDecoder$ExtImageDecoderParam();
                   uExtImageDec.format = "ftypheic";
                   uExtImageDec.version = str1;
                   uExtImageDec.filenameExtension = str;
                   uExtImageDec.headerLength = 20;
                   uExtImageDec.progressiveDecode = i;
                   uExtImageDec.mime = "image/heic";
                   uExtImageDec.sniffOffset = 4;
                   uExtImageDec.decoderPath = str3;
                   uExtImageDec.dependedPath = stringArray;
                   ExtImageDecoder$ExtImageDecoderParam uExtImageDec1 = new ExtImageDecoder$ExtImageDecoderParam();
                   uExtImageDec1.format = "ftypmif1";
                   uExtImageDec1.version = str1;
                   uExtImageDec1.filenameExtension = str;
                   uExtImageDec1.headerLength = 20;
                   uExtImageDec1.progressiveDecode = i;
                   uExtImageDec1.mime = "image/heif";
                   uExtImageDec1.sniffOffset = 4;
                   uExtImageDec1.decoderPath = str3;
                   uExtImageDec1.dependedPath = stringArray;
                   ExtImageDecoder.setExtImageDecoder(uExtImageDec, this.heicDecodeListener);
                   ExtImageDecoder.setExtImageDecoder(uExtImageDec1, this.mifDecodeListener);
                   v7t.d("ExtImgDecoder", "setExtImageDecoder over");
                }else {
                   ExtImgDecoder.inited.set(0);
                   v7t.d("ExtImgDecoder", str2+str3);
                }
                return;
             }
          }
       }
    }
    public boolean isExchangeImgUrlEnable(){
       int i = 1;
       IpChange $ipChange = ExtImgDecoder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          return $ipChange.ipc$dispatch("d48b9fe7", objArray).booleanValue();
       }else {
          vpw.b();
          wpw i1 = vpw.commonConfig.I;
          if (i1 == null) {
             v7t.d("ExtImgDecoder", "enableExchangeImgUrl: "+i1);
          }
          return i1;
       }
    }
    public boolean isExtImgDecoderEnable(){
       int i = 1;
       IpChange $ipChange = ExtImgDecoder.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          return $ipChange.ipc$dispatch("69a7f9a6", objArray).booleanValue();
       }else {
          vpw.b();
          wpw h = vpw.commonConfig.H;
          if (h == null) {
             v7t.d("ExtImgDecoder", "isExtImgDecoderEnable: "+h);
          }
          return h;
       }
    }
    public boolean useAlphaChannelDecoder(){
       IpChange $ipChange = ExtImgDecoder.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mUseAlphaChannelDecoder;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("25034643", objArray).booleanValue();
    }
}
