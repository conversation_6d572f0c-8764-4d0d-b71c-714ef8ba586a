package android.taobao.windvane.extra.uc.UCSetupService$4;
import com.uc.webview.export.extension.UCPlayer$UpdaterClient;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.io.File;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import com.uc.webview.export.extension.U4Engine$IDownloadHandle;
import java.lang.Boolean;
import android.taobao.windvane.extra.uc.UCSetupService$DownloadController;
import com.uc.webview.base.UCKnownException;

public final class UCSetupService$4 extends UCPlayer$UpdaterClient	// class@000226 from classes.dex
{
    public static IpChange $ipChange;

    public void UCSetupService$4(){
       super();
    }
    public static Object ipc$super(UCSetupService$4 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/UCSetupService$4");
    }
    public void onDownloadFinish(String p0,File p1){
       IpChange $ipChange = UCSetupService$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("6e75e042", objArray);
       }
       return;
    }
    public void onDownloadProgress(int p0){
       IpChange $ipChange = UCSetupService$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("49af8908", objArray);
       }
       return;
    }
    public boolean onDownloadStart(String p0,U4Engine$IDownloadHandle p1){
       IpChange $ipChange = UCSetupService$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("92f77ae7", objArray).booleanValue();
       }else if(UCSetupService$DownloadController.getInstance().shouldDelay()){
          UCSetupService$DownloadController.getInstance().delay(p1);
          return 0;
       }else {
          return 1;
       }
    }
    public void onFailed(UCKnownException p0){
       IpChange $ipChange = UCSetupService$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("cc3b7ee3", objArray);
       }
       return;
    }
    public void onSuccess(String p0){
       IpChange $ipChange = UCSetupService$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("37d948b5", objArray);
       }
       return;
    }
}
