package android.taobao.windvane.extra.crash.WVUTCrashCaughtListener$PageStartWVEventListener;
import tb.jqw;
import tb.t2o;
import android.taobao.windvane.extra.crash.WVUTCrashCaughtListener;
import java.lang.Object;
import tb.iqw;
import tb.kqw;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import java.util.LinkedList;
import java.lang.StringBuilder;
import tb.v7t;

public class WVUTCrashCaughtListener$PageStartWVEventListener implements jqw	// class@000190 from classes.dex
{
    public final WVUTCrashCaughtListener this$0;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000c6);
       t2o.a(0x3d80029a);
    }
    public void WVUTCrashCaughtListener$PageStartWVEventListener(WVUTCrashCaughtListener p0){
       this.this$0 = p0;
       super();
    }
    public kqw onEvent(int p0,iqw p1,Object[] p2){
       iqw b;
       IpChange $ipChange = WVUTCrashCaughtListener$PageStartWVEventListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1,p2};
          return $ipChange.ipc$dispatch("75ee5a2a", objArray);
       }else if(p0 != 1001){
          switch (p0){
              case 3001:
              case 3003:
                WVUTCrashCaughtListener.wv_currentStatus = "1";
                break;
              case 3002:
                WVUTCrashCaughtListener.wv_currentStatus = "0";
                break;
              default:
          }
       }else if(p1 != null && (b = p1.b) != null){
          if (WVUTCrashCaughtListener.access$000(this.this$0) != null) {
             if (WVUTCrashCaughtListener.access$000(this.this$0).size() > 9) {
                WVUTCrashCaughtListener.access$000(this.this$0).removeFirst();
             }
             WVUTCrashCaughtListener.access$000(this.this$0).addLast(b);
          }
          WVUTCrashCaughtListener.access$102(this.this$0, b);
          v7t.l("WV_URL_CHANGE", "current Url : "+b);
       }
       WVUTCrashCaughtListener.wv_currentStatus = "2";
       return null;
    }
}
