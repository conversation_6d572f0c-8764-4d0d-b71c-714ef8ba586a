package android.taobao.windvane.export.network.b$c;
import tb.esd;
import android.taobao.windvane.export.network.Request;
import java.lang.Object;
import com.taobao.android.riverlogger.RVLLevel;
import java.lang.String;
import tb.icn;
import tb.lcn;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Long;
import java.lang.Integer;

public final class b$c implements esd	// class@000173 from classes.dex
{
    public long a;
    public final icn b;
    public int c;
    public final esd d;
    public final Request e;
    public static IpChange $ipChange;

    public void b$c(esd p0,Request p1){
       super();
       this.d = p0;
       this.e = p1;
       this.b = lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("prefetchTiming");
       this.c = 0;
    }
    public void recordProperty(String p0,Object p1){
       b$c td;
       IpChange $ipChange = b$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("4faa9773", objArray);
          return;
       }else if((td = this.d) != null){
          td.recordProperty(p0, p1);
       }
       return;
    }
    public void recordStage(String p0,long p1){
       b$c td;
       IpChange $ipChange = b$c.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("6a661a86", objArray);
          return;
       }else if((td = this.d) != null){
          td.recordStage(p0, p1);
       }
       if ("documentRequestStart".equals(p0)) {
          this.a = p1;
       }else {
          this.b.a(p0, Long.valueOf((p1 - this.a)));
       }
       if ("documentRequestEnd".equals(p0) || "documentPrefetchHitTime".equals(p0)) {
          this.c = this.c + 1;
       }
       try{
          if (this.c == 2) {
             b$c tb = this.b;
             String str = "type";
             String str1 = (this.d == null)? "system": "uc";
             tb.a(str, str1).a("requestId", Integer.valueOf(this.e.h())).a("url", this.e.j()).f();
          }
          return;
       }catch(java.lang.Exception e0){
       }
    }
}
