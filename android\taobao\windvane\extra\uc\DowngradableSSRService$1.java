package android.taobao.windvane.extra.uc.DowngradableSSRService$1;
import tb.mnf;
import android.taobao.windvane.extra.uc.DowngradableSSRService;
import java.lang.Object;
import tb.bgq;
import tb.egq;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.alibaba.fastjson.JSONObject;
import java.lang.Integer;
import com.alibaba.fastjson.JSON;
import tb.y71;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import java.util.Map;
import anetwork.channel.entity.RequestImpl;
import anetwork.channel.Request;
import android.taobao.windvane.extra.uc.AliRequestAdapter;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.util.HashMap;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import anetwork.channel.degrade.DegradableNetwork;
import android.app.Application;
import com.taobao.tao.Globals;
import android.content.Context;
import android.taobao.windvane.extra.uc.DowngradableSSRService$1$1;
import android.os.Handler;
import anetwork.channel.NetworkListener;
import java.util.concurrent.Future;
import anetwork.channel.aidl.adapter.NetworkProxy;

public class DowngradableSSRService$1 implements mnf	// class@00020d from classes.dex
{
    public final DowngradableSSRService this$0;
    public final mnf val$callback;
    public static IpChange $ipChange;

    public void DowngradableSSRService$1(DowngradableSSRService p0,mnf p1){
       this.this$0 = p0;
       this.val$callback = p1;
       super();
    }
    public void onError(bgq p0,egq p1){
       String str;
       String str1;
       bgq c;
       DowngradableSSRService$1 tval$callbac;
       int i = 0;
       IpChange $ipChange = DowngradableSSRService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("7a95834", objArray);
          return;
       }else {
          try{
             str = "DowngradableSSRService";
             str1 = "mtopSSRFinish";
             if (p1 != null && p0 != null) {
                DowngradableSSRService.access$000().put("errorCode", Integer.valueOf(p1.a));
                DowngradableSSRService.access$000().put("errorMsg", p1.c);
                y71.commitFail(str1, p1.a, DowngradableSSRService.access$000().toJSONString(), p0.a);
             }
             lcn.a(RVLLevel.Error, str).j(str1).b(DowngradableSSRService.access$000()).f();
          }catch(java.lang.Exception e0){
          }
          if (p0 != null && (p1 != null && p1.a == 417)) {
             JSONObject jSONObject = new JSONObject();
             jSONObject.put("url", p0.a);
             y71.commitSuccess("mtopSSRDowngrade", jSONObject.toJSONString());
             lcn.f(RVLLevel.Error, str, "downgrade network to normal request");
             RequestImpl requestImpl = new RequestImpl(p0.a);
             requestImpl.setFollowRedirects(e0);
             requestImpl.setRetryTime(AliRequestAdapter.retryTimes);
             requestImpl.setConnectTimeout(AliRequestAdapter.connectTimeout);
             requestImpl.setReadTimeout(AliRequestAdapter.readTimeout);
             requestImpl.setCookieEnabled(WVUCWebView.isNeedCookie(p0.a));
             requestImpl.setMethod(p0.b);
             if ((c = p0.c) != null) {
                Iterator iterator = c.entrySet().iterator();
                while (iterator.hasNext()) {
                   Map$Entry uEntry = iterator.next();
                   str1 = uEntry.getKey();
                   requestImpl.addHeader(str1, uEntry.getValue());
                }
             }
             new DegradableNetwork(Globals.getApplication()).asyncSend(requestImpl, p0.a, null, new DowngradableSSRService$1$1(this, p0));
             return;
          }else if((tval$callbac = this.val$callback) != null){
             tval$callbac.onError(p0, p1);
          }
          return;
       }
    }
    public void onFinish(bgq p0){
       DowngradableSSRService$1 tval$callbac;
       IpChange $ipChange = DowngradableSSRService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4d53f97", objArray);
          return;
       }else if((tval$callbac = this.val$callback) != null){
          tval$callbac.onFinish(p0);
       }
       try{
          String str = "mtopSSRFinish";
          if (p0 != null) {
             y71.commitSuccess(str, p0.a);
          }
          lcn.a(RVLLevel.Error, "DowngradableSSRService").j(str).b(DowngradableSSRService.access$000()).f();
          return;
       }catch(java.lang.Exception e0){
       }
    }
    public void onReceiveData(bgq p0,byte[] p1){
       DowngradableSSRService$1 tval$callbac;
       IpChange $ipChange = DowngradableSSRService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("150b5e1a", objArray);
          return;
       }else if((tval$callbac = this.val$callback) != null){
          tval$callbac.onReceiveData(p0, p1);
       }
       return;
    }
    public void onResponse(bgq p0,int p1,Map p2){
       DowngradableSSRService$1 tval$callbac;
       IpChange $ipChange = DowngradableSSRService$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
          $ipChange.ipc$dispatch("1e09d3a7", objArray);
          return;
       }else if((tval$callbac = this.val$callback) != null){
          tval$callbac.onResponse(p0, p1, p2);
       }
       try{
          DowngradableSSRService.access$000().put("responseCode", Integer.valueOf(p1));
          return;
       }catch(java.lang.Exception e0){
       }
    }
}
