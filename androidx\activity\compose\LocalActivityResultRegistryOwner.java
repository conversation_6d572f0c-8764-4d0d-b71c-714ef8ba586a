package androidx.activity.compose.LocalActivityResultRegistryOwner;
import androidx.activity.compose.LocalActivityResultRegistryOwner$LocalComposition$1;
import tb.le40;
import tb.d1a;
import java.lang.Object;
import tb.v140;
import androidx.compose.runtime.CompositionLocalKt;
import androidx.compose.runtime.a;
import androidx.activity.result.ActivityResultRegistryOwner;
import tb.oa20;
import androidx.compose.ui.platform.AndroidCompositionLocals_androidKt;
import android.content.Context;
import android.content.ContextWrapper;
import tb.x140;

public final class LocalActivityResultRegistryOwner	// class@000484 from classes.dex
{
    public static final int $stable;
    public static final LocalActivityResultRegistryOwner INSTANCE;
    private static final v140 LocalComposition;

    static {
       LocalActivityResultRegistryOwner.INSTANCE = new LocalActivityResultRegistryOwner();
       LocalActivityResultRegistryOwner.LocalComposition = CompositionLocalKt.e(null, LocalActivityResultRegistryOwner$LocalComposition$1.INSTANCE, 1, null);
    }
    private void LocalActivityResultRegistryOwner(){
       super();
    }
    public final ActivityResultRegistryOwner getCurrent(a p0,int p1){
       ActivityResultRegistryOwner uActivityRes;
       p0.F(0x548547d7);
       if ((uActivityRes = p0.d(LocalActivityResultRegistryOwner.LocalComposition)) == null) {
          uActivityRes = p0.d(AndroidCompositionLocals_androidKt.g());
          while (true) {
             if (uActivityRes instanceof ContextWrapper) {
                if (uActivityRes instanceof ActivityResultRegistryOwner) {
                   break ;
                }else {
                   uActivityRes = uActivityRes.getBaseContext();
                }
             }else {
                uActivityRes = null;
             }
          }
       }
       p0.K();
       return uActivityRes;
    }
    public final x140 provides(ActivityResultRegistryOwner p0){
       return LocalActivityResultRegistryOwner.LocalComposition.d(p0);
    }
}
