package android.taobao.windvane.extra.storage.strategy.FccStrategy$Condition;
import tb.t2o;
import java.lang.String;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionOperation;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValue;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.util.Map;
import android.taobao.windvane.extra.storage.strategy.FccStrategy;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionOperator;

public class FccStrategy$Condition	// class@0001ee from classes.dex
{
    public Map environment;
    public String expression;
    public FccStrategy$ConditionOperation operation;
    public FccStrategy$ConditionValue value;
    public String variable;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800124);
    }
    public void FccStrategy$Condition(String p0,FccStrategy$ConditionOperation p1,FccStrategy$ConditionValue p2,String p3){
       super();
       this.variable = p0;
       this.operation = p1;
       this.value = p2;
       this.expression = p3;
    }
    public boolean checkValid(){
       FccStrategy$Condition tvalue;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = FccStrategy$Condition.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("2b6d1a5f", objArray).booleanValue();
       }else if(TextUtils.isEmpty(this.variable)){
          return i;
       }else if(!FccStrategy.access$000().containsKey(this.operation)){
          return i;
       }else if((tvalue = this.value) != null && tvalue.checkValid()){
          i = true;
       }
       return i;
    }
    public boolean evaluate(){
       FccStrategy$Condition tenvironment;
       Object obj;
       FccStrategy$ConditionOperator uConditionOp;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = FccStrategy$Condition.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("17f03ca4", objArray).booleanValue();
       }else if((tenvironment = this.environment) == null){
          return i;
       }else if((obj = tenvironment.get(this.variable)) == null){
          return i;
       }else if((uConditionOp = FccStrategy.access$000().get(this.operation)) != null && uConditionOp.evaluate(this.value, obj)){
          i = true;
       }
       return i;
    }
    public void setEnvironment(Map p0){
       IpChange $ipChange = FccStrategy$Condition.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3928402d", objArray);
          return;
       }else {
          this.environment = p0;
          return;
       }
    }
    public void setExpression(String p0){
       IpChange $ipChange = FccStrategy$Condition.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("551003ab", objArray);
          return;
       }else {
          this.expression = p0;
          return;
       }
    }
}
