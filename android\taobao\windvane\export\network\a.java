package android.taobao.windvane.export.network.a;
import tb.t2o;
import java.lang.Object;
import android.taobao.windvane.export.network.Request;
import android.taobao.windvane.export.network.RequestCallback;
import android.os.Handler;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.uc.prefetch.UCDefaultUserAgent;
import tb.vpw;
import tb.wpw;
import android.webkit.CookieManager;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import java.lang.Integer;
import com.uc.webview.export.CookieManager;
import tb.rsa;
import android.taobao.windvane.export.network.a$a;
import java.lang.Runnable;
import java.lang.CharSequence;
import tb.y8o;
import java.util.Map;
import com.taobao.zcache.ResourceResponse;
import tb.yox;
import com.taobao.zcache.Error;
import tb.ikd;
import java.util.HashMap;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import java.util.ArrayList;
import tb.jfq;
import tb.c7j;
import tb.ffs;
import java.lang.Boolean;

public class a	// class@000170 from classes.dex
{
    public static IpChange $ipChange;
    public static final String ACCEPTENCODING;
    public static final String ACCEPTLANGSTR;
    public static final String ACCEPTSTR;

    static {
       t2o.a(0x3d800080);
       t2o.a(0x3d800079);
    }
    public void a(){
       super();
    }
    public static void a(a p0,Request p1,RequestCallback p2,Handler p3){
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2,p3};
          $ipChange.ipc$dispatch("adcd4d44", objArray);
          return;
       }else {
          p0.d(p1, p2, p3);
          return;
       }
    }
    public final void b(Request p0){
       int i = 1;
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d3635de5", objArray);
          return;
       }else {
          p0.a("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8");
          p0.a("Accept-Encoding", "gzip, deflate");
          p0.a("Accept-Language", "zh-CN,zh;q=0.9,en-CN;q=0.8,en-US;q=0.7,en;q=0.6;");
          p0.a("User-Agent", UCDefaultUserAgent.VALUE);
          String cookie = (i == vpw.commonConfig.g0)? CookieManager.getInstance().getCookie(p0.j()): CookieManager.getInstance().getCookie(p0.j());
          if (cookie != null) {
             p0.a("Cookie", cookie);
          }
          return;
       }
    }
    public void c(Request p0,RequestCallback p1,Handler p2){
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("71150c06", objArray);
          return;
       }else if(p2 == null){
          p2 = rsa.b().a();
       }
       if (p2 == null) {
          this.d(p0, p1, null);
       }else {
          p2.post(new a$a(this, p0, p1, p2));
       }
       return;
    }
    public final void d(Request p0,RequestCallback p1,Handler p2){
       ResourceResponse resourceResp;
       boolean b;
       ikd oikd;
       IpChange $ipChange = a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("d11b3046", objArray);
          return;
       }else {
          p0.l("documentRequestThreadSwitched");
          this.b(p0);
          p0.l("documentRequestHeaderAdded");
          String str = p0.j();
          if (vpw.commonConfig.s2 != null && !str.contains("prefetch-disable-zcache=true")) {
             try{
                if ((resourceResp = yox.h(new y8o(str, p0.c()))) != null && (resourceResp.getError() == null && resourceResp.getData() != null)) {
                   if ((oikd = p0.i()) != null && !oikd.a(resourceResp.getHeader())) {
                      lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("zcacheInvalid").a("requestId", Integer.valueOf(p0.h())).f();
                   }else {
                      HashMap hashMap = new HashMap();
                      Iterator iterator = resourceResp.getHeader().entrySet().iterator();
                      while (iterator.hasNext()) {
                         Map$Entry uEntry = iterator.next();
                         ArrayList uArrayList = new ArrayList();
                         uArrayList.add(uEntry.getValue());
                         hashMap.put(uEntry.getKey(), uArrayList);
                      }
                      p1.onResponse(200, hashMap);
                      p1.onNetworkResponse(200, hashMap);
                      p1.onReceiveData(resourceResp.getData());
                      p1.onFinish();
                      lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("zcacheHit").a("requestId", Integer.valueOf(p0.h())).f();
                      return;
                   }
                }
             }catch(java.lang.Exception e1){
                lcn.a(RVLLevel.Error, "Themis/Performance/RequestPrefetch").j("zcacheError").a("requestId", Integer.valueOf(p0.h())).a("e", e1).f();
             }
          }else {
             lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("zcacheDisable").a("requestId", Integer.valueOf(p0.h())).f();
          }
          p0.l("documentRequestZCacheRead");
          if (b = jfq.a(str)) {
             new c7j().a(p0, p1, p2);
          }else {
             new ffs().a(p0, p1, null);
          }
          p0.l("documentRequestSent");
          lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("requestSent").a("isMtopSSRRequest", Boolean.valueOf(b)).a("requestId", Integer.valueOf(p0.h())).a("headers", p0.c()).f();
          return;
       }
    }
}
