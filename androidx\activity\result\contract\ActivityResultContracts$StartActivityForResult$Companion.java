package androidx.activity.result.contract.ActivityResultContracts$StartActivityForResult$Companion;
import java.lang.Object;
import tb.a07;

public final class ActivityResultContracts$StartActivityForResult$Companion	// class@0004db from classes.dex
{

    private void ActivityResultContracts$StartActivityForResult$Companion(){
       super();
    }
    public void ActivityResultContracts$StartActivityForResult$Companion(a07 p0){
       super();
    }
}
