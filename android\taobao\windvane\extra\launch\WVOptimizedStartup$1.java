package android.taobao.windvane.extra.launch.WVOptimizedStartup$1;
import java.lang.Runnable;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.r9u;
import android.taobao.windvane.extra.launch.WVOptimizedStartup;

public final class WVOptimizedStartup$1 implements Runnable	// class@0001b5 from classes.dex
{
    public static IpChange $ipChange;

    public void WVOptimizedStartup$1(){
       super();
    }
    public void run(){
       IpChange $ipChange = WVOptimizedStartup$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          r9u.b("disableVerifyClass");
          WVOptimizedStartup.access$000();
          r9u.d();
          return;
       }
    }
}
