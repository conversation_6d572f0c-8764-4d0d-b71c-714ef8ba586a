package android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$e;
import java.lang.Runnable;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$d;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;

public final class MemoryResWarmupManager$e implements Runnable	// class@00015d from classes.dex
{
    public final MemoryResWarmupManager$d a;
    public final boolean b;
    public final String c;
    public static IpChange $ipChange;

    public void MemoryResWarmupManager$e(MemoryResWarmupManager$d p0,boolean p1,String p2){
       this.a = p0;
       this.b = p1;
       this.c = p2;
       super();
    }
    public void run(){
       IpChange $ipChange = MemoryResWarmupManager$e.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          this.a.a(this.b, this.c);
          return;
       }
    }
}
