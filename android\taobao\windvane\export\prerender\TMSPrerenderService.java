package android.taobao.windvane.export.prerender.TMSPrerenderService;
import tb.t2o;
import java.util.concurrent.atomic.AtomicBoolean;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import android.taobao.windvane.export.prerender.TMSPrerenderModel;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import com.taobao.orange.OrangeConfig;
import android.taobao.windvane.export.prerender.TMSPrerenderService$setup$1;
import tb.obk;
import tb.g1a;
import tb.ckf;
import java.lang.Boolean;
import java.util.List;
import java.lang.Iterable;
import java.util.Iterator;
import android.taobao.windvane.export.prerender.TMSPrerenderTaskModel;
import com.alibaba.fastjson.JSON;
import tb.xum$a;
import java.lang.Long;
import tb.xum;
import android.taobao.windvane.export.prerender.PrerenderManager;
import tb.xhv;
import kotlin.Result;
import java.lang.Throwable;
import kotlin.b;

public final class TMSPrerenderService	// class@000180 from classes.dex
{
    public static IpChange $ipChange;
    public static final TMSPrerenderService INSTANCE;
    public static final AtomicBoolean a;
    public static String b;
    public static TMSPrerenderModel c;

    static {
       t2o.a(0x3d8000aa);
       TMSPrerenderService.INSTANCE = new TMSPrerenderService();
       TMSPrerenderService.a = new AtomicBoolean(false);
    }
    public void TMSPrerenderService(){
       super();
    }
    public static final String a(TMSPrerenderService p0){
       IpChange $ipChange = TMSPrerenderService.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return TMSPrerenderService.b;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("31ece9bb", objArray);
    }
    public static final TMSPrerenderModel b(TMSPrerenderService p0){
       IpChange $ipChange = TMSPrerenderService.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return TMSPrerenderService.c;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("6c529fbb", objArray);
    }
    public static final void c(TMSPrerenderService p0,String p1){
       IpChange $ipChange = TMSPrerenderService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("b273a91b", objArray);
          return;
       }else {
          TMSPrerenderService.b = p1;
          return;
       }
    }
    public static final void d(TMSPrerenderService p0,TMSPrerenderModel p1){
       IpChange $ipChange = TMSPrerenderService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("5135fd13", objArray);
          return;
       }else {
          TMSPrerenderService.c = p1;
          return;
       }
    }
    public final void e(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = TMSPrerenderService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          $ipChange.ipc$dispatch("5cce3884", objArray);
          return;
       }else if(TMSPrerenderService.a.compareAndSet(i, i1)){
          lcn.a(RVLLevel.Info, "Themis/Performance/Prerender").j("setup").f();
          String[] stringArray = new String[]{"themis_prerender_config"};
          OrangeConfig.getInstance().registerListener(stringArray, TMSPrerenderService$setup$1.INSTANCE, i1);
       }
       return;
    }
    public final void f(g1a p0){
       TMSPrerenderModel c;
       Object obj;
       Long delayNextTim;
       int i = 1;
       IpChange $ipChange = TMSPrerenderService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("5c0731af", objArray);
          return;
       }else {
          ckf.g(p0, "callback");
          if (!TMSPrerenderService.a.get()) {
             p0.invoke(Boolean.FALSE);
             return;
          }else if((c = TMSPrerenderService.c) != null){
             String str = "start";
             if (!c.getEnable()) {
                lcn.a(RVLLevel.Error, "Themis/Performance/Prerender").j(str).a("msg", "config.enable is false").f();
                p0.invoke(Boolean.FALSE);
                return;
             }else {
                Iterator iterator = c.getTasks().iterator();
                while (true) {
                   if (iterator.hasNext()) {
                      obj = iterator.next();
                      if (!c.isMatchCondition(obj)) {
                         continue ;
                      }
                   }else {
                      obj = null;
                   }
                   if (obj != null) {
                      lcn.a(RVLLevel.Error, "Themis/Performance/Prerender").j(str).a("config", JSON.toJSONString(obj)).f();
                      String url = obj.getUrl();
                      ckf.d(url);
                      xum$a uoa = new xum$a().f(url).c(i).e(obj.getSpmBVerifyValue());
                      long l = ((delayNextTim = obj.getDelayNextTime()) != null)? delayNextTim.longValue(): -1;
                      PrerenderManager.INSTANCE.c(uoa.b(l).a(), p0);
                      Result.constructor-impl(xhv.INSTANCE);
                      return;
                   }
                }
             }
          }
          p0.invoke(Boolean.FALSE);
          return;
       }
    }
}
