package android.taobao.windvane.connect.HttpConnector$HttpOverFlowException;
import java.lang.Exception;
import tb.t2o;
import android.taobao.windvane.connect.HttpConnector;
import java.lang.String;

public class HttpConnector$HttpOverFlowException extends Exception	// class@00014e from classes.dex
{
    public final HttpConnector this$0;

    static {
       t2o.a(0x3d80003c);
    }
    public void HttpConnector$HttpOverFlowException(HttpConnector p0,String p1){
       this.this$0 = p0;
       super(p1);
    }
}
