package android.taobao.windvane.extra.uc.WVUCWebViewClient$1;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.uc.WVUCWebViewClient;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import org.json.JSONObject;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Throwable;

public class WVUCWebViewClient$1 implements ValueCallback	// class@000263 from classes.dex
{
    public final WVUCWebViewClient this$0;
    public final WVUCWebView val$webview;
    public static IpChange $ipChange;

    public void WVUCWebViewClient$1(WVUCWebViewClient p0,WVUCWebView p1){
       this.this$0 = p0;
       this.val$webview = p1;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       JSONObject metaObject;
       IpChange $ipChange = WVUCWebViewClient$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else if((metaObject = WVUCWebViewClient.getMetaObject(p0)) != null && this.val$webview != null){
          metaObject = metaObject.optString("WV.Meta.Falco.PageName");
          if (!TextUtils.isEmpty(metaObject)) {
             this.val$webview.setFalcoPageName(metaObject);
          }
          return;
       }else {
          return;
       }
    }
}
