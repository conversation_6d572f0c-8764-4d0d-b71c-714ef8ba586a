package androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$fullyDrawnReporter$1;
import tb.u1a;
import kotlin.jvm.internal.Lambda;
import tb.d1a;
import java.lang.Object;
import androidx.compose.runtime.a;
import java.lang.Number;
import tb.xhv;
import androidx.activity.compose.ReportDrawnKt;

public final class ReportDrawnKt$ReportDrawnWhen$fullyDrawnReporter$1 extends Lambda implements u1a	// class@0004a0 from classes.dex
{
    public final int $$changed;
    public final d1a $predicate;

    public void ReportDrawnKt$ReportDrawnWhen$fullyDrawnReporter$1(d1a p0,int p1){
       this.$predicate = p0;
       this.$$changed = p1;
       super(2);
    }
    public Object invoke(Object p0,Object p1){
       this.invoke(p0, p1.intValue());
       return xhv.INSTANCE;
    }
    public final void invoke(a p0,int p1){
       ReportDrawnKt.ReportDrawnWhen(this.$predicate, p0, (this.$$changed | 0x01));
    }
}
