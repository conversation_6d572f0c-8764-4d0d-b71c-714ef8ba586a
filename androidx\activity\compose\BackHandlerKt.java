package androidx.activity.compose.BackHandlerKt;
import tb.d1a;
import androidx.compose.runtime.a;
import androidx.compose.runtime.ComposerImpl;
import java.lang.Object;
import tb.xf40;
import tb.me40;
import java.lang.Class;
import androidx.compose.runtime.a$a;
import androidx.activity.compose.BackHandlerKt$BackHandler$backCallback$1$1;
import androidx.activity.compose.BackHandlerKt$BackHandler$1$1;
import androidx.compose.runtime.EffectsKt;
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner;
import androidx.activity.OnBackPressedDispatcherOwner;
import androidx.activity.OnBackPressedDispatcher;
import tb.v140;
import androidx.compose.ui.platform.AndroidCompositionLocals_androidKt;
import tb.oa20;
import androidx.lifecycle.LifecycleOwner;
import androidx.activity.compose.BackHandlerKt$BackHandler$2$1;
import tb.g1a;
import tb.b940;
import androidx.activity.compose.BackHandlerKt$BackHandler$3;
import androidx.compose.runtime.RecomposeScopeImpl;
import tb.u1a;
import java.lang.IllegalStateException;
import java.lang.String;

public final class BackHandlerKt	// class@000481 from classes.dex
{

    public static final void BackHandler(boolean p0,d1a p1,a p2,int p3,int p4){
       int i;
       int i1;
       Object a;
       OnBackPressedDispatcherOwner current;
       b940 uob940;
       p2 = p2.B(-361453782);
       if (i = p4 & 0x01) {
          i1 = p3 | 0x06;
       }else if(!((p3 & 0x06))){
          i1 = (p2.a(p0))? 4: 2;
          i1 = i1 | p3;
       }else {
          i1 = p3;
       }
       if ((p4 & 0x02)) {
          i1 = i1 | 0x30;
       }else if(!((p3 & 0x30))){
          int i2 = (p2.y(p1))? 32: 16;
          i1 = i1 | i2;
       }
       if (((i1 & 0x13)) == 18) {
          a uoa = p2;
          if (uoa.a()) {
             uoa.e();
          label_00e9 :
             if ((uob940 = p2.p0()) != null) {
                uob940.I(new BackHandlerKt$BackHandler$3(p0, p1, p3, p4));
             }
             return;
          }
       }
       if (i) {
          p0 = true;
       }
       xf40 oxf40 = me40.o(p1, p2, ((i1 >> 3) & 0x0e));
       a uoa1 = p2;
       uoa1.F(-971159753);
       BackHandlerKt$BackHandler$backCallback$1$1 uBackHandler = uoa1.o();
       a.Companion.getClass();
       a = a$a.a;
       if (uBackHandler == a) {
          uBackHandler = new BackHandlerKt$BackHandler$backCallback$1$1(p0, oxf40);
          uoa1.E(uBackHandler);
       }
       uoa1.K();
       uoa1.F(-971159481);
       BackHandlerKt$BackHandler$1$1 uBackHandler1 = uoa1.o();
       if (((uoa1.y(uBackHandler) | uoa1.a(p0))) || uBackHandler1 == a) {
          uBackHandler1 = new BackHandlerKt$BackHandler$1$1(uBackHandler, p0);
          uoa1.E(uBackHandler1);
       }
       uoa1.K();
       i = 0;
       EffectsKt.i(uBackHandler1, p2, i);
       if ((current = LocalOnBackPressedDispatcherOwner.INSTANCE.getCurrent(p2, 6)) != null) {
          OnBackPressedDispatcher onBackPresse = current.getOnBackPressedDispatcher();
          LifecycleOwner lifecycleOwn = uoa1.d(AndroidCompositionLocals_androidKt.getLocalLifecycleOwner());
          uoa1.F(-971159120);
          BackHandlerKt$BackHandler$2$1 uBackHandler2 = uoa1.o();
          if ((((uoa1.y(onBackPresse) | uoa1.y(lifecycleOwn)) | uoa1.y(uBackHandler))) || uBackHandler2 == a) {
             uBackHandler2 = new BackHandlerKt$BackHandler$2$1(onBackPresse, lifecycleOwn, uBackHandler);
             uoa1.E(uBackHandler2);
          }
          uoa1.K();
          EffectsKt.b(lifecycleOwn, onBackPresse, uBackHandler2, p2, i);
          goto label_00e9 ;
       }else {
          throw new IllegalStateException("No OnBackPressedDispatcherOwner was provided via LocalOnBackPressedDispatcherOwner");
       }
    }
    private static final d1a BackHandler$lambda$0(xf40 p0){
       return p0.getValue();
    }
    public static final d1a access$BackHandler$lambda$0(xf40 p0){
       return BackHandlerKt.BackHandler$lambda$0(p0);
    }
}
