package android.taobao.windvane.extra.uc.WVUCWebView$TouchEventHandler;
import tb.t2o;
import java.lang.Object;
import android.view.MotionEvent;
import java.lang.Boolean;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Integer;

public class WVUCWebView$TouchEventHandler	// class@00025f from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800195);
    }
    public void WVUCWebView$TouchEventHandler(){
       super();
    }
    public Boolean coreDispatchTouchEvent(MotionEvent p0){
       IpChange $ipChange = WVUCWebView$TouchEventHandler.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("477e730d", objArray);
    }
    public Boolean coreOverScrollBy(int p0,int p1,int p2,int p3,int p4,int p5,int p6,int p7,boolean p8){
       IpChange $ipChange = WVUCWebView$TouchEventHandler.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return null;
       }
       Object[] objArray = new Object[10];
       objArray[0] = this;
       objArray[1] = new Integer(p0);
       objArray[2] = new Integer(p1);
       objArray[3] = new Integer(p2);
       objArray[4] = new Integer(p3);
       objArray[5] = new Integer(p4);
       objArray[6] = new Integer(p5);
       objArray[7] = new Integer(p6);
       objArray[8] = new Integer(p7);
       objArray[9] = new Boolean(p8);
       return $ipChange.ipc$dispatch("c848a3d3", objArray);
    }
}
