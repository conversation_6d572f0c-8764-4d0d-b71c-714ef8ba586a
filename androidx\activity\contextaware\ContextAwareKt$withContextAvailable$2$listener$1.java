package androidx.activity.contextaware.ContextAwareKt$withContextAvailable$2$listener$1;
import androidx.activity.contextaware.OnContextAvailableListener;
import tb.q23;
import tb.g1a;
import java.lang.Object;
import android.content.Context;
import java.lang.String;
import tb.ckf;
import kotlin.Result;
import java.lang.Throwable;
import kotlin.b;
import tb.ar4;

public final class ContextAwareKt$withContextAvailable$2$listener$1 implements OnContextAvailableListener	// class@0004a5 from classes.dex
{
    public final q23 $co;
    public final g1a $onContextAvailable;

    public void ContextAwareKt$withContextAvailable$2$listener$1(q23 p0,g1a p1){
       this.$co = p0;
       this.$onContextAvailable = p1;
       super();
    }
    public void onContextAvailable(Context p0){
       ckf.g(p0, "context");
       ContextAwareKt$withContextAvailable$2$listener$1 t$co = this.$co;
       p0 = Result.constructor-impl(this.$onContextAvailable.invoke(p0));
       t$co.resumeWith(p0);
       return;
    }
}
