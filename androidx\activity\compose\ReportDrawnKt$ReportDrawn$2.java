package androidx.activity.compose.ReportDrawnKt$ReportDrawn$2;
import tb.u1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import androidx.compose.runtime.a;
import java.lang.Number;
import tb.xhv;
import androidx.activity.compose.ReportDrawnKt;

public final class ReportDrawnKt$ReportDrawn$2 extends Lambda implements u1a	// class@000498 from classes.dex
{
    public final int $$changed;

    public void ReportDrawnKt$ReportDrawn$2(int p0){
       this.$$changed = p0;
       super(2);
    }
    public Object invoke(Object p0,Object p1){
       this.invoke(p0, p1.intValue());
       return xhv.INSTANCE;
    }
    public final void invoke(a p0,int p1){
       ReportDrawnKt.ReportDrawn(p0, (this.$$changed | 0x01));
    }
}
