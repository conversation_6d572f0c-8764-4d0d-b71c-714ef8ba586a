package android.taobao.windvane.extra.crash.WVUTCrashCaughtListener;
import com.taobao.android.tcrash.JvmUncaughtCrashListener;
import tb.t2o;
import java.lang.Object;
import java.util.LinkedList;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.lqw;
import android.taobao.windvane.extra.crash.WVUTCrashCaughtListener$PageStartWVEventListener;
import tb.jqw;
import java.lang.Thread;
import java.lang.Throwable;
import java.util.Map;
import java.util.HashMap;
import tb.bka;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.gtw;
import tb.aka;
import java.lang.StringBuilder;
import tb.v7t;
import com.uc.webview.export.Build$Version;
import com.uc.webview.export.Build;

public class WVUTCrashCaughtListener implements JvmUncaughtCrashListener	// class@000191 from classes.dex
{
    private LinkedList mUrlList;
    private String wv_currentUrl;
    public static IpChange $ipChange;
    public static String wv_currentStatus;

    static {
       t2o.a(0x3d8000c5);
       WVUTCrashCaughtListener.wv_currentStatus = "0";
    }
    public void WVUTCrashCaughtListener(){
       super();
       this.mUrlList = null;
       this.wv_currentUrl = "";
       this.init();
    }
    public static LinkedList access$000(WVUTCrashCaughtListener p0){
       IpChange $ipChange = WVUTCrashCaughtListener.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mUrlList;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("1c15c412", objArray);
    }
    public static String access$102(WVUTCrashCaughtListener p0,String p1){
       IpChange $ipChange = WVUTCrashCaughtListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("a024b73d", objArray);
       }else {
          p0.wv_currentUrl = p1;
          return p1;
       }
    }
    private void init(){
       IpChange $ipChange = WVUTCrashCaughtListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("fede197", objArray);
          return;
       }else {
          this.mUrlList = new LinkedList();
          lqw.d().b(new WVUTCrashCaughtListener$PageStartWVEventListener(this));
          return;
       }
    }
    public Map onJvmUncaughtCrash(Thread p0,Throwable p1){
       int i = 0;
       int i1 = 3;
       int i2 = 1;
       IpChange $ipChange = WVUTCrashCaughtListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          objArray[i2] = p0;
          objArray[2] = p1;
          return $ipChange.ipc$dispatch("d9b5b942", objArray);
       }else {
          int i3 = this.mUrlList.size();
          HashMap hashMap = new HashMap();
          if (this.mUrlList != null && i3 >= i2) {
             bka uobka = bka.c();
             while (i1 < i3) {
                String str = this.mUrlList.get(i1);
                if (!TextUtils.isEmpty(str)) {
                   this.mUrlList.set(i1, gtw.i(str));
                   aka uoaka = uobka.b(str);
                   if (!i && uoaka != null) {
                      v7t.a("WVUTCrashCaughtListener", "found grey page: ".append(str).toString());
                      hashMap.put("wxAirTag", uoaka.a());
                      i = 1;
                   }
                }
                i1 = i1 + i2;
             }
             hashMap.put("crash_url_list", this.mUrlList.toString());
             hashMap.put("wv_currentUrl", this.wv_currentUrl);
             hashMap.put("wv_currentStatus", WVUTCrashCaughtListener.wv_currentStatus);
             hashMap.put("uc_version_info", "ucbs "+Build$Version.NAME+"."+Build.TIME+"-impl "+Build.UCM_VERSION+"."+Build.CORE_TIME);
          }
          return hashMap;
       }
    }
}
