package android.taobao.windvane.extra.uc.WVUCWebViewFragment;
import androidx.fragment.app.Fragment;
import tb.t2o;
import java.lang.String;
import java.lang.Class;
import android.app.Activity;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.os.Bundle;
import android.taobao.windvane.extra.uc.WVUCWebView;
import com.android.alibaba.ip.runtime.IpChange;
import androidx.fragment.app.FragmentActivity;
import android.content.Context;
import android.taobao.windvane.extra.uc.WVUCWebViewClient;
import android.taobao.windvane.extra.uc.WVUCWebChromeClient;
import android.view.ViewGroup$LayoutParams;
import com.uc.webview.export.WebView;
import android.content.Intent;
import java.lang.Integer;
import java.lang.Boolean;
import android.os.BaseBundle;
import android.view.LayoutInflater;
import android.view.ViewGroup;
import android.view.View;
import tb.v7t;
import android.view.ViewParent;
import java.lang.Throwable;
import com.uc.webview.export.WebViewClient;
import com.uc.webview.export.WebChromeClient;

public class WVUCWebViewFragment extends Fragment	// class@00026c from classes.dex
{
    private Activity activity;
    private WVUCWebChromeClient mChromeClient;
    public WVUCWebView mWebView;
    private WVUCWebViewClient mWebclient;
    private String url;
    public static IpChange $ipChange;
    private static String TAG;
    public static String URL;

    static {
       t2o.a(0x3d8001a1);
       WVUCWebViewFragment.TAG = WVUCWebViewFragment.class.getSimpleName();
       WVUCWebViewFragment.URL = "url";
    }
    public void WVUCWebViewFragment(){
       super();
       this.mWebView = null;
       this.mWebclient = null;
       this.mChromeClient = null;
       this.url = null;
    }
    public void WVUCWebViewFragment(Activity p0){
       super();
       this.mWebView = null;
       this.mWebclient = null;
       this.mChromeClient = null;
       this.url = null;
       this.activity = p0;
    }
    public static Object ipc$super(WVUCWebViewFragment p0,String p1,Object[] p2){
       int i = 0;
       switch (p1.hashCode()){
           case 0xa5d6cd73:
             super.onResume();
             return null;
           case 0xa6532022:
             super.onDestroy();
             return null;
           case 0xd9c272d2:
             super.onCreate(p2[i]);
             return null;
           case 0x2f87fc5e:
             super.onPause();
             return null;
           case 0x4bbd23dd:
             super.onLowMemory();
             return null;
           case 0x4f4e949d:
             super.onAttach(p2[i]);
             return null;
           case 0x7ed0f9d9:
             super.onDetach();
             return null;
           default:
             throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/WVUCWebViewFragment");
       }
    }
    public WVUCWebView getWebView(){
       WVUCWebViewFragment tactivity;
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("151ffdcd", objArray);
       }else if(this.mWebView == null){
          if ((tactivity = this.activity) == null) {
             tactivity = this.getActivity();
          }
          if (tactivity == null) {
             return null;
          }else {
             this.mWebView = new WVUCWebView(tactivity);
             this.setWebViewClient(this.mWebclient);
             this.setWebchormeClient(this.mChromeClient);
             this.mWebView.setLayoutParams(new ViewGroup$LayoutParams(-1, -1));
          }
       }
       return this.mWebView;
    }
    public void onActivityResult(int p0,int p1,Intent p2){
       WVUCWebViewFragment tmWebView;
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),new Integer(p1),p2};
          $ipChange.ipc$dispatch("4af7346f", objArray);
          return;
       }else if((tmWebView = this.mWebView) != null){
          tmWebView.onActivityResult(p0, p1, p2);
       }
       return;
    }
    public void onAttach(Activity p0){
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4f4e949d", objArray);
          return;
       }else {
          super.onAttach(p0);
          this.activity = p0;
          return;
       }
    }
    public boolean onBackPressed(){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("88afc67", objArray).booleanValue();
       }else if(this.getWebView() != null && this.getWebView().canGoBack()){
          this.getWebView().goBack();
          return i1;
       }else {
          return i;
       }
    }
    public void onCreate(Bundle p0){
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d9c272d2", objArray);
          return;
       }else {
          super.onCreate(p0);
          if ((p0 = this.getArguments()) != null) {
             this.url = p0.getString(WVUCWebViewFragment.URL);
          }
          return;
       }
    }
    public View onCreateView(LayoutInflater p0,ViewGroup p1,Bundle p2){
       WVUCWebViewFragment turl;
       WVUCWebViewFragment tmWebView;
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("bcd5231c", objArray);
       }else {
          this.getWebView();
          if ((turl = this.url) != null && (tmWebView = this.mWebView) != null) {
             tmWebView.loadUrl(turl);
          }else {
             v7t.a(WVUCWebViewFragment.TAG, "image urls is null");
          }
          return this.mWebView;
       }
    }
    public void onDestroy(){
       WVUCWebViewFragment tmWebView;
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("a6532022", objArray);
          return;
       }else if((tmWebView = this.mWebView) != null){
          tmWebView.setVisibility(8);
          this.mWebView.removeAllViews();
          if (this.mWebView.getParent() != null) {
             this.mWebView.getParent().removeView(this.mWebView);
          }
          this.mWebView.destroy();
          this.mWebView = null;
       }
       this.activity = null;
       try{
          super.onDestroy();
       }catch(java.lang.Exception e0){
          v7t.d(WVUCWebViewFragment.TAG, e0.getMessage());
       }
       return;
    }
    public void onDetach(){
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("7ed0f9d9", objArray);
          return;
       }else {
          super.onDetach();
          return;
       }
    }
    public void onLowMemory(){
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("4bbd23dd", objArray);
          return;
       }else {
          super.onLowMemory();
          return;
       }
    }
    public void onPause(){
       WVUCWebViewFragment tmWebView;
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("2f87fc5e", objArray);
          return;
       }else if((tmWebView = this.mWebView) != null){
          tmWebView.onPause();
       }
       super.onPause();
       return;
    }
    public void onResume(){
       WVUCWebViewFragment tmWebView;
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("a5d6cd73", objArray);
          return;
       }else if((tmWebView = this.mWebView) != null){
          tmWebView.onResume();
       }
       super.onResume();
       return;
    }
    public void setWebViewClient(WVUCWebViewClient p0){
       WVUCWebViewFragment tmWebView;
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d5a058df", objArray);
          return;
       }else if(p0 != null){
          this.mWebclient = p0;
          if ((tmWebView = this.mWebView) != null) {
             tmWebView.setWebViewClient(p0);
          }
       }
       return;
    }
    public void setWebchormeClient(WVUCWebChromeClient p0){
       WVUCWebViewFragment tmWebView;
       IpChange $ipChange = WVUCWebViewFragment.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("3360c045", objArray);
          return;
       }else if(p0 != null){
          this.mChromeClient = p0;
          if ((tmWebView = this.mWebView) != null) {
             tmWebView.setWebChromeClient(p0);
          }
       }
       return;
    }
}
