package android.taobao.windvane.extra.uc.WVUCWebView$7;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.WVUCWebView;
import android.view.View;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class WVUCWebView$7 implements Runnable	// class@00025a from classes.dex
{
    public final WVUCWebView this$0;
    public final View val$v;
    public static IpChange $ipChange;

    public void WVUCWebView$7(WVUCWebView p0,View p1){
       this.this$0 = p0;
       this.val$v = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = WVUCWebView$7.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          this.val$v.requestLayout();
          return;
       }
    }
}
