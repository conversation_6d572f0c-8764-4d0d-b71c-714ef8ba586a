package androidx.activity.result.contract.ActivityResultContracts$RequestPermission;
import androidx.activity.result.contract.ActivityResultContract;
import android.content.Context;
import java.lang.Object;
import android.content.Intent;
import java.lang.String;
import tb.ckf;
import androidx.activity.result.contract.ActivityResultContracts$RequestMultiplePermissions;
import androidx.activity.result.contract.ActivityResultContracts$RequestMultiplePermissions$Companion;
import androidx.activity.result.contract.ActivityResultContract$SynchronousResult;
import androidx.core.content.ContextCompat;
import java.lang.Boolean;

public final class ActivityResultContracts$RequestPermission extends ActivityResultContract	// class@0004da from classes.dex
{

    public void ActivityResultContracts$RequestPermission(){
       super();
    }
    public Intent createIntent(Context p0,Object p1){
       return this.createIntent(p0, p1);
    }
    public Intent createIntent(Context p0,String p1){
       ckf.g(p0, "context");
       ckf.g(p1, "input");
       String[] stringArray = new String[]{p1};
       return ActivityResultContracts$RequestMultiplePermissions.Companion.createIntent$activity_release(stringArray);
    }
    public ActivityResultContract$SynchronousResult getSynchronousResult(Context p0,Object p1){
       return this.getSynchronousResult(p0, p1);
    }
    public ActivityResultContract$SynchronousResult getSynchronousResult(Context p0,String p1){
       ckf.g(p0, "context");
       ckf.g(p1, "input");
       ActivityResultContract$SynchronousResult synchronousR = (!ContextCompat.checkSelfPermission(p0, p1))? new ActivityResultContract$SynchronousResult(Boolean.TRUE): null;
       return synchronousR;
    }
    public Boolean parseResult(int p0,Intent p1){
       if (p1 == null || p0 != -1) {
          return Boolean.FALSE;
       }
       int[] intArrayExtr = p1.getIntArrayExtra("androidx.activity.result.contract.extra.PERMISSION_GRANT_RESULTS");
       boolean b = false;
       if (intArrayExtr != null) {
          int len = intArrayExtr.length;
          int i = 0;
          while (i < len) {
             if (!intArrayExtr[i]) {
                b = true;
                break ;
             }
             i = i + 1;
          }
       }
       return Boolean.valueOf(b);
    }
    public Object parseResult(int p0,Intent p1){
       return this.parseResult(p0, p1);
    }
}
