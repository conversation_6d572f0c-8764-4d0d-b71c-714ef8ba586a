package android.taobao.windvane.extra.uc.AliNetworkHostingService$ResourceType;

public interface abstract AliNetworkHostingService$ResourceType	// class@000208 from classes.dex
{
    public static final int FAVICON = 12;
    public static final int FONT_RESOURCE = 5;
    public static final int IMAGE = 4;
    public static final int MAIN_FRAME = 0;
    public static final int MEDIA = 8;
    public static final int OBJECT = 7;
    public static final int PREFETCH = 11;
    public static final int SCRIPT = 3;
    public static final int SHARED_WORKER = 10;
    public static final int STYLE_SHEET = 2;
    public static final int SUB_FRAME = 1;
    public static final int SUB_RESOURCE = 6;
    public static final int WORKER = 9;
    public static final int XHR = 13;

}
