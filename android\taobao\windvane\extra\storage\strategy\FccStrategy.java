package android.taobao.windvane.extra.storage.strategy.FccStrategy;
import tb.t2o;
import java.util.HashMap;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionOperation;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionEqualOperator;
import java.lang.Object;
import tb.d2c;
import tb.pa8;
import tb.b2c;
import java.util.Map;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.ka8;
import android.taobao.windvane.util.DeviceUtils;
import java.lang.Integer;
import android.taobao.windvane.jsbridge.api.WVDevelopTool;
import java.lang.Float;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$Condition;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValue;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValueType;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValueRange;
import java.util.List;
import java.util.ArrayList;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Boolean;
import java.util.Iterator;
import com.alibaba.fastjson.JSON;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;

public class FccStrategy	// class@0001f5 from classes.dex
{
    public b2c scheduleGrade;
    public static IpChange $ipChange;
    private static final String DEVICE_LEVEL_VARIABLE;
    private static final String OR_SEPARATOR;
    private static final String RANGE_SEPARATOR;
    private static final String TAG;
    private static final Map sOperatorMap;

    static {
       t2o.a(0x3d800123);
       HashMap hashMap = new HashMap();
       FccStrategy.sOperatorMap = hashMap;
       hashMap.put(FccStrategy$ConditionOperation.EQUAL, new FccStrategy$ConditionEqualOperator());
    }
    public void FccStrategy(){
       super();
       this.scheduleGrade = pa8.c().f();
    }
    public static Map access$000(){
       IpChange $ipChange = FccStrategy.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return FccStrategy.sOperatorMap;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("c8ee3d6c", objArray);
    }
    private Map getEnvironment(){
       FccStrategy tscheduleGra;
       Map map;
       IpChange $ipChange = FccStrategy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("d1657a19", objArray);
       }else {
          HashMap hashMap = new HashMap();
          if ((tscheduleGra = this.scheduleGrade) != null && (map = tscheduleGra.c()) != null) {
             hashMap.putAll(map);
          }
          hashMap.put("DL", Integer.valueOf(DeviceUtils.c()));
          if ((map = WVDevelopTool.getMockL2Info()) != null) {
             hashMap.putAll(map);
          }
          return hashMap;
       }
    }
    public static Float parseAsFloat(String p0){
       IpChange $ipChange = FccStrategy.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Float.valueOf(Float.parseFloat(p0));
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("2168b6b1", objArray);
    }
    private FccStrategy$Condition parseCondition(String p0){
       FccStrategy$ConditionValue uConditionVa;
       Float uFloat;
       int i = 2;
       int i1 = 1;
       int i2 = 0;
       IpChange $ipChange = FccStrategy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[i2] = this;
          objArray[i1] = p0;
          return $ipChange.ipc$dispatch("a9b64ae1", objArray);
       }else {
          FccStrategy$ConditionOperation[] uConditionOp = new FccStrategy$ConditionOperation[i1];
          uConditionOp[i2] = FccStrategy$ConditionOperation.EQUAL;
          object oobject = uConditionOp[i2];
          String[] stringArray = p0.split(oobject.operationName);
          if (stringArray.length != i) {
             return null;
          }
          String str = stringArray[i2].trim();
          String str1 = stringArray[i1].trim();
          String[] stringArray1 = str1.split("_", -1);
          if (stringArray1.length == i) {
             uConditionVa = new FccStrategy$ConditionValue(FccStrategy$ConditionValueType.RANGE);
             uConditionVa.valueRange = new FccStrategy$ConditionValueRange(FccStrategy.parseAsFloat(stringArray1[i2].trim()), FccStrategy.parseAsFloat(stringArray1[i1].trim()));
          }else if((uFloat = FccStrategy.parseAsFloat(str1)) != null){
             uConditionVa = new FccStrategy$ConditionValue(FccStrategy$ConditionValueType.NUMBER);
             uConditionVa.valueNumber = uFloat;
          }else {
             uConditionVa = new FccStrategy$ConditionValue(FccStrategy$ConditionValueType.STRING);
             uConditionVa.valueString = str1;
          }
          return new FccStrategy$Condition(str, oobject, uConditionVa, p0);
       }
    }
    private List parseExpression(String p0){
       FccStrategy$Condition uCondition;
       int i = 0;
       IpChange $ipChange = FccStrategy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("d6f5bccb", objArray);
       }else {
          ArrayList uArrayList = new ArrayList();
          if (TextUtils.isEmpty(p0)) {
             return uArrayList;
          }
          String[] stringArray = p0.split(",");
          int len = stringArray.length;
          while (i < len) {
             if ((uCondition = this.parseCondition(stringArray[i])) != null && uCondition.checkValid()) {
                uArrayList.add(uCondition);
             }
             i = i + 1;
          }
          return uArrayList;
       }
    }
    public boolean evaluateConditions(List p0){
       FccStrategy$Condition expression;
       String str;
       icn oicn;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = FccStrategy.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("1407b52d", objArray).booleanValue();
       }else if(p0 != null && !p0.isEmpty()){
          Map environment = this.getEnvironment();
          Iterator iterator = p0.iterator();
          while (true) {
             if (iterator.hasNext()) {
                FccStrategy$Condition uCondition = iterator.next();
                uCondition.setEnvironment(environment);
                if (uCondition.evaluate()) {
                   expression = uCondition.expression;
                }
             }else {
                expression = null;
                i = false;
             }
             if (environment.size() < 15) {
                try{
                   str = JSON.toJSONString(environment);
                }catch(java.lang.Exception e0){
                   str = "size exceeds 15, not show";
                }
             }else {
             }
             oicn = lcn.a(RVLLevel.Info, "WindVane/FccStrategy").j("evaluate").a("environment", str).a("result", Boolean.valueOf(e0));
             if (expression == null) {
                expression = "";
                break ;
             }
             break ;
          }
          oicn.a("matchedExpression", expression).f();
          return e0;
       }else {
          return i1;
       }
    }
    public boolean useFccStrategyCache(String p0){
       IpChange $ipChange = FccStrategy.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.evaluateConditions(this.parseExpression(p0));
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("dd60c65", objArray).booleanValue();
    }
}
