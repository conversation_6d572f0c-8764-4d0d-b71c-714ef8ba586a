package android.taobao.safemode.SafeModeReceiver;
import android.content.BroadcastReceiver;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.content.Context;
import android.content.Intent;
import com.android.alibaba.ip.runtime.IpChange;
import tb.qpo;
import java.lang.System;
import android.taobao.safemode.SafeModeActivity;
import tb.kpo;
import android.taobao.safemode.SafeModeReceiver$a;
import tb.lpo;

public class SafeModeReceiver extends BroadcastReceiver	// class@000146 from classes.dex
{
    public static IpChange $ipChange;

    public void SafeModeReceiver(){
       super();
    }
    public static Object ipc$super(SafeModeReceiver p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/safemode/SafeModeReceiver");
    }
    public void onReceive(Context p0,Intent p1){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = SafeModeReceiver.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("3c04d85a", objArray);
          return;
       }else {
          p0 = p0.getApplicationContext();
          qpo.a(p0, "EnterSafeModeService");
          if (p1 == null) {
             System.exit(i1);
             return;
          }else {
             new kpo(p0, p1.getStringExtra("Version"), p1.getBooleanExtra("Launch", i), new SafeModeReceiver$a(this)).b();
             return;
          }
       }
    }
}
