package android.taobao.windvane.extra.performance2.WVWPManager;
import tb.t2o;
import java.lang.Object;
import java.util.concurrent.ConcurrentHashMap;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.performance2.WVWPData;
import java.util.Collection;
import java.util.Map;
import java.util.Iterator;
import java.lang.ref.Reference;
import android.taobao.windvane.webview.IWVWebView;
import android.taobao.windvane.extra.performance2.IPerformance;
import tb.gtw;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.StringBuilder;
import tb.v7t;
import java.lang.Throwable;
import tb.x74;
import java.lang.Integer;

public class WVWPManager	// class@0001e3 from classes.dex
{
    private final Map wvwpMap;
    public static IpChange $ipChange;
    private static final String TAG;
    private static WVWPManager sInstance;

    static {
       t2o.a(0x3d800118);
    }
    private void WVWPManager(){
       super();
       this.wvwpMap = new ConcurrentHashMap();
    }
    public static WVWPManager getInstance(){
       IpChange $ipChange = WVWPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("ffe49ba8", objArray);
       }else if(WVWPManager.sInstance == null){
          WVWPManager wVWPManager = WVWPManager.class;
          _monitor_enter(wVWPManager);
          if (WVWPManager.sInstance == null) {
             WVWPManager.sInstance = new WVWPManager();
          }
          _monitor_exit(wVWPManager);
       }
       return WVWPManager.sInstance;
    }
    public WVWPData findDataByUrl(String p0){
       WVWPData webviewWeakR;
       IPerformance cachedUrl;
       String str = "WVWPManager";
       IpChange $ipChange = WVWPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("8a3326ab", objArray);
       }else {
          Iterator iterator = this.wvwpMap.values().iterator();
          while (true) {
             if (!iterator.hasNext()) {
                return null;
             }
             WVWPData wVWPData = iterator.next();
             if ((webviewWeakR = wVWPData.webviewWeakRef) == null) {
                continue ;
             }else {
                IWVWebView iWVWebView = webviewWeakR.get();
                if (iWVWebView instanceof IPerformance) {
                   cachedUrl = iWVWebView.getCachedUrl();
                   boolean b = iWVWebView.isPreInit();
                   if (TextUtils.equals(gtw.i(cachedUrl), gtw.i(p0))) {
                      if (!b) {
                         return wVWPData;
                      }
                      break ;
                   }
                }
             }
          }
          v7t.d(str, "isPreInit, abort upload white page:"+cachedUrl);
          return null;
       }
    }
    public void onDestroy(int p0){
       IpChange $ipChange = WVWPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("24115721", objArray);
          return;
       }else {
          this.wvwpMap.remove(Integer.valueOf(p0));
          return;
       }
    }
    public void onViewInit(int p0,WVWPData p1){
       IpChange $ipChange = WVWPManager.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("857c570d", objArray);
          return;
       }else {
          this.wvwpMap.put(Integer.valueOf(p0), p1);
          return;
       }
    }
}
