package android.taobao.windvane.cache.WVFileCache$FixedSizeLinkedHashMap;
import java.util.LinkedHashMap;
import tb.t2o;
import android.taobao.windvane.cache.WVFileCache;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.util.Map$Entry;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.util.AbstractMap;
import tb.v7t;
import tb.oqw;
import java.io.File;
import tb.sb9;
import java.nio.channels.FileChannel;
import tb.pqw;

public class WVFileCache$FixedSizeLinkedHashMap extends LinkedHashMap	// class@000149 from classes.dex
{
    public final WVFileCache this$0;
    public static IpChange $ipChange;
    private static final long serialVersionUID;

    static {
       t2o.a(0x3d800014);
    }
    public void WVFileCache$FixedSizeLinkedHashMap(WVFileCache p0){
       this.this$0 = p0;
       super();
    }
    public static Object ipc$super(WVFileCache$FixedSizeLinkedHashMap p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/cache/WVFileCache$FixedSizeLinkedHashMap");
    }
    public boolean removeEldestEntry(Map$Entry p0){
       int i = 0;
       IpChange $ipChange = WVFileCache$FixedSizeLinkedHashMap.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("6471a6cf", objArray).booleanValue();
       }else if(this.size() > WVFileCache.a(this.this$0)){
          if (v7t.h()) {
             v7t.a(WVFileCache.b(), "removeEldestEntry, size:"+this.size()+" "+p0.getKey());
          }
          p0 = p0.getValue();
          if (p0 instanceof oqw && sb9.a(new File(WVFileCache.c(this.this$0), p0.c))) {
             pqw.d(3, p0, WVFileCache.d(this.this$0));
          }
          return 1;
       }else {
          return i;
       }
    }
}
