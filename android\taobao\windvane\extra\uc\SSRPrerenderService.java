package android.taobao.windvane.extra.uc.SSRPrerenderService;
import tb.nnf;
import tb.t2o;
import java.lang.Object;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.extra.uc.DowngradableSSRService;
import tb.zeq;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import android.net.Uri;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.net.Uri$Builder;
import android.taobao.windvane.extra.uc.prefetch.ResourceRequest$Builder;
import android.taobao.windvane.extra.uc.prefetch.UCDefaultUserAgent;
import android.taobao.windvane.extra.uc.prefetch.ResourceRequest;
import android.taobao.windvane.extra.uc.prefetch.ResourcePrefetch;
import tb.bgq;
import tb.mnf;
import android.os.Handler;
import java.lang.Boolean;
import java.util.concurrent.atomic.AtomicInteger;
import java.io.ByteArrayOutputStream;
import java.util.concurrent.atomic.AtomicBoolean;
import android.taobao.windvane.extra.uc.SSRPrerenderService$1;

public class SSRPrerenderService implements nnf	// class@00021c from classes.dex
{
    private final nnf mService;
    public static IpChange $ipChange;
    public static final String TAG;

    static {
       t2o.a(0x3d800150);
    }
    public void SSRPrerenderService(){
       super();
       this.mService = (vpw.commonConfig.g2 != null)? new DowngradableSSRService(): zeq.a();
       return;
    }
    public static void access$000(SSRPrerenderService p0,String p1,String p2){
       IpChange $ipChange = SSRPrerenderService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2};
          $ipChange.ipc$dispatch("f0bdf98a", objArray);
          return;
       }else {
          p0.prefetchResourceList(p1, p2);
          return;
       }
    }
    public static String makeAbsoluteURL(String p0,String p1){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = SSRPrerenderService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("a70c517b", objArray);
       }else {
          String str = null;
          if (p1 == null) {
             return str;
          }
          Uri uri = Uri.parse(p1);
          if (!TextUtils.isEmpty(uri.getScheme())) {
             return p1;
          }
          if (p0 == null) {
             return str;
          }
          Uri uri1 = Uri.parse(p0);
          if (!TextUtils.isEmpty(uri.getAuthority())) {
             return uri.buildUpon().scheme(uri1.getScheme()).toString();
          }
          if (p1.startsWith("/")) {
             return uri.buildUpon().scheme(uri1.getScheme()).encodedAuthority(uri1.getEncodedAuthority()).toString();
          }
          if ((p1 = uri1.getEncodedPath()) != null) {
             p1 = p1.substring(i1, (p1.lastIndexOf(47) + i));
          }
          return uri.buildUpon().scheme(uri1.getScheme()).encodedAuthority(uri1.getEncodedAuthority()).encodedPath(p1).appendEncodedPath(uri.getEncodedPath()).encodedQuery(uri.getEncodedQuery()).encodedFragment(uri.getEncodedFragment()).toString();
       }
    }
    private void prefetchResource(String p0){
       IpChange $ipChange = SSRPrerenderService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("4b6ee3f6", objArray);
          return;
       }else if(TextUtils.isEmpty(p0)){
          return;
       }else {
          ResourcePrefetch.getInstance().prefetch(new ResourceRequest$Builder().setUrl(p0).setMethod("GET").setHeader("User-Agent", UCDefaultUserAgent.VALUE).build());
          return;
       }
    }
    private void prefetchResourceList(String p0,String p1){
       object oobject;
       Uri uri;
       int i = 0;
       IpChange $ipChange = SSRPrerenderService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("f9a7b07e", objArray);
          return;
       }else if(p1 != null){
          try{
             String[] stringArray = p1.split(",");
             while (i < stringArray.length && i < vpw.commonConfig.T1) {
                if ((oobject = stringArray[i]) != null && (uri = Uri.parse(oobject)) != null) {
                   if (TextUtils.isEmpty(uri.getScheme())) {
                      this.prefetchResource(SSRPrerenderService.makeAbsoluteURL(p0, oobject));
                   }else {
                      this.prefetchResource(oobject);
                   }
                }
                i = i + 1;
             }
          }catch(java.lang.Exception e0){
          }catch(java.lang.Exception e0){
          }
       }
       return;
    }
    public boolean asyncSend(bgq p0,mnf p1,Handler p2){
       int i = 0;
       IpChange $ipChange = SSRPrerenderService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("ac8307d5", objArray).booleanValue();
       }else {
          SSRPrerenderService$1 v7 = new SSRPrerenderService$1(this, new AtomicInteger(i), p1, new AtomicBoolean(i), new ByteArrayOutputStream());
          return this.mService.asyncSend(p0, v7, p2);
       }
    }
    public boolean cancel(bgq p0){
       IpChange $ipChange = SSRPrerenderService.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mService.cancel(p0);
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("801a6ac3", objArray).booleanValue();
    }
}
