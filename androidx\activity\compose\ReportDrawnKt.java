package androidx.activity.compose.ReportDrawnKt;
import androidx.compose.runtime.a;
import androidx.compose.runtime.ComposerImpl;
import androidx.activity.compose.ReportDrawnKt$ReportDrawn$1;
import tb.d1a;
import tb.b940;
import androidx.activity.compose.ReportDrawnKt$ReportDrawn$2;
import androidx.compose.runtime.RecomposeScopeImpl;
import tb.u1a;
import tb.g1a;
import java.lang.Object;
import androidx.activity.compose.LocalFullyDrawnReporterOwner;
import androidx.activity.FullyDrawnReporterOwner;
import androidx.activity.FullyDrawnReporter;
import java.lang.Class;
import androidx.compose.runtime.a$a;
import androidx.activity.compose.ReportDrawnKt$ReportDrawnAfter$1$1;
import tb.ar4;
import androidx.compose.runtime.EffectsKt;
import androidx.activity.compose.ReportDrawnKt$ReportDrawnAfter$2;
import androidx.activity.compose.ReportDrawnKt$ReportDrawnAfter$fullyDrawnReporter$1;
import androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$1$1;
import androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$2;
import androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$fullyDrawnReporter$1;

public final class ReportDrawnKt	// class@0004a1 from classes.dex
{

    public static final void ReportDrawn(a p0,int p1){
       b940 uob940;
       p0 = p0.B(-1357012904);
       if (!p1) {
          a uoa = p0;
          if (uoa.a()) {
             uoa.e();
          label_001d :
             if ((uob940 = p0.p0()) != null) {
                uob940.I(new ReportDrawnKt$ReportDrawn$2(p1));
             }
             return;
          }
       }
       ReportDrawnKt.ReportDrawnWhen(ReportDrawnKt$ReportDrawn$1.INSTANCE, p0, 6);
       goto label_001d ;
    }
    public static final void ReportDrawnAfter(g1a p0,a p1,int p2){
       int i1;
       FullyDrawnReporterOwner current;
       FullyDrawnReporter fullyDrawnRe;
       b940 uob940;
       p1 = p1.B(0x38584e28);
       int i = 2;
       if (!((p2 & 0x06))) {
          i1 = (p1.y(p0))? 4: 2;
          i1 = i1 | p2;
       }else {
          i1 = p2;
       }
       if (((i1 & 0x03)) == i) {
          a uoa = p1;
          if (uoa.a()) {
             uoa.e();
          label_0071 :
             if ((uob940 = p1.p0()) != null) {
                uob940.I(new ReportDrawnKt$ReportDrawnAfter$2(p0, p2));
             }
             return;
          }
       }
       if ((current = LocalFullyDrawnReporterOwner.INSTANCE.getCurrent(p1, 6)) != null && (fullyDrawnRe = current.getFullyDrawnReporter()) != null) {
          a uoa1 = p1;
          uoa1.F(-100805929);
          ReportDrawnKt$ReportDrawnAfter$1$1 reportDrawnA = uoa1.o();
          if (!((uoa1.y(fullyDrawnRe) | uoa1.y(p0)))) {
             a.Companion.getClass();
             if (reportDrawnA != a$a.a) {
             label_0067 :
                uoa1.K();
                EffectsKt.e(p0, fullyDrawnRe, reportDrawnA, p1, (i1 & 0x0e));
                goto label_0071 ;
             }
          }
          reportDrawnA = new ReportDrawnKt$ReportDrawnAfter$1$1(fullyDrawnRe, p0, null);
          uoa1.E(reportDrawnA);
          goto label_0067 ;
       }else if((uob940 = p1.p0()) != null){
          uob940.I(new ReportDrawnKt$ReportDrawnAfter$fullyDrawnReporter$1(p0, p2));
       }
       return;
    }
    public static final void ReportDrawnWhen(d1a p0,a p1,int p2){
       int i1;
       FullyDrawnReporterOwner current;
       FullyDrawnReporter fullyDrawnRe;
       b940 uob940;
       p1 = p1.B(-2047119994);
       int i = 2;
       if (!((p2 & 0x06))) {
          i1 = (p1.y(p0))? 4: 2;
          i1 = i1 | p2;
       }else {
          i1 = p2;
       }
       if (((i1 & 0x03)) == i) {
          a uoa = p1;
          if (uoa.a()) {
             uoa.e();
          label_0072 :
             if ((uob940 = p1.p0()) != null) {
                uob940.I(new ReportDrawnKt$ReportDrawnWhen$2(p0, p2));
             }
             return;
          }
       }
       if ((current = LocalFullyDrawnReporterOwner.INSTANCE.getCurrent(p1, 6)) != null && (fullyDrawnRe = current.getFullyDrawnReporter()) != null) {
          a uoa1 = p1;
          uoa1.F(-537074000);
          ReportDrawnKt$ReportDrawnWhen$1$1 reportDrawnW = uoa1.o();
          if (!((uoa1.y(fullyDrawnRe) | uoa1.y(p0)))) {
             a.Companion.getClass();
             if (reportDrawnW != a$a.a) {
             label_0066 :
                uoa1.K();
                EffectsKt.b(fullyDrawnRe, p0, reportDrawnW, p1, ((i1 << 3) & 0x70));
                goto label_0072 ;
             }
          }
          reportDrawnW = new ReportDrawnKt$ReportDrawnWhen$1$1(fullyDrawnRe, p0);
          uoa1.E(reportDrawnW);
          goto label_0066 ;
       }else if((uob940 = p1.p0()) != null){
          uob940.I(new ReportDrawnKt$ReportDrawnWhen$fullyDrawnReporter$1(p0, p2));
       }
       return;
    }
}
