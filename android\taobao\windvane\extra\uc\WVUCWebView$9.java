package android.taobao.windvane.extra.uc.WVUCWebView$9;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.x74;

public class WVUCWebView$9 implements Runnable	// class@00025c from classes.dex
{
    public final WVUCWebView this$0;
    public static IpChange $ipChange;

    public void WVUCWebView$9(WVUCWebView p0){
       this.this$0 = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = WVUCWebView$9.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          x74.a();
          return;
       }
    }
}
