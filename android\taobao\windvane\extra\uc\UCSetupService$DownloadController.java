package android.taobao.windvane.extra.uc.UCSetupService$DownloadController;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.uc.webview.export.extension.U4Engine$IDownloadHandle;
import java.lang.Boolean;
import android.taobao.windvane.extra.uc.UCSetupService$NetworkHelper;
import tb.yaa;
import android.content.Context;
import tb.vpw;
import tb.wpw;
import tb.v7t;
import java.lang.StringBuilder;

public final class UCSetupService$DownloadController	// class@000228 from classes.dex
{
    private final ArrayList mDelayedTasks;
    public static IpChange $ipChange;
    private static final boolean ENABLE_RESUME;
    private static UCSetupService$DownloadController sInstance;

    static {
       t2o.a(0x3d80015e);
    }
    public void UCSetupService$DownloadController(){
       super();
       this.mDelayedTasks = null;
    }
    public static UCSetupService$DownloadController getInstance(){
       IpChange $ipChange = UCSetupService$DownloadController.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("417f73ef", objArray);
       }else if(UCSetupService$DownloadController.sInstance == null){
          UCSetupService$DownloadController uDownloadCon = UCSetupService$DownloadController.class;
          _monitor_enter(uDownloadCon);
          if (UCSetupService$DownloadController.sInstance == null) {
             UCSetupService$DownloadController.sInstance = new UCSetupService$DownloadController();
          }
          _monitor_exit(uDownloadCon);
       }
       return UCSetupService$DownloadController.sInstance;
    }
    public void delay(U4Engine$IDownloadHandle p0){
       IpChange $ipChange = UCSetupService$DownloadController.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("88b4d481", objArray);
          return;
       }else if(p0 == null){
          return;
       }else {
          p0.cancel();
          return;
       }
    }
    public void resume(){
       IpChange $ipChange = UCSetupService$DownloadController.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("deb96e34", objArray);
       }
       return;
    }
    public boolean shouldDelay(){
       int i3;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = UCSetupService$DownloadController.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("a740881b", objArray).booleanValue();
       }else {
          int currentNetwo = UCSetupService$NetworkHelper.getInstance().getCurrentNetworkType(yaa.n);
          int i2 = (i1 == currentNetwo)? 1: 0;
          if (yaa.f().l()) {
             i3 = (4 == currentNetwo)? 1: 0;
             i2 = i2 | i3;
          }
          vpw.b();
          if (vpw.commonConfig.S != null && yaa.f().m()) {
             i3 = (5 == currentNetwo)? 1: 0;
             i2 = i2 | i3;
          }
          if (!i2) {
             v7t.d("UCSetupService", "current env cannot download u4 core");
          }else {
             String str = "start download u4 core,is4G=[";
             if (4 == currentNetwo) {
                i = true;
             }
             v7t.i("UCSetupService", str+i+"]");
          }
          return (i2 ^ 0x01);
       }
    }
}
