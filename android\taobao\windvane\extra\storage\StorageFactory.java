package android.taobao.windvane.extra.storage.StorageFactory;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import android.taobao.windvane.extra.storage.IStorage;
import com.android.alibaba.ip.runtime.IpChange;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.extra.storage.ProtoDBStorageImpl;
import android.taobao.windvane.extra.storage.WVStorageImpl;

public class StorageFactory	// class@0001ec from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800121);
    }
    private void StorageFactory(){
       super();
    }
    public static IStorage createStorageInstance(String p0){
       IpChange $ipChange = StorageFactory.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          return $ipChange.ipc$dispatch("cd8da0f7", objArray);
       }else if(vpw.commonConfig.Z1 != null){
          return new ProtoDBStorageImpl(p0);
       }else {
          return new WVStorageImpl(p0);
       }
    }
}
