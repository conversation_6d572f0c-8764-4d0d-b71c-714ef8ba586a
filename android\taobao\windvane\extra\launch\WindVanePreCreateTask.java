package android.taobao.windvane.extra.launch.WindVanePreCreateTask;
import android.taobao.windvane.extra.launch.InitOnceTask;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import android.app.Application;
import java.util.HashMap;
import android.content.Context;
import android.taobao.windvane.extra.uc.pool.WebViewPool;

public class WindVanePreCreateTask extends InitOnceTask	// class@0001c1 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000f6);
    }
    public void WindVanePreCreateTask(){
       super();
    }
    public static Object ipc$super(WindVanePreCreateTask p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/launch/WindVanePreCreateTask");
    }
    public String getName(){
       IpChange $ipChange = WindVanePreCreateTask.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "WindVanePreCreateTask";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7c09e698", objArray);
    }
    public void initImpl(Application p0,HashMap p1){
       IpChange $ipChange = WindVanePreCreateTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("73f761cb", objArray);
          return;
       }else {
          WebViewPool.setUp(p0);
          return;
       }
    }
}
