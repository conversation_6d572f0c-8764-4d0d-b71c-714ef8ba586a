package android.taobao.windvane.extra.performance2.WVPageTrackerAPI;
import tb.kpw;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import tb.nsw;
import org.json.JSONObject;
import android.taobao.windvane.extra.performance2.IPerformance;
import java.util.Iterator;
import java.lang.Throwable;

public class WVPageTrackerAPI extends kpw	// class@0001df from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800114);
    }
    public void WVPageTrackerAPI(){
       super();
    }
    public static Object ipc$super(WVPageTrackerAPI p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/performance2/WVPageTrackerAPI");
    }
    public boolean execute(String p0,String p1,WVCallBackContext p2){
       IpChange $ipChange = WVPageTrackerAPI.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("bcd41fd1", objArray).booleanValue();
       }else if(p0.equals("reportPerformanceInfo")){
          this.reportPerformanceInfo(p1, p2);
          return 1;
       }else {
          return 0;
       }
    }
    public void reportPerformanceInfo(String p0,WVCallBackContext p1){
       JSONObject jSONObject1;
       IpChange $ipChange = WVPageTrackerAPI.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("8fd613f8", objArray);
          return;
       }else {
          nsw onsw = new nsw();
          if (this.performance == null) {
             onsw.j("HY_FAILED");
             onsw.b("msg", "performance object does not exist");
             p1.error(onsw);
             return;
          }else {
             try{
                JSONObject jSONObject = new JSONObject(p0);
                this.performance.receiveJSMessageForCustomizedFSP(jSONObject.optLong("firstScreenPaint"));
                if ((jSONObject1 = jSONObject.getJSONObject("otherPerformanceStage")) != null) {
                   Iterator iterator = jSONObject1.keys();
                   while (iterator.hasNext()) {
                      String str = iterator.next();
                      this.performance.receiveJSMessageForCustomizedStage(jSONObject1.optLong(str), str);
                   }
                }
                p1.success();
             }catch(java.lang.Exception e9){
                onsw.j("HY_FAILED");
                onsw.b("msg", "exception: "+e9.getMessage());
                p1.error(onsw);
             }
             return;
          }
       }
    }
}
