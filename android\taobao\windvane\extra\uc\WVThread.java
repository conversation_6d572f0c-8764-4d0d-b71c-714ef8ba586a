package android.taobao.windvane.extra.uc.WVThread;
import android.os.HandlerThread;
import tb.t2o;
import java.lang.String;
import java.lang.Thread;
import android.os.Handler;
import android.os.Looper;
import android.os.Handler$Callback;
import java.lang.Object;
import java.lang.Boolean;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;

public class WVThread extends HandlerThread	// class@000235 from classes.dex
{
    private Handler mHandler;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d80016a);
    }
    public void WVThread(String p0){
       super(p0);
       this.start();
       this.mHandler = new Handler(this.getLooper());
    }
    public void WVThread(String p0,int p1){
       super(p0, p1);
       this.start();
       this.mHandler = new Handler(this.getLooper());
    }
    public void WVThread(String p0,int p1,Handler$Callback p2){
       super(p0, p1);
       this.start();
       this.mHandler = new Handler(this.getLooper(), p2);
    }
    public void WVThread(String p0,Handler$Callback p1){
       super(p0);
       this.start();
       this.mHandler = new Handler(this.getLooper(), p1);
    }
    public static Object ipc$super(WVThread p0,String p1,Object[] p2){
       if (p1.hashCode() == -1052580006) {
          return new Boolean(super.quit());
       }
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/WVThread");
    }
    public Handler getHandler(){
       IpChange $ipChange = WVThread.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mHandler;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("e690ed4b", objArray);
    }
    public boolean quit(){
       WVThread tmHandler;
       IpChange $ipChange = WVThread.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          return $ipChange.ipc$dispatch("c142e75a", objArray).booleanValue();
       }else if((tmHandler = this.mHandler) != null){
          tmHandler.removeCallbacksAndMessages(null);
       }
       return super.quit();
    }
}
