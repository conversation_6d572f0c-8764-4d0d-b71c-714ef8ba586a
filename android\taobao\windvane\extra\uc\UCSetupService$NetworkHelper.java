package android.taobao.windvane.extra.uc.UCSetupService$NetworkHelper;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.content.Context;
import java.lang.Number;
import android.net.ConnectivityManager;
import android.net.NetworkInfo;
import tb.vpw;
import tb.wpw;

public final class UCSetupService$NetworkHelper	// class@000229 from classes.dex
{
    private int mNetworkType;
    public static IpChange $ipChange;
    public static final int NETWORK_TYPE_2G;
    public static final int NETWORK_TYPE_3G;
    public static final int NETWORK_TYPE_4G;
    public static final int NETWORK_TYPE_5G;
    public static final int NETWORK_TYPE_UNKNOWN;
    public static final int NETWORK_TYPE_WIFI;
    private static UCSetupService$NetworkHelper sInstance;

    static {
       t2o.a(0x3d80015f);
    }
    private void UCSetupService$NetworkHelper(){
       super();
       this.mNetworkType = 0;
    }
    public static UCSetupService$NetworkHelper getInstance(){
       IpChange $ipChange = UCSetupService$NetworkHelper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("a4363fc7", objArray);
       }else if(UCSetupService$NetworkHelper.sInstance == null){
          UCSetupService$NetworkHelper networkHelpe = UCSetupService$NetworkHelper.class;
          _monitor_enter(networkHelpe);
          if (UCSetupService$NetworkHelper.sInstance == null) {
             UCSetupService$NetworkHelper.sInstance = new UCSetupService$NetworkHelper();
          }
          _monitor_exit(networkHelpe);
       }
       return UCSetupService$NetworkHelper.sInstance;
    }
    public int getCurrentNetworkType(Context p0){
       NetworkInfo activeNetwor;
       int i = 1;
       int i1 = 2;
       IpChange $ipChange = UCSetupService$NetworkHelper.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("61fd1ef", objArray).intValue();
       }else if((activeNetwor = p0.getSystemService("connectivity").getActiveNetworkInfo()) == null){
          return 0;
       }else {
          int type = activeNetwor.getType();
          if (i != type && 9 != type) {
             if (!type) {
                int subtype = activeNetwor.getSubtype();
                if (4 != subtype && (i != subtype && i1 != subtype)) {
                   if ((i = 3) != subtype && (8 != subtype && (6 != subtype && (5 != subtype && 12 != subtype)))) {
                      if (13 == subtype) {
                         i = 4;
                      }else {
                         vpw.b();
                         if (vpw.commonConfig.S != null && 20 == subtype) {
                            i = 5;
                         }
                      }
                   }
                }else {
                   i = 2;
                }
             }
             i = 0;
          }
          return i;
       }
    }
}
