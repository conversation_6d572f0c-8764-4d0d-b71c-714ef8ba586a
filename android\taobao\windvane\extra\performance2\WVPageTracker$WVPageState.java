package android.taobao.windvane.extra.performance2.WVPageTracker$WVPageState;
import java.lang.Enum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.Class;
import java.lang.Number;

public final class WVPageTracker$WVPageState extends Enum	// class@0001dc from classes.dex
{
    private int state;
    private static final WVPageTracker$WVPageState[] $VALUES;
    public static IpChange $ipChange;
    public static final WVPageTracker$WVPageState WVPageStateError;
    public static final WVPageTracker$WVPageState WVPageStateErrorOccurred;
    public static final WVPageTracker$WVPageState WVPageStateFinishLoad;
    public static final WVPageTracker$WVPageState WVPageStateInit;
    public static final WVPageTracker$WVPageState WVPageStateLoadURL;
    public static final WVPageTracker$WVPageState WVPageStateStartLoad;

    static {
       WVPageTracker$WVPageState wVPageState = new WVPageTracker$WVPageState("WVPageStateError", 0, -1);
       WVPageTracker$WVPageState.WVPageStateError = wVPageState;
       WVPageTracker$WVPageState wVPageState1 = new WVPageTracker$WVPageState("WVPageStateInit", 1, 0);
       WVPageTracker$WVPageState.WVPageStateInit = wVPageState1;
       WVPageTracker$WVPageState wVPageState2 = new WVPageTracker$WVPageState("WVPageStateLoadURL", 2, 1);
       WVPageTracker$WVPageState.WVPageStateLoadURL = wVPageState2;
       WVPageTracker$WVPageState wVPageState3 = new WVPageTracker$WVPageState("WVPageStateStartLoad", 3, 2);
       WVPageTracker$WVPageState.WVPageStateStartLoad = wVPageState3;
       WVPageTracker$WVPageState wVPageState4 = new WVPageTracker$WVPageState("WVPageStateFinishLoad", 4, 3);
       WVPageTracker$WVPageState.WVPageStateFinishLoad = wVPageState4;
       WVPageTracker$WVPageState wVPageState5 = new WVPageTracker$WVPageState("WVPageStateErrorOccurred", 5, 4);
       WVPageTracker$WVPageState.WVPageStateErrorOccurred = wVPageState5;
       WVPageTracker$WVPageState[] wVPageStateA = new WVPageTracker$WVPageState[]{wVPageState,wVPageState1,wVPageState2,wVPageState3,wVPageState4,wVPageState5};
       WVPageTracker$WVPageState.$VALUES = wVPageStateA;
    }
    private void WVPageTracker$WVPageState(String p0,int p1,int p2){
       super(p0, p1);
       this.state = p2;
    }
    public static Object ipc$super(WVPageTracker$WVPageState p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/performance2/WVPageTracker$WVPageState");
    }
    public static WVPageTracker$WVPageState valueOf(int p0){
       int i = 1;
       IpChange $ipChange = WVPageTracker$WVPageState.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = new Integer(p0);
          return $ipChange.ipc$dispatch("7e02f92", objArray);
       }else if(p0 != -1){
          if (!p0) {
             return WVPageTracker$WVPageState.WVPageStateInit;
          }
          if (p0 == i) {
             return WVPageTracker$WVPageState.WVPageStateLoadURL;
          }
          if (p0 == 2) {
             return WVPageTracker$WVPageState.WVPageStateStartLoad;
          }
          if (p0 == 3) {
             return WVPageTracker$WVPageState.WVPageStateFinishLoad;
          }
          if (p0 != 4) {
             return null;
          }
          return WVPageTracker$WVPageState.WVPageStateErrorOccurred;
       }else {
          return WVPageTracker$WVPageState.WVPageStateError;
       }
    }
    public static WVPageTracker$WVPageState valueOf(String p0){
       IpChange $ipChange = WVPageTracker$WVPageState.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Enum.valueOf(WVPageTracker$WVPageState.class, p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("592ae82d", objArray);
    }
    public static WVPageTracker$WVPageState[] values(){
       IpChange $ipChange = WVPageTracker$WVPageState.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVPageTracker$WVPageState.$VALUES.clone();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("50cafde", objArray);
    }
    public int value(){
       IpChange $ipChange = WVPageTracker$WVPageState.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.state;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("d249f56b", objArray).intValue();
    }
}
