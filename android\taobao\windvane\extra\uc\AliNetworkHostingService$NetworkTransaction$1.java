package android.taobao.windvane.extra.uc.AliNetworkHostingService$NetworkTransaction$1;
import java.lang.Runnable;
import android.taobao.windvane.extra.uc.AliNetworkHostingService$NetworkTransaction;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import com.uc.webview.export.extension.INetworkHostingService$IDelegate;

public class AliNetworkHostingService$NetworkTransaction$1 implements Runnable	// class@000204 from classes.dex
{
    public final AliNetworkHostingService$NetworkTransaction this$1;
    public static IpChange $ipChange;

    public void AliNetworkHostingService$NetworkTransaction$1(AliNetworkHostingService$NetworkTransaction p0){
       this.this$1 = p0;
       super();
    }
    public void run(){
       IpChange $ipChange = AliNetworkHostingService$NetworkTransaction$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          AliNetworkHostingService$NetworkTransaction.access$200(this.this$1).onError(-2104, "req send failed");
          return;
       }
    }
}
