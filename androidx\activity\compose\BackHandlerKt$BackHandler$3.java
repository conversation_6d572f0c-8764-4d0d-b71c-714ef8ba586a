package androidx.activity.compose.BackHandlerKt$BackHandler$3;
import tb.u1a;
import kotlin.jvm.internal.Lambda;
import tb.d1a;
import java.lang.Object;
import androidx.compose.runtime.a;
import java.lang.Number;
import tb.xhv;
import androidx.activity.compose.BackHandlerKt;

public final class BackHandlerKt$BackHandler$3 extends Lambda implements u1a	// class@00047f from classes.dex
{
    public final int $$changed;
    public final int $$default;
    public final boolean $enabled;
    public final d1a $onBack;

    public void BackHandlerKt$BackHandler$3(boolean p0,d1a p1,int p2,int p3){
       this.$enabled = p0;
       this.$onBack = p1;
       this.$$changed = p2;
       this.$$default = p3;
       super(2);
    }
    public Object invoke(Object p0,Object p1){
       this.invoke(p0, p1.intValue());
       return xhv.INSTANCE;
    }
    public final void invoke(a p0,int p1){
       BackHandlerKt.BackHandler(this.$enabled, this.$onBack, p0, (this.$$changed | 0x01), this.$$default);
    }
}
