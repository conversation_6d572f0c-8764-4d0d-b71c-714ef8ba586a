package android.taobao.windvane.extra.log.TLogImpl;
import android.taobao.windvane.util.log.ILog;
import tb.t2o;
import com.taobao.tao.log.TLog;
import android.util.AndroidRuntimeException;
import java.lang.String;
import java.lang.Throwable;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.StringBuilder;
import java.lang.Integer;
import java.lang.Boolean;

public class TLogImpl implements ILog	// class@0001c8 from classes.dex
{
    public static IpChange $ipChange;
    private static final String DELIMITER;
    private static final String LOG_MODULE_NAME;

    static {
       t2o.a(0x3d8000fd);
       t2o.a(0x3d8002d7);
       IpChange $ipChange = TLog.$ipChange;
    }
    public void TLogImpl(){
       super();
    }
    public void d(String p0,String p1){
       IpChange $ipChange = TLogImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("7f180e7f", objArray);
          return;
       }else {
          TLog.logd("WindVane", p0, p1);
          return;
       }
    }
    public void d(String p0,String p1,Throwable p2){
       IpChange $ipChange = TLogImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("80ec3ad6", objArray);
          return;
       }else {
          StringBuilder str = p1+"\n";
          p1 = (p2 != null)? p2.getMessage(): "throwable is null";
          TLog.logd("WindVane", p0, str+p1);
          return;
       }
    }
    public void e(String p0,String p1){
       IpChange $ipChange = TLogImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("bb83980", objArray);
          return;
       }else {
          TLog.loge("WindVane", p0, p1);
          return;
       }
    }
    public void e(String p0,String p1,Throwable p2){
       IpChange $ipChange = TLogImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("152aaa75", objArray);
          return;
       }else {
          TLog.loge("WindVane", p0, p1, p2);
          return;
       }
    }
    public void i(String p0,String p1){
       IpChange $ipChange = TLogImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("3e38e584", objArray);
          return;
       }else {
          TLog.logi("WindVane", p0, p1);
          return;
       }
    }
    public void i(String p0,String p1,Throwable p2){
       IpChange $ipChange = TLogImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("662468f1", objArray);
          return;
       }else {
          StringBuilder str = p1+"\n";
          p1 = (p2 != null)? p2.getMessage(): "throwable is null";
          TLog.logi("WindVane", p0, str+p1);
          return;
       }
    }
    public boolean isLogLevelEnabled(int p0){
       int i = 1;
       IpChange $ipChange = TLogImpl.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return i;
       }
       Object[] objArray = new Object[]{this,new Integer(p0)};
       return $ipChange.ipc$dispatch("b353b3c9", objArray).booleanValue();
    }
    public void log(int p0,String p1,String p2){
       int i = 3;
       int i1 = 4;
       IpChange $ipChange = TLogImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[0] = this;
          objArray[1] = new Integer(p0);
          objArray[2] = p1;
          objArray[i] = p2;
          $ipChange.ipc$dispatch("508f0fac", objArray);
          return;
       }else if(p0 != i){
          if (p0 != i1) {
             if (p0 != 5) {
                if (p0 != 6) {
                   this.v(p1, p2);
                }else {
                   this.e(p1, p2);
                }
             }else {
                this.w(p1, p2);
             }
          }else {
             this.i(p1, p2);
          }
       }else {
          this.d(p1, p2);
       }
       return;
    }
    public void v(String p0,String p1){
       IpChange $ipChange = TLogImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("625b1491", objArray);
          return;
       }else {
          TLog.logv("WindVane", p0, p1);
          return;
       }
    }
    public void v(String p0,String p1,Throwable p2){
       IpChange $ipChange = TLogImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("ed501404", objArray);
          return;
       }else {
          StringBuilder str = p1+"\n";
          p1 = (p2 != null)? p2.getMessage(): "throwable is null";
          TLog.logv("WindVane", p0, str+p1);
          return;
       }
    }
    public void w(String p0,String p1){
       IpChange $ipChange = TLogImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("eefb3f92", objArray);
          return;
       }else {
          TLog.logw("WindVane", p0, p1);
          return;
       }
    }
    public void w(String p0,String p1,Throwable p2){
       IpChange $ipChange = TLogImpl.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("818e83a3", objArray);
          return;
       }else {
          StringBuilder str = p1+"\n";
          p1 = (p2 != null)? p2.getMessage(): "throwable is null";
          TLog.logw("WindVane", p0, str+p1);
          return;
       }
    }
}
