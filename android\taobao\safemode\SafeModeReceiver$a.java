package android.taobao.safemode.SafeModeReceiver$a;
import tb.lpo;
import android.taobao.safemode.SafeModeReceiver;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.System;

public class SafeModeReceiver$a implements lpo	// class@000145 from classes.dex
{
    public static IpChange $ipChange;

    public void SafeModeReceiver$a(SafeModeReceiver p0){
       super();
    }
    public void onFinish(){
       IpChange $ipChange = SafeModeReceiver$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("badeed9", objArray);
          return;
       }else {
          System.exit(0);
          return;
       }
    }
}
