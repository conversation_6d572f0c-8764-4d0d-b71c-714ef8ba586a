package android.taobao.windvane.extra.launch.WindVaneWelComeTask$1;
import java.lang.Runnable;
import android.taobao.windvane.extra.launch.WindVaneWelComeTask;
import android.app.Application;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.r9u;
import android.content.Context;
import android.taobao.windvane.extra.uc.init.WVDexOptimizer;

public class WindVaneWelComeTask$1 implements Runnable	// class@0001c3 from classes.dex
{
    public final WindVaneWelComeTask this$0;
    public final Application val$application;
    public static IpChange $ipChange;

    public void WindVaneWelComeTask$1(WindVaneWelComeTask p0,Application p1){
       this.this$0 = p0;
       this.val$application = p1;
       super();
    }
    public void run(){
       IpChange $ipChange = WindVaneWelComeTask$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          r9u.b("preloadDexSync");
          WVDexOptimizer.preloadDexSafe(this.val$application);
          r9u.d();
          return;
       }
    }
}
