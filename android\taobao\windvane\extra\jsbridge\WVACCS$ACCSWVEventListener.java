package android.taobao.windvane.extra.jsbridge.WVACCS$ACCSWVEventListener;
import tb.jqw;
import tb.t2o;
import android.taobao.windvane.webview.IWVWebView;
import java.lang.Object;
import java.lang.ref.WeakReference;
import tb.iqw;
import tb.kqw;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.String;
import java.lang.ref.Reference;
import tb.v7t;
import org.json.JSONObject;

public class WVACCS$ACCSWVEventListener implements jqw	// class@0001a0 from classes.dex
{
    private WeakReference webview;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000d6);
       t2o.a(0x3d80029a);
    }
    public void WVACCS$ACCSWVEventListener(IWVWebView p0){
       super();
       this.webview = new WeakReference(p0);
    }
    public kqw onEvent(int p0,iqw p1,Object[] p2){
       IWVWebView iWVWebView;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = WVACCS$ACCSWVEventListener.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1,p2};
          return $ipChange.ipc$dispatch("75ee5a2a", objArray);
       }else if((iWVWebView = this.webview.get()) == null){
          if (v7t.h()) {
             v7t.d("ACCS", "webview is recycled");
          }
          return null;
       }else {
          switch (p0){
              case 5001:
                JSONObject jSONObject = new JSONObject();
                jSONObject.put("serviceId", p2[i1]);
                jSONObject.put("resultData", new String(p2[i]));
                String str = jSONObject.toString();
                iWVWebView.fireEvent("WV.Event.ACCS.OnData", str);
                if (v7t.h()) {
                   v7t.i("ACCS", str);
                }
                break;
              case 5002:
                iWVWebView.fireEvent("WV.Event.ACCS.OnConnected", "{}");
                if (v7t.h()) {
                   v7t.d("ACCS", "ACCS connect");
                }
                break;
              case 5003:
                iWVWebView.fireEvent("WV.Event.ACCS.OnDisConnected", "{}");
                if (v7t.h()) {
                   v7t.d("ACCS", "ACCS disconnect");
                }
                break;
              default:
          }
          return null;
       }
    }
}
