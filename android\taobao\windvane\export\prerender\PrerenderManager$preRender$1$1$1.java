package android.taobao.windvane.export.prerender.PrerenderManager$preRender$1$1$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import android.taobao.windvane.export.prerender.PrerenderManager$preRender$1$1;
import tb.wum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.xhv;
import com.android.alibaba.ip.runtime.IpChange;
import android.taobao.windvane.export.prerender.PrerenderManager;
import java.util.List;
import android.taobao.windvane.export.prerender.PrerenderManager$preRender$1$1$1$a;
import android.taobao.windvane.export.prerender.PrerenderManager$preRender$1;
import tb.xum;
import java.lang.Runnable;
import com.taobao.themis.kernel.utils.CommonExtKt;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;
import java.lang.Boolean;
import tb.g1a;

public final class PrerenderManager$preRender$1$1$1 extends Lambda implements d1a	// class@000178 from classes.dex
{
    public final wum $prerenderItem;
    public final PrerenderManager$preRender$1$1 this$0;
    public static IpChange $ipChange;

    public void PrerenderManager$preRender$1$1$1(PrerenderManager$preRender$1$1 p0,wum p1){
       this.this$0 = p0;
       this.$prerenderItem = p1;
       super(0);
    }
    public static Object ipc$super(PrerenderManager$preRender$1$1$1 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/export/prerender/PrerenderManager$preRender$1$1$1");
    }
    public Object invoke(){
       this.invoke();
       return xhv.INSTANCE;
    }
    public final void invoke(){
       IpChange $ipChange = PrerenderManager$preRender$1$1$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("7560ccff", objArray);
          return;
       }else {
          PrerenderManager.a(PrerenderManager.INSTANCE).add(this.$prerenderItem);
          CommonExtKt.p(new PrerenderManager$preRender$1$1$1$a(this), this.this$0.this$0.$params.d());
          lcn.f(RVLLevel.Error, "Themis/Performance/Prerender", "start preRendering, url: "+this.this$0.this$0.$params.e()+", preRenderType: "+this.this$0.this$0.$params.b());
          this.this$0.this$0.$callback.invoke(Boolean.TRUE);
          return;
       }
    }
}
