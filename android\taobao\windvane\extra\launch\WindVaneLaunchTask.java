package android.taobao.windvane.extra.launch.WindVaneLaunchTask;
import tb.t2o;
import android.taobao.windvane.extra.launch.WindVaneWelComeTask;
import android.taobao.windvane.extra.launch.WindVaneHomeVisibleTask;
import android.taobao.windvane.extra.launch.WindVaneIdleTask;
import android.taobao.windvane.extra.launch.WindVanePreCreateTask;
import android.taobao.windvane.extra.launch.WindVaneScheduledPreCreateTask;
import java.lang.Object;
import android.app.Application;
import java.util.HashMap;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.launch.InitOnceTask;

public class WindVaneLaunchTask	// class@0001c0 from classes.dex
{
    public static IpChange $ipChange;
    private static final WindVaneHomeVisibleTask homeVisibleTask;
    private static final WindVaneIdleTask idleTask;
    public static final WindVanePreCreateTask preCreateTask;
    public static final WindVaneScheduledPreCreateTask scheduledPreCreateTask;
    private static final WindVaneWelComeTask welcomeTask;

    static {
       t2o.a(0x3d8000f5);
       WindVaneLaunchTask.welcomeTask = new WindVaneWelComeTask();
       WindVaneLaunchTask.homeVisibleTask = new WindVaneHomeVisibleTask();
       WindVaneLaunchTask.idleTask = new WindVaneIdleTask();
       WindVaneLaunchTask.preCreateTask = new WindVanePreCreateTask();
       WindVaneLaunchTask.scheduledPreCreateTask = new WindVaneScheduledPreCreateTask();
    }
    public void WindVaneLaunchTask(){
       super();
    }
    public static void initAtHomeVisible(Application p0,HashMap p1){
       IpChange $ipChange = WindVaneLaunchTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("3596abab", objArray);
          return;
       }else {
          WindVaneLaunchTask.homeVisibleTask.init(p0, p1);
          return;
       }
    }
    public static void initAtIdle(Application p0,HashMap p1){
       IpChange $ipChange = WindVaneLaunchTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("27321852", objArray);
          return;
       }else {
          WindVaneLaunchTask.idleTask.init(p0, p1);
          return;
       }
    }
    public static void initAtWelcome(Application p0,HashMap p1){
       IpChange $ipChange = WindVaneLaunchTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("a64178fa", objArray);
          return;
       }else {
          WindVaneLaunchTask.welcomeTask.init(p0, p1);
          return;
       }
    }
    public static void initPreCreate(Application p0,HashMap p1){
       IpChange $ipChange = WindVaneLaunchTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("37907aca", objArray);
          return;
       }else {
          WindVaneLaunchTask.preCreateTask.init(p0, p1);
          return;
       }
    }
    public static void initScheduledPreCreate(Application p0,HashMap p1){
       IpChange $ipChange = WindVaneLaunchTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("6f208afd", objArray);
          return;
       }else {
          WindVaneLaunchTask.scheduledPreCreateTask.init(p0, p1);
          return;
       }
    }
}
