package androidx.activity.compose.ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$2;
import tb.gi20;
import androidx.activity.compose.ReportDrawnComposition;
import java.lang.Object;

public final class ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$2 implements gi20	// class@00049d from classes.dex
{
    public final ReportDrawnComposition $compositionDrawn$inlined;

    public void ReportDrawnKt$ReportDrawnWhen$1$1$invoke$$inlined$onDispose$2(ReportDrawnComposition p0){
       this.$compositionDrawn$inlined = p0;
       super();
    }
    public void dispose(){
       this.$compositionDrawn$inlined.removeReporter();
    }
}
