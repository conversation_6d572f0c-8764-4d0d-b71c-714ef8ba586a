package android.taobao.windvane.extra.performance.WVWebViewPageModel;
import tb.cce;
import tb.t2o;
import java.lang.Object;
import java.util.concurrent.CopyOnWriteArrayList;
import java.util.concurrent.ConcurrentHashMap;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.vpw;
import tb.wpw;
import java.util.Iterator;
import java.util.List;
import android.taobao.windvane.extra.performance.action.IPerformanceVisitor;
import android.taobao.windvane.extra.performance.action.AddPropertyVisitor;
import android.taobao.windvane.extra.performance.action.AddPropertyIfAbsentVisitor;
import java.lang.Long;
import android.taobao.windvane.extra.performance.action.AddStageVisitor;
import android.taobao.windvane.extra.performance.action.AddStageIfAbsentVisitor;

public class WVWebViewPageModel implements cce	// class@0001d0 from classes.dex
{
    private final List mPerformanceVisitorList;
    private final List mWebViewPageModelList;
    private final ConcurrentHashMap propertyMap;
    private final ConcurrentHashMap stageMap;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800105);
       t2o.a(0x3d8000b5);
    }
    public void WVWebViewPageModel(){
       super();
       this.mWebViewPageModelList = new CopyOnWriteArrayList();
       this.stageMap = new ConcurrentHashMap();
       this.propertyMap = new ConcurrentHashMap();
       this.mPerformanceVisitorList = new CopyOnWriteArrayList();
    }
    public void addWebViewPageModel(cce p0){
       IPerformanceVisitor iPerformance;
       IpChange $ipChange = WVWebViewPageModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("63ed38db", objArray);
          return;
       }else if(p0 == null){
          return;
       }else if(vpw.commonConfig.r2 != null){
          try{
             Iterator iterator = this.mPerformanceVisitorList.iterator();
             while (iterator.hasNext()) {
                if ((iPerformance = iterator.next()) != null) {
                   iPerformance.accept(p0);
                }
             }
          }catch(java.lang.Exception e0){
          }
       }
       this.mWebViewPageModelList.add(p0);
       return;
    }
    public ConcurrentHashMap getPropertyMap(){
       IpChange $ipChange = WVWebViewPageModel.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.propertyMap;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("6aad66ce", objArray);
    }
    public ConcurrentHashMap getStageMap(){
       IpChange $ipChange = WVWebViewPageModel.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.stageMap;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("9666c20b", objArray);
    }
    public void onProperty(String p0,Object p1){
       IpChange $ipChange = WVWebViewPageModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("1991a501", objArray);
          return;
       }else {
          this.propertyMap.put(p0, p1);
          this.mPerformanceVisitorList.add(new AddPropertyVisitor(p0, p1));
          Iterator iterator = this.mWebViewPageModelList.iterator();
          while (iterator.hasNext()) {
             iterator.next().onProperty(p0, p1);
          }
          return;
       }
    }
    public void onPropertyIfAbsent(String p0,Object p1){
       IpChange $ipChange = WVWebViewPageModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("564eb9d7", objArray);
          return;
       }else if(!this.propertyMap.containsKey(p0)){
          this.propertyMap.put(p0, p1);
       }
       this.mPerformanceVisitorList.add(new AddPropertyIfAbsentVisitor(p0, p1));
       Iterator iterator = this.mWebViewPageModelList.iterator();
       while (iterator.hasNext()) {
          iterator.next().onPropertyIfAbsent(p0, p1);
       }
       return;
    }
    public void onStage(String p0,long p1){
       IpChange $ipChange = WVWebViewPageModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("6ba4fb14", objArray);
          return;
       }else {
          this.stageMap.put(p0, Long.valueOf(p1));
          this.mPerformanceVisitorList.add(new AddStageVisitor(p0, p1));
          Iterator iterator = this.mWebViewPageModelList.iterator();
          while (iterator.hasNext()) {
             iterator.next().onStage(p0, p1);
          }
          return;
       }
    }
    public void onStageIfAbsent(String p0,long p1){
       IpChange $ipChange = WVWebViewPageModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("da2a227e", objArray);
          return;
       }else if(!this.stageMap.containsKey(p0)){
          this.stageMap.put(p0, Long.valueOf(p1));
       }
       this.mPerformanceVisitorList.add(new AddStageIfAbsentVisitor(p0, p1));
       Iterator iterator = this.mWebViewPageModelList.iterator();
       while (iterator.hasNext()) {
          iterator.next().onStageIfAbsent(p0, p1);
       }
       return;
    }
}
