package android.taobao.windvane.extra.uc.WVUCWebViewClient$4;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.uc.WVUCWebViewClient;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import tb.csw;
import tb.trw;

public class WVUCWebViewClient$4 implements ValueCallback	// class@000266 from classes.dex
{
    public final WVUCWebViewClient this$0;
    public final long val$mPageFinshTime;
    public final String val$url;
    public static IpChange $ipChange;

    public void WVUCWebViewClient$4(WVUCWebViewClient p0,String p1,long p2){
       this.this$0 = p0;
       this.val$url = p1;
       this.val$mPageFinshTime = p2;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       IpChange $ipChange = WVUCWebViewClient$4.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else if(trw.getPerformanceMonitor() != null){
          trw.getPerformanceMonitor().didPagePerformanceInfo(this.val$url, p0);
          trw.getPerformanceMonitor().didPageFinishLoadAtTime(this.val$url, this.val$mPageFinshTime);
       }
       return;
    }
}
