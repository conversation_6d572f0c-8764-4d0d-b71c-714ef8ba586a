package androidx.activity.compose.LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.FullyDrawnReporterOwner;
import java.lang.Object;

public final class LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1 extends Lambda implements d1a	// class@000485 from classes.dex
{
    public static final LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1 INSTANCE;

    static {
       LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1.INSTANCE = new LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1();
    }
    public void LocalFullyDrawnReporterOwner$LocalFullyDrawnReporterOwner$1(){
       super(0);
    }
    public final FullyDrawnReporterOwner invoke(){
       return null;
    }
    public Object invoke(){
       return this.invoke();
    }
}
