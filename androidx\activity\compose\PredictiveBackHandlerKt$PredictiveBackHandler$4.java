package androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$4;
import tb.u1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Object;
import androidx.compose.runtime.a;
import java.lang.Number;
import tb.xhv;
import androidx.activity.compose.PredictiveBackHandlerKt;

public final class PredictiveBackHandlerKt$PredictiveBackHandler$4 extends Lambda implements u1a	// class@000491 from classes.dex
{
    public final int $$changed;
    public final int $$default;
    public final boolean $enabled;
    public final u1a $onBack;

    public void PredictiveBackHandlerKt$PredictiveBackHandler$4(boolean p0,u1a p1,int p2,int p3){
       this.$enabled = p0;
       this.$onBack = p1;
       this.$$changed = p2;
       this.$$default = p3;
       super(2);
    }
    public Object invoke(Object p0,Object p1){
       this.invoke(p0, p1.intValue());
       return xhv.INSTANCE;
    }
    public final void invoke(a p0,int p1){
       PredictiveBackHandlerKt.PredictiveBackHandler(this.$enabled, this.$onBack, p0, (this.$$changed | 0x01), this.$$default);
    }
}
