package android.taobao.windvane.extra.uc.AliNetworkHostingService$BodyHandlerAdapter;
import anetwork.channel.IBodyHandler;
import tb.t2o;
import android.taobao.windvane.extra.uc.AliNetworkHostingService;
import com.uc.webview.export.extension.INetworkHostingService$IUploadStream;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import java.lang.Number;
import java.lang.System;
import java.lang.Throwable;
import tb.v7t;

public class AliNetworkHostingService$BodyHandlerAdapter implements IBodyHandler	// class@000201 from classes.dex
{
    private boolean mIsCompleted;
    private final INetworkHostingService$IUploadStream mOriginalStream;
    private INetworkHostingService$IUploadStream mWorkingStream;
    public final AliNetworkHostingService this$0;
    public static IpChange $ipChange;
    private static final int BUFFER_SIZE;

    static {
       t2o.a(0x3d800137);
       t2o.a(0x264001d4);
    }
    public void AliNetworkHostingService$BodyHandlerAdapter(AliNetworkHostingService p0,INetworkHostingService$IUploadStream p1){
       this.this$0 = p0;
       super();
       this.mIsCompleted = false;
       this.mOriginalStream = p1;
       this.mWorkingStream = p1;
    }
    public boolean isCompleted(){
       AliNetworkHostingService$BodyHandlerAdapter tmIsComplete;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = AliNetworkHostingService$BodyHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = this;
          return $ipChange.ipc$dispatch("8c6bb44c", objArray).booleanValue();
       }else if((tmIsComplete = this.mIsCompleted) != null){
          this.mIsCompleted = i;
          return i1;
       }else {
          return tmIsComplete;
       }
    }
    public synchronized int read(byte[] p0){
       int len;
       int i;
       IpChange $ipChange = AliNetworkHostingService$BodyHandlerAdapter.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("9ed24497", objArray).intValue();
       }else if(this.mWorkingStream == null){
          this.mOriginalStream.rewind();
          this.mWorkingStream = this.mOriginalStream;
       }
       if ((len = p0.length) >= 4096) {
          len = 4096;
       }
       byte[] uobyteArray = new byte[len];
       if (!(i = this.mWorkingStream.read(uobyteArray))) {
          this.mIsCompleted = true;
          this.mWorkingStream = null;
       }else if(i > 0){
          System.arraycopy(uobyteArray, 0, p0, 0, i);
       }
       return i;
    }
}
