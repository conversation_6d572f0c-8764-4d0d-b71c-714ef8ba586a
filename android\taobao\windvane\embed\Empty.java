package android.taobao.windvane.embed.Empty;
import android.taobao.windvane.embed.BaseEmbedView;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.content.Context;
import android.view.View;
import com.android.alibaba.ip.runtime.IpChange;
import android.widget.TextView;

public class Empty extends BaseEmbedView	// class@000155 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800052);
    }
    public void Empty(){
       super();
    }
    public static Object ipc$super(Empty p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/embed/Empty");
    }
    public View generateView(Context p0){
       int i = 0;
       IpChange $ipChange = Empty.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("7334ca29", objArray);
       }else {
          TextView textView = new TextView(p0);
          textView.setBackgroundColor(i);
          textView.setGravity(17);
          return textView;
       }
    }
    public String getViewType(){
       IpChange $ipChange = Empty.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "empty";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("35692924", objArray);
    }
}
