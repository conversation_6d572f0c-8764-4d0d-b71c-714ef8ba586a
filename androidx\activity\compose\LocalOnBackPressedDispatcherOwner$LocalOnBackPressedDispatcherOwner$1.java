package androidx.activity.compose.LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.OnBackPressedDispatcherOwner;
import java.lang.Object;

public final class LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1 extends Lambda implements d1a	// class@000487 from classes.dex
{
    public static final LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1 INSTANCE;

    static {
       LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1.INSTANCE = new LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1();
    }
    public void LocalOnBackPressedDispatcherOwner$LocalOnBackPressedDispatcherOwner$1(){
       super(0);
    }
    public final OnBackPressedDispatcherOwner invoke(){
       return null;
    }
    public Object invoke(){
       return this.invoke();
    }
}
