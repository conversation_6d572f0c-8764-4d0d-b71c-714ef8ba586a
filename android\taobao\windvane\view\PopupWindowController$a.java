package android.taobao.windvane.view.PopupWindowController$a;
import android.view.View$OnTouchListener;
import android.taobao.windvane.view.PopupWindowController;
import android.widget.Button;
import java.lang.Object;
import android.view.View;
import android.view.MotionEvent;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import android.widget.LinearLayout;

public class PopupWindowController$a implements View$OnTouchListener	// class@000308 from classes.dex
{
    public final Button a;
    public final PopupWindowController b;
    public static IpChange $ipChange;

    public void PopupWindowController$a(PopupWindowController p0,Button p1){
       super();
       this.b = p0;
       this.a = p1;
    }
    public boolean onTouch(View p0,MotionEvent p1){
       IpChange $ipChange = PopupWindowController$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("d4aa3aa4", objArray).booleanValue();
       }else {
          int top = PopupWindowController.b(this.b).getTop();
          int i = (int)p1.getY();
          if (p1.getAction() == 1 && i < top) {
             this.a.performClick();
          }
          return 1;
       }
    }
}
