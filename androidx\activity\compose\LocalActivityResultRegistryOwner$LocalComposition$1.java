package androidx.activity.compose.LocalActivityResultRegistryOwner$LocalComposition$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.result.ActivityResultRegistryOwner;
import java.lang.Object;

public final class LocalActivityResultRegistryOwner$LocalComposition$1 extends Lambda implements d1a	// class@000483 from classes.dex
{
    public static final LocalActivityResultRegistryOwner$LocalComposition$1 INSTANCE;

    static {
       LocalActivityResultRegistryOwner$LocalComposition$1.INSTANCE = new LocalActivityResultRegistryOwner$LocalComposition$1();
    }
    public void LocalActivityResultRegistryOwner$LocalComposition$1(){
       super(0);
    }
    public final ActivityResultRegistryOwner invoke(){
       return null;
    }
    public Object invoke(){
       return this.invoke();
    }
}
