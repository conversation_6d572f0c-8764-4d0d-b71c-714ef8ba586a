package android.taobao.windvane.extra.uc.WVUCWebChromeClient$12;
import android.webkit.ConsoleMessage$MessageLevel;
import java.lang.Enum;

public class WVUCWebChromeClient$12	// class@000242 from classes.dex
{
    public static final int[] $SwitchMap$android$webkit$ConsoleMessage$MessageLevel;

    static {
       int[] ointArray = new int[ConsoleMessage$MessageLevel.values().length];
       try{
          WVUCWebChromeClient$12.$SwitchMap$android$webkit$ConsoleMessage$MessageLevel = ointArray;
          ointArray[ConsoleMessage$MessageLevel.ERROR.ordinal()] = 1;
          try{
             WVUCWebChromeClient$12.$SwitchMap$android$webkit$ConsoleMessage$MessageLevel[ConsoleMessage$MessageLevel.WARNING.ordinal()] = 2;
             try{
                WVUCWebChromeClient$12.$SwitchMap$android$webkit$ConsoleMessage$MessageLevel[ConsoleMessage$MessageLevel.DEBUG.ordinal()] = 3;
                try{
                   WVUCWebChromeClient$12.$SwitchMap$android$webkit$ConsoleMessage$MessageLevel[ConsoleMessage$MessageLevel.LOG.ordinal()] = 4;
                   try{
                      WVUCWebChromeClient$12.$SwitchMap$android$webkit$ConsoleMessage$MessageLevel[ConsoleMessage$MessageLevel.TIP.ordinal()] = 5;
                   }catch(java.lang.NoSuchFieldError e0){
                   }
                }catch(java.lang.NoSuchFieldError e0){
                }
             }catch(java.lang.NoSuchFieldError e0){
             }
          }catch(java.lang.NoSuchFieldError e0){
          }
       }catch(java.lang.NoSuchFieldError e0){
       }
    }
}
