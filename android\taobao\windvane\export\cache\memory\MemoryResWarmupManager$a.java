package android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$a;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager$d;
import android.taobao.windvane.export.cache.memory.model.ResourceItemModel;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import android.taobao.windvane.export.cache.memory.MemoryResWarmupManager;
import java.lang.StringBuilder;
import tb.v7t;

public final class MemoryResWarmupManager$a implements MemoryResWarmupManager$d	// class@000159 from classes.dex
{
    public final MemoryResWarmupManager$d a;
    public final ResourceItemModel b;
    public static IpChange $ipChange;

    public void MemoryResWarmupManager$a(MemoryResWarmupManager$d p0,ResourceItemModel p1){
       super();
       this.a = p0;
       this.b = p1;
    }
    public void a(boolean p0,String p1){
       IpChange $ipChange = MemoryResWarmupManager$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Boolean(p0),p1};
          $ipChange.ipc$dispatch("c845825", objArray);
          return;
       }else {
          MemoryResWarmupManager.a(this.a, p0, p1);
          v7t.i("MemoryResWarmupManager", "warmup "+this.b.src+" "+this.b.mode+" "+this.b.type+" "+p0+" "+p1);
          return;
       }
    }
}
