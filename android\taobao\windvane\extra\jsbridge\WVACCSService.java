package android.taobao.windvane.extra.jsbridge.WVACCSService;
import com.taobao.accs.base.TaoBaseService;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.taobao.accs.base.TaoBaseService$ExtraInfo;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import tb.v7t;
import com.taobao.accs.base.TaoBaseService$ConnectInfo;
import tb.lqw;
import tb.kqw;

public class WVACCSService extends TaoBaseService	// class@0001a2 from classes.dex
{
    private Context mContext;
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x3d8000d7);
    }
    public void WVACCSService(){
       super();
       this.mContext = null;
    }
    public static Object ipc$super(WVACCSService p0,String p1,Object[] p2){
       if (p1.hashCode() != 0x18a7a6c2) {
          throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/jsbridge/WVACCSService");
       }
       super.onCreate();
       return null;
    }
    public void onBind(String p0,int p1,TaoBaseService$ExtraInfo p2){
       IpChange $ipChange = WVACCSService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
          $ipChange.ipc$dispatch("3fa398db", objArray);
          return;
       }else {
          v7t.a("CallbackService", "onBind");
          return;
       }
    }
    public void onConnected(TaoBaseService$ConnectInfo p0){
       IpChange $ipChange = WVACCSService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("df10c6", objArray);
          return;
       }else {
          lqw.d().e(5002);
          v7t.a("CallbackService", "onConnected");
          return;
       }
    }
    public void onCreate(){
       IpChange $ipChange = WVACCSService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("18a7a6c2", objArray);
          return;
       }else {
          super.onCreate();
          this.mContext = this;
          v7t.a("CallbackService", "onCreate");
          return;
       }
    }
    public void onData(String p0,String p1,String p2,byte[] p3,TaoBaseService$ExtraInfo p4){
       IpChange $ipChange = WVACCSService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3,p4};
          $ipChange.ipc$dispatch("694255fc", objArray);
          return;
       }else if(v7t.h()){
          v7t.i("CallbackService", "serviceId : "+p0+" dataId :"+p2);
       }
       Object[] objArray1 = new Object[]{p0,p3};
       lqw.d().g(5001, objArray1);
       return;
    }
    public void onDisconnected(TaoBaseService$ConnectInfo p0){
       IpChange $ipChange = WVACCSService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("e46188f6", objArray);
          return;
       }else {
          lqw.d().e(5003);
          v7t.a("CallbackService", "onDisconnected");
          return;
       }
    }
    public void onResponse(String p0,String p1,int p2,byte[] p3,TaoBaseService$ExtraInfo p4){
       IpChange $ipChange = WVACCSService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2),p3,p4};
          $ipChange.ipc$dispatch("d5239c42", objArray);
          return;
       }else {
          v7t.a("CallbackService", "onResponse");
          return;
       }
    }
    public void onSendData(String p0,String p1,int p2,TaoBaseService$ExtraInfo p3){
       IpChange $ipChange = WVACCSService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,new Integer(p2),p3};
          $ipChange.ipc$dispatch("f29e89fa", objArray);
          return;
       }else {
          v7t.a("CallbackService", "onSendData");
          return;
       }
    }
    public void onUnbind(String p0,int p1,TaoBaseService$ExtraInfo p2){
       IpChange $ipChange = WVACCSService.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Integer(p1),p2};
          $ipChange.ipc$dispatch("7b4e074", objArray);
          return;
       }else {
          v7t.a("CallbackService", "onCreate");
          return;
       }
    }
}
