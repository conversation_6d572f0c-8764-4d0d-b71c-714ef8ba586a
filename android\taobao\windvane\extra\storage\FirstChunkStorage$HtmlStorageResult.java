package android.taobao.windvane.extra.storage.FirstChunkStorage$HtmlStorageResult;
import tb.t2o;
import android.taobao.windvane.extra.storage.FccStorageType;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.CharSequence;
import android.text.TextUtils;

public class FirstChunkStorage$HtmlStorageResult	// class@0001e6 from classes.dex
{
    public String html;
    public String htmlKey;
    public int htmlLength;
    public FccStorageType type;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d80011c);
    }
    public void FirstChunkStorage$HtmlStorageResult(FccStorageType p0,String p1,int p2,String p3){
       super();
       this.type = p0;
       this.html = p1;
       this.htmlLength = p2;
       this.htmlKey = p3;
    }
    public boolean isEmpty(){
       IpChange $ipChange = FirstChunkStorage$HtmlStorageResult.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return TextUtils.isEmpty(this.html);
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("f187dd4e", objArray).booleanValue();
    }
}
