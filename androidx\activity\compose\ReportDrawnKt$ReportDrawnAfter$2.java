package androidx.activity.compose.ReportDrawnKt$ReportDrawnAfter$2;
import tb.u1a;
import kotlin.jvm.internal.Lambda;
import tb.g1a;
import java.lang.Object;
import androidx.compose.runtime.a;
import java.lang.Number;
import tb.xhv;
import androidx.activity.compose.ReportDrawnKt;

public final class ReportDrawnKt$ReportDrawnAfter$2 extends Lambda implements u1a	// class@00049a from classes.dex
{
    public final int $$changed;
    public final g1a $block;

    public void ReportDrawnKt$ReportDrawnAfter$2(g1a p0,int p1){
       this.$block = p0;
       this.$$changed = p1;
       super(2);
    }
    public Object invoke(Object p0,Object p1){
       this.invoke(p0, p1.intValue());
       return xhv.INSTANCE;
    }
    public final void invoke(a p0,int p1){
       ReportDrawnKt.ReportDrawnAfter(this.$block, p0, (this.$$changed | 0x01));
    }
}
