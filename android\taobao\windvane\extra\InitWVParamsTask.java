package android.taobao.windvane.extra.InitWVParamsTask;
import java.io.Serializable;
import tb.t2o;
import java.lang.Object;
import android.app.Application;
import java.util.HashMap;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import tb.lpw;
import tb.mpw;
import tb.yru;
import tb.og8;
import tb.yaa;
import android.content.Context;
import java.lang.Boolean;
import com.taobao.android.ab.api.ABGlobal;
import tb.v7t;
import java.lang.Integer;
import android.taobao.windvane.config.EnvEnum;
import android.taobao.windvane.WindVaneSDK;
import tb.lex;
import com.ut.device.UTDevice;
import tb.b0s;
import tb.h8s;
import com.taobao.themis.kernel.basic.TMSLogger;
import android.taobao.windvane.extra.launch.WindVaneLaunchTask;
import tb.vpw;
import tb.btw;
import tb.eqw;
import tb.wpw;
import java.lang.System;
import tb.gvm;
import java.lang.StringBuilder;
import com.alibaba.mtl.appmonitor.AppMonitor$Alarm;
import java.lang.Throwable;
import android.taobao.windvane.extra.uc.UCSetupServiceUtil;

public class InitWVParamsTask implements Serializable	// class@000183 from classes.dex
{
    public static IpChange $ipChange;
    public static final String TAG;

    static {
       t2o.a(0x3d8000b8);
    }
    public void InitWVParamsTask(){
       super();
    }
    private void initParams(Application p0,HashMap p1){
       int i = 1;
       IpChange $ipChange = InitWVParamsTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("abb2c931", objArray);
          return;
       }else {
          lpw olpw = this.obtainWVParams(p0, p1);
          mpw.a().c(olpw);
          yru oyru = new yru();
          oyru.a = "windvane";
          oyru.b = "vKFaqtcEUEHI15lIOzsI6jIQldPpaCZ3";
          oyru.e = i;
          oyru.d = i;
          oyru.c = og8.b();
          olpw.g = oyru;
          yaa.f().k(olpw);
          return;
       }
    }
    public static boolean isInitWVParamOpenV2(Context p0){
       int i = 1;
       IpChange $ipChange = InitWVParamsTask.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return (ABGlobal.isFeatureOpened(p0, "OptInitWvParamV2") ^ i);
       }
       Object[] objArray = new Object[i];
       objArray[0] = p0;
       return $ipChange.ipc$dispatch("393b098b", objArray).booleanValue();
    }
    private lpw obtainWVParams(Application p0,HashMap p1){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = InitWVParamsTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("f3c8a781", objArray);
       }else {
          lpw olpw = new lpw();
          olpw.a = p1.get("ttid");
          i1 = p1.get("envIndex").intValue();
          if (!i1) {
             olpw.c = p1.get("onlineAppKey");
             WindVaneSDK.setEnvMode(EnvEnum.ONLINE);
          }else if(i1 == i){
             olpw.c = p1.get("onlineAppKey");
             WindVaneSDK.setEnvMode(EnvEnum.PRE);
          }else {
             olpw.c = p1.get("dailyAppkey");
             WindVaneSDK.setEnvMode(EnvEnum.DAILY);
          }
          olpw.d = "TB";
          olpw.f = lex.TB_UC_SDK_APP_KEY_SEC;
          olpw.e = "0.0.0";
          olpw.e = p1.get("appVersion");
          olpw.b = UTDevice.getUtdid(p0);
          return olpw;
       }
    }
    public void init(Application p0,HashMap p1){
       String str = "";
       String str1 = "opt useTime PreStartup ";
       IpChange $ipChange = InitWVParamsTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("dddb138b", objArray);
          return;
       }else {
          b0s.a();
          if (h8s.a(p0.getApplicationContext())) {
             TMSLogger.b("TMSEarlyInitializer", "skip InitWVParamsTask, but still run initAtWelcome");
             WindVaneLaunchTask.initAtWelcome(p0, p1);
             return;
          }else {
             yaa.n = p0;
             if (!ABGlobal.isFeatureOpened(p0, "OptInitWvParam") && !InitWVParamsTask.isInitWVParamOpenV2(p0)) {
                this.initParams(p0, p1);
                vpw.b().d();
                btw.c().e();
                eqw.c().e();
                if (vpw.commonConfig.O != null) {
                   gvm.a(p0);
                   v7t.d("InitWVParamsTask", str1+(System.currentTimeMillis() - System.currentTimeMillis()));
                }
                AppMonitor$Alarm.commitFail("WindVane", "InitWVParamOld", str, str);
             }else if(!mpw.a().b()){
                this.initParams(p0, p1);
             }
             og8.f(p1.get("isDebuggable").booleanValue());
             UCSetupServiceUtil.configUCSettingsBeforeInit();
             return;
          }
       }
    }
}
