package android.support.v4.os.ResultReceiver$MyResultReceiver;
import android.support.v4.os.IResultReceiver$Stub;
import android.support.v4.os.ResultReceiver;
import android.os.Bundle;
import android.support.v4.os.ResultReceiver$MyRunnable;
import java.lang.Runnable;
import android.os.Handler;

class ResultReceiver$MyResultReceiver extends IResultReceiver$Stub	// class@000139 from classes.dex
{
    final ResultReceiver this$0;

    public void ResultReceiver$MyResultReceiver(ResultReceiver p0){
       this.this$0 = p0;
       super();
    }
    public void send(int p0,Bundle p1){
       ResultReceiver mHandler;
       ResultReceiver$MyResultReceiver tthis$0 = this.this$0;
       if ((mHandler = tthis$0.mHandler) != null) {
          mHandler.post(new ResultReceiver$MyRunnable(tthis$0, p0, p1));
       }else {
          tthis$0.onReceiveResult(p0, p1);
       }
       return;
    }
}
