package android.taobao.windvane.util.log.ILog;
import java.lang.String;
import java.lang.Throwable;

public interface abstract ILog	// class@000305 from classes.dex
{

    void d(String p0,String p1);
    void d(String p0,String p1,Throwable p2);
    void e(String p0,String p1);
    void e(String p0,String p1,Throwable p2);
    void i(String p0,String p1);
    void i(String p0,String p1,Throwable p2);
    void v(String p0,String p1);
    void v(String p0,String p1,Throwable p2);
    void w(String p0,String p1);
    void w(String p0,String p1,Throwable p2);
}
