package android.taobao.windvane.export.prerender.PrerenderManager$preRender$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import tb.g1a;
import tb.xum;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.xhv;
import com.android.alibaba.ip.runtime.IpChange;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.export.prerender.PrerenderManager;
import java.util.List;
import com.taobao.android.riverlogger.RVLLevel;
import tb.lcn;
import java.lang.Boolean;
import java.lang.Iterable;
import java.util.Iterator;
import tb.wum;
import tb.zcd;
import tb.vum;
import android.taobao.windvane.export.prerender.PrerenderManager$preRender$1$1;

public final class PrerenderManager$preRender$1 extends Lambda implements d1a	// class@00017a from classes.dex
{
    public final g1a $callback;
    public final xum $params;
    public static IpChange $ipChange;

    public void PrerenderManager$preRender$1(g1a p0,xum p1){
       this.$callback = p0;
       this.$params = p1;
       super(0);
    }
    public static Object ipc$super(PrerenderManager$preRender$1 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/export/prerender/PrerenderManager$preRender$1");
    }
    public Object invoke(){
       this.invoke();
       return xhv.INSTANCE;
    }
    public final void invoke(){
       Object obj;
       IpChange $ipChange = PrerenderManager$preRender$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("7560ccff", objArray);
          return;
       }else {
          PrerenderManager iNSTANCE = PrerenderManager.INSTANCE;
          if (PrerenderManager.a(iNSTANCE).size() >= vpw.commonConfig.u2) {
             lcn.f(RVLLevel.Error, "Themis/Performance/Prerender", "exceed prerender size limit");
             this.$callback.invoke(Boolean.FALSE);
             return;
          }else {
             Iterator iterator = PrerenderManager.a(iNSTANCE).iterator();
             while (true) {
                if (iterator.hasNext()) {
                   obj = iterator.next();
                   xum oxum = obj.a();
                   zcd ozcd = oxum.c();
                   if (!ozcd.a(oxum.e(), this.$params.e())) {
                      continue ;
                   }
                }else {
                   obj = null;
                }
                if (obj != null) {
                   break ;
                }else {
                   vum.INSTANCE.a(this.$params, new PrerenderManager$preRender$1$1(this));
                   return;
                }
             }
             lcn.f(RVLLevel.Info, "Themis/Performance/Prerender", "found existing prerender webview");
             this.$callback.invoke(Boolean.FALSE);
             return;
          }
       }
    }
}
