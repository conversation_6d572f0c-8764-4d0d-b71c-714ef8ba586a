package android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionEqualOperator;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionOperator;
import tb.t2o;
import java.lang.Object;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValue;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValueType;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Number;
import java.lang.Float;
import android.taobao.windvane.extra.storage.strategy.FccStrategy;
import android.taobao.windvane.extra.storage.strategy.FccStrategy$ConditionValueRange;

public class FccStrategy$ConditionEqualOperator implements FccStrategy$ConditionOperator	// class@0001ef from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800125);
       t2o.a(0x3d800127);
    }
    public void FccStrategy$ConditionEqualOperator(){
       super();
    }
    public boolean evaluate(FccStrategy$ConditionValue p0,Object p1){
       FccStrategy$ConditionValue valueType;
       int i = 1;
       IpChange $ipChange = FccStrategy$ConditionEqualOperator.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("ac5d6cfb", objArray).booleanValue();
       }else if((valueType = p0.valueType) == FccStrategy$ConditionValueType.STRING){
          if (p1 == null || !TextUtils.equals(String.valueOf(p1), p0.valueString)) {
             i = false;
          }
          return i;
       }else if(valueType == FccStrategy$ConditionValueType.NUMBER){
          if (p1 != null && p0.valueNumber != null) {
             if (p1 instanceof Number) {
                if (p0.valueNumber.floatValue() - p1.floatValue()) {
                   i = false;
                }
                return i;
             }else if(p1 instanceof String){
                if ((p1 = FccStrategy.parseAsFloat(p1)) == null || p0.valueNumber.compareTo(p1)) {
                   i = false;
                }
                return i;
             }
          }
          return 0;
       }else if(valueType == FccStrategy$ConditionValueType.RANGE && (p1 != null && (valueType = p0.valueRange) != null)){
          if (p1 instanceof Number) {
             return valueType.isInRange(p1.floatValue());
          }else if(p1 instanceof String){
             if ((p1 = FccStrategy.parseAsFloat(p1)) == null || !p0.valueRange.isInRange(p1.floatValue())) {
                i = false;
             }
             return i;
          }
       }
       return 0;
    }
}
