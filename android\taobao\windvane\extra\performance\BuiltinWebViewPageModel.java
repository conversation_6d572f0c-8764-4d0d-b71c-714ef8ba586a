package android.taobao.windvane.extra.performance.BuiltinWebViewPageModel;
import tb.cce;
import tb.t2o;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import android.view.View;
import tb.tc;
import tb.jpw;
import tb.lab;
import java.lang.Class;
import java.lang.Long;

public class BuiltinWebViewPageModel implements cce	// class@0001c9 from classes.dex
{
    private final WVUCWebView webView;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000fe);
       t2o.a(0x3d8000b5);
    }
    public void BuiltinWebViewPageModel(WVUCWebView p0){
       super();
       this.webView = p0;
    }
    public void onProperty(String p0,Object p1){
       lab olab;
       IpChange $ipChange = BuiltinWebViewPageModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("1991a501", objArray);
          return;
       }else if(!tc.a(this.webView)){
          return;
       }else if((olab = jpw.c().a(lab.class)) != null){
          olab.e(this.webView, p0, p1);
       }
       return;
    }
    public void onPropertyIfAbsent(String p0,Object p1){
       lab olab;
       IpChange $ipChange = BuiltinWebViewPageModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("564eb9d7", objArray);
          return;
       }else if(!tc.a(this.webView)){
          return;
       }else if((olab = jpw.c().a(lab.class)) != null){
          olab.b(this.webView, p0, p1);
       }
       return;
    }
    public void onStage(String p0,long p1){
       lab olab;
       IpChange $ipChange = BuiltinWebViewPageModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("6ba4fb14", objArray);
          return;
       }else if(!tc.a(this.webView)){
          return;
       }else if((olab = jpw.c().a(lab.class)) != null){
          olab.c(this.webView, p0, p1);
       }
       return;
    }
    public void onStageIfAbsent(String p0,long p1){
       lab olab;
       IpChange $ipChange = BuiltinWebViewPageModel.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("da2a227e", objArray);
          return;
       }else if(!tc.a(this.webView)){
          return;
       }else if((olab = jpw.c().a(lab.class)) != null){
          olab.h(this.webView, p0, p1);
       }
       return;
    }
}
