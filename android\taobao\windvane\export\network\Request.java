package android.taobao.windvane.export.network.Request;
import tb.t2o;
import java.util.concurrent.atomic.AtomicInteger;
import android.taobao.windvane.export.network.Request$b;
import java.lang.Object;
import java.util.ArrayList;
import java.util.HashMap;
import android.taobao.windvane.extra.uc.timing.RequestTiming;
import java.lang.String;
import java.util.Map;
import tb.ecd;
import tb.r47;
import tb.ikd;
import android.taobao.windvane.export.network.Request$a;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Number;
import tb.esd;
import java.lang.Boolean;
import java.util.Iterator;
import android.util.Pair;
import java.lang.Long;
import java.util.Set;
import java.util.Map$Entry;
import android.os.SystemClock;
import android.taobao.windvane.extra.uc.interfaces.IRequestTiming;

public class Request	// class@000169 from classes.dex
{
    public final String a;
    public final String b;
    public final List c;
    public final Map d;
    public esd e;
    public Map f;
    public final int g;
    public final boolean h;
    public final ecd i;
    public final ikd j;
    public final IRequestTiming l;
    public static IpChange $ipChange;
    public static final AtomicInteger k;

    static {
       t2o.a(0x3d800088);
       Request.k = new AtomicInteger(0);
    }
    public void Request(Request$b p0){
       super();
       this.c = new ArrayList();
       this.d = new HashMap();
       this.l = new RequestTiming();
       this.a = Request$b.a(p0);
       this.b = Request$b.b(p0);
       this.f = Request$b.c(p0);
       this.h = p0.d;
       this.i = (Request$b.d(p0) != null)? Request$b.d(p0): new r47();
       this.j = Request$b.e(p0);
       return;
    }
    public void Request(Request$b p0,Request$a p1){
       super(p0);
    }
    public void Request(Request p0){
       super();
       this.c = new ArrayList();
       this.d = new HashMap();
       this.l = new RequestTiming();
       this.a = p0.a;
       this.b = p0.b;
       this.f = p0.f;
       this.e = p0.e;
       this.h = p0.d();
       this.i = p0.i;
       this.j = p0.j;
       this.g = Request.f();
    }
    public static int f(){
       IpChange $ipChange = Request.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return Request.k.getAndAdd(1);
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("595db446", objArray).intValue();
    }
    public void a(String p0,String p1){
       IpChange $ipChange = Request.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("dbb8617f", objArray);
          return;
       }else if(p0 != null && p1 != null){
          this.k();
          if (!this.f.containsKey(p0)) {
             this.f.put(p0, p1);
          }
       }
       return;
    }
    public boolean b(esd p0){
       Pair pair;
       Map$Entry uEntry;
       int i = 0;
       IpChange $ipChange = Request.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("ae91a534", objArray).booleanValue();
       }else if(this.e == null){
          _monitor_enter(this);
          if (this.e == null) {
             this.e = p0;
             try{
                Iterator iterator = this.c.iterator();
                while (iterator.hasNext()) {
                   if ((pair = iterator.next()) != null) {
                      p0.recordStage(pair.first, pair.second.longValue());
                   }
                }
                this.c.clear();
                iterator = this.d.entrySet().iterator();
                while (iterator.hasNext()) {
                   if ((uEntry = iterator.next()) != null) {
                      p0.recordProperty(uEntry.getKey(), uEntry.getValue());
                   }
                }
                this.d.clear();
                _monitor_exit(this);
                return e0;
             }catch(java.lang.Exception e0){
             }
          }else {
             _monitor_exit(this);
          }
       }
       return i;
    }
    public Map c(){
       IpChange $ipChange = Request.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.f;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("cf4415cc", objArray);
    }
    public boolean d(){
       IpChange $ipChange = Request.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.h;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("e92611aa", objArray).booleanValue();
    }
    public String e(){
       IpChange $ipChange = Request.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.b;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("5e63d782", objArray);
    }
    public ecd g(){
       IpChange $ipChange = Request.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.i;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("b0373b53", objArray);
    }
    public int h(){
       IpChange $ipChange = Request.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.g;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("fef4bfce", objArray).intValue();
    }
    public ikd i(){
       IpChange $ipChange = Request.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.j;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("a7bb8853", objArray);
    }
    public String j(){
       IpChange $ipChange = Request.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.a;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("de8f0660", objArray);
    }
    public final void k(){
       IpChange $ipChange = Request.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("2a5ec198", objArray);
          return;
       }else if(this.f == null){
          this.f = new HashMap();
       }
       return;
    }
    public void l(String p0){
       IpChange $ipChange = Request.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f21d799e", objArray);
          return;
       }else {
          this.n(p0, SystemClock.uptimeMillis());
          return;
       }
    }
    public void m(String p0,Object p1){
       Request te;
       IpChange $ipChange = Request.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("539dc3c3", objArray);
          return;
       }else {
          _monitor_enter(this);
          try{
             if ((te = this.e) == null) {
                this.d.put(p0, p1);
             }else {
                te.recordProperty(p0, p1);
             }
             _monitor_exit(this);
             return;
          }catch(java.lang.Exception e0){
          }
       }
    }
    public void n(String p0,long p1){
       Request te;
       IpChange $ipChange = Request.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Long(p1)};
          $ipChange.ipc$dispatch("b34e8ed6", objArray);
          return;
       }else {
          _monitor_enter(this);
          try{
             if ((te = this.e) == null) {
                this.c.add(new Pair(p0, Long.valueOf(p1)));
             }else {
                te.recordStage(p0, p1);
             }
             _monitor_exit(this);
             return;
          }catch(java.lang.Exception e0){
          }
       }
    }
    public IRequestTiming o(){
       IpChange $ipChange = Request.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.l;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("1f4a73b4", objArray);
    }
}
