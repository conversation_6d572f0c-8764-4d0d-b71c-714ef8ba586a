package android.taobao.windvane.extra.uc.WVUCWebView$10;
import com.uc.webview.export.extension.UCExtension$InjectJSProvider;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Integer;
import java.lang.StringBuilder;

public class WVUCWebView$10 implements UCExtension$InjectJSProvider	// class@000251 from classes.dex
{
    public final WVUCWebView this$0;
    public static IpChange $ipChange;

    public void WVUCWebView$10(WVUCWebView p0){
       this.this$0 = p0;
       super();
    }
    public String getJS(int p0,String p1){
       IpChange $ipChange = WVUCWebView$10.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.this$0.injectJs;
       }
       Object[] objArray = new Object[]{this,new Integer(p0),p1};
       return $ipChange.ipc$dispatch("3f660b59", objArray);
    }
}
