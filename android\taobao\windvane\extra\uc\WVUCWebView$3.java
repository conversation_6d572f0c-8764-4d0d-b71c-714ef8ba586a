package android.taobao.windvane.extra.uc.WVUCWebView$3;
import java.lang.Runnable;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.uc.WVUCWebView;

public final class WVUCWebView$3 implements Runnable	// class@000255 from classes.dex
{
    public static IpChange $ipChange;

    public void WVUCWebView$3(){
       super();
    }
    public void run(){
       IpChange $ipChange = WVUCWebView$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("5c510192", objArray);
          return;
       }else {
          WVUCWebView.access$700();
          return;
       }
    }
}
