package android.taobao.windvane.extra.jsbridge.WVMega;
import tb.kpw;
import tb.t2o;
import java.lang.String;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.Boolean;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.os.Looper;
import android.os.Handler;
import android.taobao.windvane.extra.jsbridge.WVMega$1;
import java.lang.Runnable;
import android.taobao.windvane.webview.IWVWebView;
import android.content.Context;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import android.taobao.windvane.extra.jsbridge.WVMegaBridgeContext;
import com.taobao.android.riverlogger.RVLLevel;
import tb.icn;
import tb.lcn;
import tb.xq;
import android.taobao.windvane.extra.uc.WVUCWebView;
import tb.kdb;
import android.view.View;
import tb.ace;
import tb.ace$b;
import java.util.Map;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import com.alibaba.ability.env.PerfTracer;
import tb.vpw;
import tb.wpw;
import tb.ldb;
import tb.x74;
import android.taobao.windvane.extra.jsbridge.WVMega$2;
import tb.s2d;
import com.alibaba.ability.hub.AbilityHubAdapter;
import tb.nsw;
import org.json.JSONObject;
import java.lang.Throwable;
import com.alibaba.ability.result.ErrorResult;
import com.alibaba.ability.result.ErrorResult$a;

public class WVMega extends kpw	// class@0001a6 from classes.dex
{
    private AbilityHubAdapter adapter;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000d9);
    }
    public void WVMega(){
       super();
    }
    public static boolean access$000(WVMega p0,String p1,WVCallBackContext p2){
       IpChange $ipChange = WVMega.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.callOnUIThread(p1, p2);
       }
       Object[] objArray = new Object[]{p0,p1,p2};
       return $ipChange.ipc$dispatch("bb4aa34a", objArray).booleanValue();
    }
    private boolean call(String p0,WVCallBackContext p1){
       IpChange $ipChange = WVMega.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("e2c1d830", objArray).booleanValue();
       }else {
          WVCallBackContext currentUrlFr = p1.currentUrlFromAsyncChannel;
          if (TextUtils.isEmpty(currentUrlFr)) {
             if (Looper.myLooper() == Looper.getMainLooper()) {
                return this.callOnUIThread(p0, p1);
             }else {
                new Handler(Looper.getMainLooper()).post(new WVMega$1(this, p0, p1));
             }
          }else {
             this.megaCall(p0, p1, currentUrlFr);
          }
          return 1;
       }
    }
    private boolean callOnUIThread(String p0,WVCallBackContext p1){
       IpChange $ipChange = WVMega.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.megaCall(p0, p1, p1.getWebview().getUrl());
       }
       Object[] objArray = new Object[]{this,p0,p1};
       return $ipChange.ipc$dispatch("39cdcb2d", objArray).booleanValue();
    }
    public static Object ipc$super(WVMega p0,String p1,Object[] p2){
       if (p1.hashCode() != -1811143243) {
          throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/jsbridge/WVMega");
       }
       super.initialize(p2[0], p2[1]);
       return null;
    }
    private boolean megaCall(String p0,WVCallBackContext p1,String p2){
       WVCallBackContext wvMegaBridge;
       Object externalCont;
       ace$b megaUserData;
       Map map;
       PerfTracer perfTracer;
       String str = "ut_page_object";
       IpChange $ipChange = WVMega.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("8ea90f8c", objArray).booleanValue();
       }else {
          JSONObject jSONObject = JSON.parseObject(p0);
          String str1 = jSONObject.getString("ability");
          String str2 = jSONObject.getString("action");
          JSONObject jSONObject1 = jSONObject.getJSONObject("options");
          if ((wvMegaBridge = p1.wvMegaBridgeContext) != null && wvMegaBridge.getEnableLoggingAPIInvocation()) {
             lcn.a(RVLLevel.Info, "Bridge").k("invoke", wvMegaBridge.getCallToken()).m(wvMegaBridge.getParentId()).a("name", "WVMega.call").a("params", jSONObject).f();
          }
          xq oxq = new xq();
          IWVWebView webview = p1.getWebview();
          String str3 = null;
          if (webview != null) {
             if (webview instanceof WVUCWebView) {
                if ((externalCont = webview.getExternalContext(str)) != null) {
                   oxq.d(str, externalCont);
                }
                oxq.p(webview);
                ace webViewConte = webview.getWebViewContext();
                str3 = webViewConte.getCustomMegaNamespace();
                String customMegaBi = webViewConte.getCustomMegaBizId();
                if ((megaUserData = webViewConte.getMegaUserDataMapAdapter()) != null && (map = megaUserData.a(str1, str2, p0)) != null) {
                   Iterator iterator = map.entrySet().iterator();
                   while (iterator.hasNext()) {
                      Map$Entry uEntry = iterator.next();
                      String key = uEntry.getKey();
                      oxq.d(key, uEntry.getValue());
                   }
                }
                if ((perfTracer = oxq.i()) != null) {
                   perfTracer.E("wvEnableAsyncJSAPIChannel", String.valueOf(webViewConte.getEnableAsyncJSAPIChannel()));
                }
                perfTracer = str3;
                str3 = customMegaBi;
             }else {
                p0 = str3;
             }
             oxq.d("url", p2);
             oxq.d("pageId", p1.getPid());
          }else {
             p0 = str3;
          }
          if (vpw.commonConfig.o2 != null) {
             oxq.j(x74.b(p2, str3, perfTracer, this.mContext));
          }
          this.adapter.j(str1, str2, oxq, jSONObject1, new WVMega$2(this, p1));
          return 1;
       }
    }
    public boolean execute(String p0,String p1,WVCallBackContext p2){
       IpChange $ipChange = WVMega.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("bcd41fd1", objArray).booleanValue();
       }else if("call".equals(p0)){
          return this.call(p1, p2);
       }else {
          return 1;
       }
    }
    public void initialize(Context p0,IWVWebView p1){
       IpChange $ipChange = WVMega.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("940c25b5", objArray);
          return;
       }else {
          super.initialize(p0, p1);
          if (p1 instanceof WVUCWebView) {
             this.adapter = p1.getAbilityHubAdapter();
          }
          return;
       }
    }
}
