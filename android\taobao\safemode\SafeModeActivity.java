package android.taobao.safemode.SafeModeActivity;
import android.view.View$OnClickListener;
import tb.lpo;
import android.app.Activity;
import java.lang.String;
import java.lang.Object;
import android.content.Context;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.os.Bundle;
import com.android.alibaba.ip.runtime.IpChange;
import android.app.ActivityManager;
import java.util.List;
import java.util.Iterator;
import android.app.ActivityManager$RunningAppProcessInfo;
import java.lang.CharSequence;
import android.os.Process;
import tb.acq;
import tb.kpo;
import android.view.View;
import com.taobao.taobao.R$id;
import android.widget.TextView;
import com.taobao.taobao.R$string;
import com.taobao.taobao.R$layout;
import android.content.Intent;
import android.app.Application;
import com.taobao.schedule.ViewProxy;
import android.content.SharedPreferences;

public class SafeModeActivity extends Activity implements View$OnClickListener, lpo	// class@000144 from classes.dex
{
    public boolean a;
    public kpo b;
    public boolean c;
    public String d;
    public boolean e;
    public static IpChange $ipChange;

    public void SafeModeActivity(){
       super();
       this.a = false;
       this.c = true;
       this.d = "";
       this.e = true;
    }
    public static Object ipc$super(SafeModeActivity p0,String p1,Object[] p2){
       int i;
       if ((i = p1.hashCode()) != -1504501726) {
          if (i != -641568046) {
             if (i != 0x1eb0a9a8) {
                throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/safemode/SafeModeActivity");
             }
             super.attachBaseContext(p2[0]);
             return null;
          }else {
             super.onCreate(p2[0]);
             return null;
          }
       }else {
          super.onDestroy();
          return null;
       }
    }
    public final void a(){
       IpChange $ipChange = SafeModeActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("2f3aa1c4", objArray);
          return;
       }else {
          Iterator iterator = this.getSystemService("activity").getRunningAppProcesses().iterator();
          while (iterator.hasNext()) {
             ActivityManager$RunningAppProcessInfo runningAppPr = iterator.next();
             if (runningAppPr.processName.contains(this.getPackageName()) && !runningAppPr.processName.contains(":safemode")) {
                Process.killProcess(runningAppPr.pid);
             }
          }
          return;
       }
    }
    public void attachBaseContext(Context p0){
       IpChange $ipChange = SafeModeActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("1eb0a9a8", objArray);
          return;
       }else {
          super.attachBaseContext(p0);
          acq.B(p0);
          return;
       }
    }
    public final void b(){
       int i = 1;
       IpChange $ipChange = SafeModeActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = this;
          $ipChange.ipc$dispatch("fede197", objArray);
          return;
       }else if(this.a == null){
          this.a = i;
          this.b.c();
       }
       return;
    }
    public void onClick(View p0){
       int i = 1;
       IpChange $ipChange = SafeModeActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("8dfcefe2", objArray);
          return;
       }else {
          int activity_saf = R$id.activity_safemode_btn_fix;
          if (p0.getId() == activity_saf) {
             if (this.e != null) {
                this.b.b();
             }
             this.findViewById(activity_saf).setText(R$string.safemode_fixing);
             this.b();
          }else {
             this.b.d(i);
          }
          return;
       }
    }
    public void onCreate(Bundle p0){
       IpChange $ipChange = SafeModeActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d9c272d2", objArray);
          return;
       }else {
          super.onCreate(p0);
          this.a();
          this.setContentView(R$layout.activity_safemode);
          this.c = this.getIntent().getBooleanExtra("Launch", 1);
          this.d = this.getIntent().getStringExtra("Version");
          this.b = new kpo(this.getApplication(), this.d, this.c, this);
          ViewProxy.setOnClickListener(this.findViewById(R$id.activity_safemode_btn_skip), this);
          this.b.a();
          if ("false".equals(this.getSharedPreferences("SafeModeOrange", 0).getString("downloadPatchAfterClick", "true"))) {
             this.e = false;
          }
          if (this.e == null && this.c != null) {
             this.b.b();
             this.e = false;
          }else {
             this.e = true;
          }
          return;
       }
    }
    public void onDestroy(){
       IpChange $ipChange = SafeModeActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("a6532022", objArray);
          return;
       }else {
          super.onDestroy();
          Process.killProcess(Process.myPid());
          return;
       }
    }
    public void onFinish(){
       IpChange $ipChange = SafeModeActivity.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("badeed9", objArray);
          return;
       }else {
          this.b.d(0);
          return;
       }
    }
}
