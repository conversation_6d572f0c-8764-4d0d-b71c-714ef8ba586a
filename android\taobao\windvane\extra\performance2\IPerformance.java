package android.taobao.windvane.extra.performance2.IPerformance;
import java.lang.String;
import android.taobao.windvane.extra.performance2.WVPerformance;
import org.json.JSONObject;

public interface abstract IPerformance	// class@0001d6 from classes.dex
{

    String getCachedUrl();
    WVPerformance getPerformanceDelegate();
    boolean isPreInit();
    boolean isReportedFSP();
    void receiveJSMessageForCustomizedFSP(long p0);
    void receiveJSMessageForCustomizedStage(long p0,String p1);
    void receiveJSMessageForFP(long p0);
    void receiveJSMessageForFSP(long p0);
    void receiveJSMessageForTTI(long p0);
    void receiveOnProperty(JSONObject p0);
    void receiveOnStage(String p0);
    void setPreInitState(boolean p0);
    void setReportedFSP(boolean p0);
}
