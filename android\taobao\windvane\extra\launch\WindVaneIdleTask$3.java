package android.taobao.windvane.extra.launch.WindVaneIdleTask$3;
import tb.bk7;
import android.taobao.windvane.extra.launch.WindVaneIdleTask;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import java.io.File;
import com.android.alibaba.ip.runtime.IpChange;
import tb.v7t;

public class WindVaneIdleTask$3 extends bk7	// class@0001bd from classes.dex
{
    public final WindVaneIdleTask this$0;
    public static IpChange $ipChange;

    public void WindVaneIdleTask$3(WindVaneIdleTask p0){
       this.this$0 = p0;
       super();
    }
    public static Object ipc$super(WindVaneIdleTask$3 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/launch/WindVaneIdleTask$3");
    }
    public void onFailed(File p0,String p1){
       int i = 0;
       IpChange $ipChange = WindVaneIdleTask$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("acf9a904", objArray);
          return;
       }else {
          v7t.i("DexOptimizer", "optimize onFailed: "+p1);
          WindVaneIdleTask.access$200(this.this$0, p0, i);
          return;
       }
    }
    public void onSuccess(File p0,String p1){
       IpChange $ipChange = WindVaneIdleTask$3.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("2c4f8b2c", objArray);
          return;
       }else {
          v7t.i("DexOptimizer", "optimize success: "+p1);
          WindVaneIdleTask.access$200(this.this$0, p0, 1);
          return;
       }
    }
}
