package android.taobao.windvane.webview.WVWebViewClient$2;
import android.webkit.ValueCallback;
import android.taobao.windvane.webview.WVWebViewClient;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import tb.csw;
import tb.trw;

public class WVWebViewClient$2 implements ValueCallback	// class@000316 from classes.dex
{
    public final WVWebViewClient this$0;
    public final String val$monitorUrl;
    public static IpChange $ipChange;

    public void WVWebViewClient$2(WVWebViewClient p0,String p1){
       this.this$0 = p0;
       this.val$monitorUrl = p1;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       IpChange $ipChange = WVWebViewClient$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else if(trw.getPerformanceMonitor() != null){
          trw.getPerformanceMonitor().didPagePerformanceInfo(this.val$monitorUrl, p0);
          trw.getPerformanceMonitor().didPageFinishLoadAtTime(this.val$monitorUrl, WVWebViewClient.access$000(this.this$0));
       }
       return;
    }
}
