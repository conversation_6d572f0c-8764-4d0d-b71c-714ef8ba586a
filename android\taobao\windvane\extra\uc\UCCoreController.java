package android.taobao.windvane.extra.uc.UCCoreController;
import tb.t2o;
import java.util.concurrent.atomic.AtomicBoolean;
import java.lang.Object;
import tb.abq;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import android.taobao.windvane.extra.uc.UCCoreController$1;
import com.uc.webview.export.extension.Sdk2CoreHost;
import android.webkit.ValueCallback;

public class UCCoreController	// class@00021e from classes.dex
{
    public static IpChange $ipChange;
    private static final String TAG;
    private static final AtomicBoolean sRegisteredThreadANRCallback;

    static {
       t2o.a(0x3d800152);
       UCCoreController.sRegisteredThreadANRCallback = new AtomicBoolean(false);
    }
    public void UCCoreController(){
       super();
    }
    public static void registerThreadANRCallback(abq p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = UCCoreController.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          $ipChange.ipc$dispatch("f75e21bc", objArray);
          return;
       }else if(UCCoreController.sRegisteredThreadANRCallback.compareAndSet(i, i1)){
          p0.e("registerThreadANRCallback");
          Sdk2CoreHost.instance().setThreadNotRespondingCallback(new UCCoreController$1(p0));
       }
       return;
    }
}
