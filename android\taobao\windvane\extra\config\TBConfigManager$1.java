package android.taobao.windvane.extra.config.TBConfigManager$1;
import tb.z8l;
import android.taobao.windvane.extra.config.TBConfigManager;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import com.taobao.orange.OrangeConfig;
import android.taobao.windvane.jsbridge.WVJsBridge;
import java.lang.StringBuilder;
import tb.yaa;
import java.nio.ByteBuffer;
import tb.sb9;
import java.lang.Throwable;

public class TBConfigManager$1 implements z8l	// class@00018b from classes.dex
{
    public final TBConfigManager this$0;
    public static IpChange $ipChange;

    public void TBConfigManager$1(TBConfigManager p0){
       this.this$0 = p0;
       super();
    }
    public void onConfigUpdate(String p0,boolean p1){
       String str = ",";
       IpChange $ipChange = TBConfigManager$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,new Boolean(p1)};
          $ipChange.ipc$dispatch("9458c0f9", objArray);
          return;
       }else {
          String str1 = "WindVane";
          if (p0.equalsIgnoreCase(str1)) {
             String str2 = "0";
             WVJsBridge.f = OrangeConfig.getInstance().getConfig(str1, "enableGetParamByJs", str2).equals("1");
             p0 = OrangeConfig.getInstance().getConfig(str1, "useOrange", "true");
             str1 = OrangeConfig.getInstance().getConfig(str1, "closeUCByRom", str2);
             TBConfigManager$1 tthis$0 = this.this$0;
             try{
                TBConfigManager.access$000(tthis$0);
                sb9.h(TBConfigManager.access$100(this.this$0), ByteBuffer.wrap((yaa.f().b()+str+p0+str+str1).getBytes("utf-8")));
             }catch(android.taobao.windvane.file.NotEnoughSpace e5){
                e5.printStackTrace();
             }catch(java.io.UnsupportedEncodingException e5){
                e5.printStackTrace();
             }
          }
          return;
       }
    }
}
