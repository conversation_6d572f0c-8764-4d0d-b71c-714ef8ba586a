package android.taobao.windvane.export.network.b;
import tb.t2o;
import java.util.LinkedHashMap;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import android.taobao.windvane.export.network.RequestCallback;
import tb.esd;
import java.lang.Integer;
import java.lang.Boolean;
import java.util.Map;
import android.taobao.windvane.export.network.RequestCache;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import android.taobao.windvane.export.network.Request;
import tb.ecd;
import com.taobao.android.riverlogger.RVLLevel;
import java.lang.StringBuilder;
import java.lang.Throwable;
import tb.lcn;
import android.taobao.windvane.export.network.b$c;
import tb.icn;
import java.lang.Number;
import tb.vpw;
import tb.wpw;
import tb.jfq;
import android.taobao.windvane.export.network.a;
import android.taobao.windvane.export.network.b$a;
import android.os.Handler;
import tb.rsa;
import android.taobao.windvane.export.network.b$b;
import java.lang.Runnable;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import tb.y71;

public class b	// class@000174 from classes.dex
{
    public static IpChange $ipChange;
    public static final Map a;
    public static final Object b;

    static {
       t2o.a(0x3d800082);
       b.a = new LinkedHashMap();
       b.b = new Object();
    }
    public static void a(String p0){
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("38fae181", objArray);
          return;
       }else {
          b.k(p0);
          return;
       }
    }
    public static boolean b(int p0,RequestCallback p1,esd p2){
       Object b;
       int i = 0;
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Integer(p0),p1,p2};
          return $ipChange.ipc$dispatch("8433afff", objArray).booleanValue();
       }else {
          b = b.b;
          _monitor_enter(b);
          RequestCache requestCache = b.a.remove(Integer.valueOf(p0));
          _monitor_exit(b);
          if (requestCache == null) {
             return i;
          }
          return b.d(requestCache, p1, p2);
       }
    }
    public static boolean c(String p0,RequestCallback p1,esd p2){
       Object b;
       int i;
       Integer integer;
       Request request;
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2};
          return $ipChange.ipc$dispatch("49d73f21", objArray).booleanValue();
       }else {
          b = b.b;
          _monitor_enter(b);
          RequestCache requestCache = null;
          try{
             Iterator iterator = b.a.entrySet().iterator();
             while (true) {
                if (iterator.hasNext()) {
                   Map$Entry uEntry = iterator.next();
                   if ((i = uEntry.getValue()) == null || (!i.d() || ((request = i.c()) == null || !request.g().a(p0, request.j())))) {
                      continue ;
                   }else {
                      try{
                         integer = uEntry.getKey();
                      label_008d :
                         requestCache = i;
                      }catch(java.lang.Exception e7){
                      }
                      lcn.f(RVLLevel.Error, "Themis/Performance/RequestPrefetch", "consumePrefetchResponseWithUrl error: "+e7.getMessage());
                      integer = requestCache;
                      goto label_008d ;
                   }
                }else {
                   integer = requestCache;
                }
                if (requestCache != null && integer != null) {
                   b.a.remove(integer);
                   break ;
                }
                break ;
             }
             _monitor_exit(b);
             return b.d(requestCache, p1, p2);
          }catch(java.lang.Exception e7){
             i = requestCache;
          }
       }
    }
    public static boolean d(RequestCache p0,RequestCallback p1,esd p2){
       int i = 0;
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,p2};
          return $ipChange.ipc$dispatch("47c7967", objArray).booleanValue();
       }else if(p0 == null){
          return i;
       }else if(!p0.b(p1)){
          return i;
       }else {
          Request request = p0.c();
          request.l("documentPrefetchHitTime");
          request.b(new b$c(p2, request));
          lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("consume").a("requestId", Integer.valueOf(request.h())).a("url", request.j()).a("isShared", Boolean.valueOf(p0.d())).f();
          return 1;
       }
    }
    public static boolean e(int p0){
       Object b;
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Integer(p0)};
          return $ipChange.ipc$dispatch("c024c083", objArray).booleanValue();
       }else {
          b = b.b;
          _monitor_enter(b);
          _monitor_exit(b);
          return b.a.containsKey(Integer.valueOf(p0));
       }
    }
    public static boolean f(String p0){
       Object b;
       RequestCache value;
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("a6ce03d2", objArray).booleanValue();
       }else {
          b = b.b;
          _monitor_enter(b);
          RequestCache requestCache = null;
          try{
             Iterator iterator = b.a.entrySet().iterator();
             while (iterator.hasNext()) {
                if ((value = iterator.next().getValue()) == null || !value.d()) {
                   continue ;
                }else {
                   Request request = value.c();
                   ecd uoecd = request.g();
                   if (uoecd.a(p0, request.j())) {
                      requestCache = value;
                      break ;
                   }
                }
             }
          }catch(java.lang.Exception e8){
             lcn.f(RVLLevel.Error, "Themis/Performance/RequestPrefetch", "consumePrefetchResponseWithUrl error: "+e8.getMessage());
          }
          _monitor_exit(b);
          if (requestCache != null && requestCache.d()) {
             i = true;
          }
          return i;
       }
    }
    public static int g(Request p0,RequestCallback p1){
       Object b;
       RequestCache value;
       Request request;
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          return $ipChange.ipc$dispatch("a2e9751c", objArray).intValue();
       }else if(vpw.commonConfig.v3 != null){
          b = b.b;
          _monitor_enter(b);
          try{
             Iterator iterator = b.a.entrySet().iterator();
             while (true) {
                if (iterator.hasNext()) {
                   if ((value = iterator.next().getValue()) == null || (!value.d() || ((request = value.c()) == null || !request.g().a(p0.j(), request.j())))) {
                      continue ;
                   }else {
                      lcn.a(RVLLevel.Warn, "Themis/Performance/RequestPrefetch").j("prefetch_sharedResource_duplicate").a("url", p0.j()).a("msg", "normal sharedResource url already exist").f();
                      _monitor_exit(b);
                      return request.h();
                   }
                }else {
                   _monitor_exit(b);
                   break ;
                }
             }
          }catch(java.lang.Exception e0){
          }
       }
       return b.h(p0, p1, e0);
    }
    public static int h(Request p0,RequestCallback p1,boolean p2){
       Handler handler;
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1,new Boolean(p2)};
          return $ipChange.ipc$dispatch("d602a01", objArray).intValue();
       }else {
          Request request = new Request(p0);
          request.l("documentRequestStart");
          int i = -1;
          if (request.d()) {
             if (jfq.a(request.j())) {
                request.a("x-s-first-chunk", "true");
             }else if(p1 != null){
                p1.onError(i, "url not in ssr white list");
             }
             return request.h();
          }
          RequestCache requestCache = new RequestCache(request);
          requestCache.e(p2);
          int i1 = request.h();
          String str = request.j();
          try{
             i = b.a.size();
             lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("sendRequest").a("existingPrefetchCount", Integer.valueOf(i)).a("requestId", Integer.valueOf(i1)).a("url", e0.j()).a("headers", e0.c()).f();
             new a().c(e0, new b$a(e0, requestCache, p1, i1), null);
             p1 = b.b;
             _monitor_enter(p1);
             b.a.put(Integer.valueOf(i1), requestCache);
             _monitor_exit(p1);
             if ((handler = rsa.b().a()) != null) {
                handler.postDelayed(new b$b(i1, str), 0x7530);
             }
             return i1;
          }catch(java.lang.Exception e0){
          }
       }
    }
    public static void i(Request p0){
       Object b;
       RequestCache value;
       Request request;
       int i = 1;
       String str = "sharedResource prefetch count exceed limit, url: ";
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          $ipChange.ipc$dispatch("a67a7849", objArray);
          return;
       }else if(vpw.commonConfig.m2 == null){
          lcn.a(RVLLevel.Error, "Themis/Performance/RequestPrefetch").j("sharedResourcePrefetch disabled").f();
          return;
       }else {
          b = b.b;
          _monitor_enter(b);
          Map a = b.a;
          if (a.size() >= 30) {
             lcn.f(RVLLevel.Error, "Themis/Performance/RequestPrefetch", str+p0.j());
             _monitor_exit(b);
             return;
          }else {
             try{
                Iterator iterator = a.entrySet().iterator();
                while (true) {
                   if (iterator.hasNext()) {
                      if ((value = iterator.next().getValue()) == null || (!value.d() || ((request = value.c()) == null || !request.g().a(p0.j(), request.j())))) {
                         continue ;
                      }else {
                         break ;
                      }
                   }else {
                      _monitor_exit(b);
                      b.h(p0, null, e0);
                      return;
                   }
                }
                lcn.f(RVLLevel.Error, "Themis/Performance/RequestPrefetch", "sharedResource url already exist, url: "+p0.j());
                _monitor_exit(b);
                return;
             }catch(java.lang.Exception e0){
             }
          }
       }
    }
    public static boolean j(int p0){
       Object b;
       int i = 0;
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{new Integer(p0)};
          return $ipChange.ipc$dispatch("ebab395c", objArray).booleanValue();
       }else {
          b = b.b;
          _monitor_enter(b);
          if (b.a.remove(Integer.valueOf(p0)) != null) {
             i = true;
          }
          _monitor_exit(b);
          return i;
       }
    }
    public static void k(String p0){
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0};
          $ipChange.ipc$dispatch("6f60186b", objArray);
          return;
       }else {
          JSONObject $ipChange1 = new JSONObject();
          $ipChange1.put("url", p0);
          y71.commitSuccess("wvPrefetchExpired", $ipChange1.toJSONString());
          return;
       }
    }
    public static boolean l(RequestCache p0){
       int i = 0;
       int i1 = 1;
       IpChange $ipChange = b.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i1];
          objArray[i] = p0;
          return $ipChange.ipc$dispatch("61eb64b2", objArray).booleanValue();
       }else {
          int i2 = p0.c().h();
          Map a = b.a;
          if (a.containsKey(Integer.valueOf(i2))) {
             lcn.f(RVLLevel.Error, "Themis/Performance/RequestPrefetch", "the request needed to be restore cache has existed, requestId: "+i2);
             return i;
          }else {
             a.put(Integer.valueOf(p0.c().h()), p0);
             lcn.a(RVLLevel.Info, "Themis/Performance/RequestPrefetch").j("restore").a("requestId", Integer.valueOf(i2)).a("url", p0.c().j()).a("isShared", Boolean.valueOf(p0.d())).f();
             return i1;
          }
       }
    }
}
