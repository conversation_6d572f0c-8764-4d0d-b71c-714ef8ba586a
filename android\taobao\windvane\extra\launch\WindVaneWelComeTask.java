package android.taobao.windvane.extra.launch.WindVaneWelComeTask;
import android.taobao.windvane.extra.launch.InitOnceTask;
import tb.t2o;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import java.lang.String;
import java.lang.Boolean;
import android.app.Application;
import tb.dpw;
import tb.x7j;
import java.util.HashMap;
import android.taobao.windvane.startup.UCInitializerInfo;
import tb.yaa;
import tb.mpw;
import tb.lpw;
import tb.yru;
import tb.og8;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.os.Build;
import java.lang.CharSequence;
import tb.v7t;
import java.lang.Integer;
import tb.xae;
import tb.mrt;
import android.taobao.windvane.extra.launch.WindVaneWelComeTask$2;
import java.lang.Runnable;
import android.taobao.windvane.config.EnvEnum;
import android.taobao.windvane.WindVaneSDK;
import tb.lex;
import tb.r9u;
import android.content.Context;
import com.ut.device.UTDevice;
import android.taobao.windvane.extra.launch.WindVaneWelComeTask$1;
import tb.b0s;
import android.taobao.windvane.extra.launch.WindVaneWelComeTask$3;
import java.util.concurrent.Future;
import android.taobao.windvane.extra.launch.WindVaneWelComeTask$4;

public class WindVaneWelComeTask extends InitOnceTask	// class@0001c7 from classes.dex
{
    public static IpChange $ipChange;
    private static final String TAG;

    static {
       t2o.a(0x3d8000f8);
    }
    public void WindVaneWelComeTask(){
       super();
    }
    public static boolean access$000(){
       IpChange $ipChange = WindVaneWelComeTask.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WindVaneWelComeTask.isXiaomiOrRedmi();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("bcd6077b", objArray).booleanValue();
    }
    public static boolean access$100(){
       IpChange $ipChange = WindVaneWelComeTask.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WindVaneWelComeTask.isMeiZu();
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("f1bc42bc", objArray).booleanValue();
    }
    public static void access$200(WindVaneWelComeTask p0,Application p1){
       IpChange $ipChange = WindVaneWelComeTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{p0,p1};
          $ipChange.ipc$dispatch("d11065db", objArray);
          return;
       }else {
          p0.preloadDexAsync(p1);
          return;
       }
    }
    private void initNecessaryAPI(){
       IpChange $ipChange = WindVaneWelComeTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("30366082", objArray);
          return;
       }else {
          dpw.a();
          x7j.a();
          return;
       }
    }
    private void initUCParams(Application p0,HashMap p1){
       int i = 1;
       IpChange $ipChange = WindVaneWelComeTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("3174993f", objArray);
          return;
       }else {
          try{
             UCInitializerInfo.a().c(14);
             yaa.n = p0;
             if (!mpw.a().b()) {
                lpw olpw = this.obtainWVParams(p0, p1);
                mpw.a().c(olpw);
                yru oyru = new yru();
                oyru.a = "windvane";
                oyru.b = "vKFaqtcEUEHI15lIOzsI6jIQldPpaCZ3";
                oyru.e = i;
                oyru.d = i;
                oyru.c = og8.b();
                olpw.g = oyru;
                yaa oyaa = yaa.f();
                oyaa.k(olpw);
                og8.f(p1.get("isDebuggable").booleanValue());
             }
             UCInitializerInfo.a().c(15);
             return;
          }catch(java.lang.Exception e0){
          }
       }
    }
    public static Object ipc$super(WindVaneWelComeTask p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/launch/WindVaneWelComeTask");
    }
    private static boolean isMeiZu(){
       IpChange $ipChange = WindVaneWelComeTask.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("b5bff16d", objArray).booleanValue();
       }else {
          String str = Build.BRAND.toLowerCase();
          String str1 = Build.MODEL.toLowerCase();
          if (Build.MANUFACTURER.toLowerCase().contains("meizu") || (str.contains("meizu") || str1.contains("meizu"))) {
             i = true;
          }
          return i;
       }
    }
    private static boolean isXiaomiOrRedmi(){
       IpChange $ipChange = WindVaneWelComeTask.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("376d9990", objArray).booleanValue();
       }else {
          String str = Build.BRAND.toLowerCase();
          String str1 = Build.MODEL.toLowerCase();
          if (!Build.MANUFACTURER.toLowerCase().contains("xiaomi") && !str.contains("xiaomi")) {
             String str2 = "redmi";
             if (!str.contains(str2) && !str1.contains(str2)) {
             label_0047 :
                return i;
             }
          }
          i = true;
          goto label_0047 ;
       }
    }
    private lpw obtainWVParams(Application p0,HashMap p1){
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = WindVaneWelComeTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          return $ipChange.ipc$dispatch("f3c8a781", objArray);
       }else {
          lpw olpw = new lpw();
          olpw.a = p1.get("ttid");
          i1 = p1.get("envIndex").intValue();
          if (!i1) {
             olpw.c = p1.get("onlineAppKey");
             mrt.a().execute(new WindVaneWelComeTask$2(this));
          }else if(i1 == i){
             olpw.c = p1.get("onlineAppKey");
             WindVaneSDK.setEnvMode(EnvEnum.PRE);
          }else {
             olpw.c = p1.get("dailyAppkey");
             WindVaneSDK.setEnvMode(EnvEnum.DAILY);
          }
          olpw.d = "TB";
          olpw.f = lex.TB_UC_SDK_APP_KEY_SEC;
          olpw.e = "0.0.0";
          olpw.e = p1.get("appVersion");
          r9u.b("getUtdid");
          olpw.b = UTDevice.getUtdid(p0);
          r9u.d();
          return olpw;
       }
    }
    private void preloadDexAsync(Application p0){
       IpChange $ipChange = WindVaneWelComeTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("babc652a", objArray);
          return;
       }else {
          mrt.a().execute(new WindVaneWelComeTask$1(this, p0));
          return;
       }
    }
    public String getName(){
       IpChange $ipChange = WindVaneWelComeTask.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return "WindVaneWelComeTask";
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("7c09e698", objArray);
    }
    public void initImpl(Application p0,HashMap p1){
       IpChange $ipChange = WindVaneWelComeTask.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("73f761cb", objArray);
          return;
       }else {
          r9u.b("TBWindVaneInitializer.init");
          b0s.a();
          r9u.d();
          r9u.b("initUCParams");
          this.initUCParams(p0, p1);
          r9u.d();
          r9u.b("initNecessaryAPI");
          this.initNecessaryAPI();
          r9u.d();
          mrt.a().submit(new WindVaneWelComeTask$3(this, p0));
          mrt.a().execute(new WindVaneWelComeTask$4(this));
          return;
       }
    }
}
