package android.taobao.yuzhuang.ManufacturerProcess;
import java.io.Serializable;
import tb.t2o;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Class;
import java.lang.reflect.Method;
import java.lang.Throwable;
import java.lang.Boolean;
import java.lang.CharSequence;
import android.text.TextUtils;
import android.app.Application;
import java.util.HashMap;
import android.content.Context;
import android.taobao.yuzhuang.ManufacturerProcess$b;

public class ManufacturerProcess implements Serializable	// class@000322 from classes.dex
{
    public static IpChange $ipChange;
    private static final String EMUI_PROPERTY;
    private static final String HARMONYOS_PROPERTY;
    private static final String MANUFACTURER_PROPERTY;
    private static final String TAG;

    static {
       t2o.a(0x30c00023);
    }
    public void ManufacturerProcess(){
       super();
    }
    public static String access$000(String p0){
       IpChange $ipChange = ManufacturerProcess.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return ManufacturerProcess.getMiuiChannelPath(p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("28c0253f", objArray);
    }
    public static String access$100(String p0){
       IpChange $ipChange = ManufacturerProcess.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return ManufacturerProcess.getSystemProperties(p0);
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("30255a5e", objArray);
    }
    private static String getMiuiChannelPath(String p0){
       int i = 1;
       try{
          Class[] uClassArray = new Class[i];
          uClassArray[0] = String.class;
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          return Class.forName("miui.os.MiuiInit").getMethod("getMiuiChannelPath", uClassArray).invoke(null, objArray);
       }catch(java.lang.Exception e0){
          return "";
       }
    }
    private static String getProp(String p0){
       int i = 1;
       try{
          Class[] uClassArray = new Class[i];
          uClassArray[0] = String.class;
          Object[] objArray = new Object[i];
          objArray[0] = p0;
          p0 = Class.forName("android.os.SystemProperties").getDeclaredMethod("get", uClassArray).invoke(null, objArray);
       label_002d :
          return p0;
       }catch(java.lang.ClassNotFoundException e6){
       }catch(java.lang.NoSuchMethodException e6){
       }catch(java.lang.IllegalAccessException e6){
       }catch(java.lang.reflect.InvocationTargetException e6){
       }
       e6.printStackTrace();
       p0 = "";
       goto label_002d ;
    }
    private static String getSystemProperties(String p0){
       Class uClass = Class.forName("android.os.SystemProperties");
       Class[] uClassArray = new Class[]{String.class,String.class};
       Object[] objArray = new Object[]{p0,null};
       return uClass.getMethod("get", uClassArray).invoke(uClass, objArray);
    }
    public static boolean isHuaweiPhone(){
       IpChange $ipChange = ManufacturerProcess.$ipChange;
       int i = 0;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[i];
          return $ipChange.ipc$dispatch("7e37e48", objArray).booleanValue();
       }else {
          String prop = ManufacturerProcess.getProp("hw_sc.build.platform.version");
          if (!TextUtils.isEmpty(ManufacturerProcess.getProp("ro.build.version.emui")) || !TextUtils.isEmpty(prop)) {
             i = true;
          }
          return i;
       }
    }
    public void init(Application p0,HashMap p1){
       IpChange $ipChange = ManufacturerProcess.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("dddb138b", objArray);
          return;
       }else {
          ManufacturerProcess$b.e(p0).g();
          return;
       }
    }
}
