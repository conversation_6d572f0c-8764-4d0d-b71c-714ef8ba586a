package androidx.activity.compose.OnBackInstance$job$1$1;
import tb.w1a;
import kotlin.coroutines.jvm.internal.SuspendLambda;
import kotlin.jvm.internal.Ref$BooleanRef;
import tb.ar4;
import java.lang.Object;
import tb.sp9;
import java.lang.Throwable;
import tb.xhv;
import tb.dkf;
import kotlin.b;
import java.lang.IllegalStateException;
import java.lang.String;

public final class OnBackInstance$job$1$1 extends SuspendLambda implements w1a	// class@00048a from classes.dex
{
    public final Ref$BooleanRef $completed;
    public int label;

    public void OnBackInstance$job$1$1(Ref$BooleanRef p0,ar4 p1){
       this.$completed = p0;
       super(3, p1);
    }
    public Object invoke(Object p0,Object p1,Object p2){
       return this.invoke(p0, p1, p2);
    }
    public final Object invoke(sp9 p0,Throwable p1,ar4 p2){
       return new OnBackInstance$job$1$1(this.$completed, p2).invokeSuspend(xhv.INSTANCE);
    }
    public final Object invokeSuspend(Object p0){
       dkf.d();
       if (this.label != null) {
          throw new IllegalStateException("call to \'resume\' before \'invoke\' with coroutine");
       }
       b.b(p0);
       p0.element = true;
       return xhv.INSTANCE;
    }
}
