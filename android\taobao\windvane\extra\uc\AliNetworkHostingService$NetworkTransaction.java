package android.taobao.windvane.extra.uc.AliNetworkHostingService$NetworkTransaction;
import com.uc.webview.export.extension.INetworkHostingService$ITransaction;
import tb.t2o;
import android.taobao.windvane.extra.uc.AliNetworkHostingService;
import com.uc.webview.export.WebView;
import java.lang.String;
import java.util.concurrent.ConcurrentHashMap;
import android.taobao.windvane.extra.uc.WVUCWebView;
import tb.ace;
import tb.vpw;
import tb.wpw;
import android.view.View;
import android.taobao.windvane.extra.uc.interfaces.IRequestTiming;
import android.taobao.windvane.extra.performance.WVH5PPManager;
import android.taobao.windvane.extra.uc.timing.RequestTiming;
import java.lang.Object;
import android.taobao.windvane.extra.uc.timing.RequestTimingMap;
import java.util.AbstractMap;
import android.taobao.windvane.extra.uc.AliNetworkAdapterNew;
import android.content.Context;
import tb.urb;
import com.uc.webview.export.extension.INetworkHostingService$IDelegate;
import com.android.alibaba.ip.runtime.IpChange;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import android.taobao.windvane.extra.uc.interfaces.IRequest;
import android.taobao.windvane.extra.uc.AliRequestAdapter;
import anetwork.channel.Request;
import android.taobao.windvane.extra.uc.MTopSSRRequest;
import tb.v7t;
import android.taobao.windvane.extra.uc.AliNetworkHostingService$BodyHandlerAdapter;
import com.uc.webview.export.extension.INetworkHostingService$IUploadStream;
import anetwork.channel.IBodyHandler;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.util.Map;
import java.lang.Integer;
import android.taobao.windvane.extra.uc.AliNetworkHostingService$EventHandlerAdapter;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import android.taobao.windvane.thread.WVThreadPool;
import android.taobao.windvane.extra.uc.AliNetworkHostingService$NetworkTransaction$1;
import java.lang.Runnable;

public class AliNetworkHostingService$NetworkTransaction extends INetworkHostingService$ITransaction	// class@000205 from classes.dex
{
    private AliNetworkAdapterNew mAliNetwork;
    private INetworkHostingService$IDelegate mDelegate;
    private final Map mExtraInfo;
    private final Map mHeaders;
    private String mMethod;
    private IRequest mRequest;
    private int mRequestFlag;
    private IRequestTiming mRequestTiming;
    private final int mRequestType;
    private INetworkHostingService$IUploadStream mUploadStream;
    private final String mUrl;
    private WVUCWebView mWebView;
    public final AliNetworkHostingService this$0;
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d80013a);
    }
    public void AliNetworkHostingService$NetworkTransaction(AliNetworkHostingService p0,WebView p1,int p2,String p3){
       WVUCWebView overrideBizI;
       this.this$0 = p0;
       super();
       this.mHeaders = new ConcurrentHashMap();
       this.mExtraInfo = new ConcurrentHashMap();
       this.mRequestType = AliNetworkHostingService.access$000(p2);
       this.mUrl = p3;
       p2 = (!p2)? 1: 0;
       AliNetworkAdapterNew uAliNetworkA = null;
       if (p1 instanceof WVUCWebView) {
       }else {
          AliNetworkAdapterNew uAliNetworkA3 = uAliNetworkA;
       }
       if (p1 != null && (p3 != null && p1.getWebViewContext().getEnableNetworkTracing())) {
          if (vpw.commonConfig.B3 != null) {
             IRequestTiming iRequestTimi = WVH5PPManager.openRequestTiming(p1, p3);
             this.mRequestTiming = iRequestTimi;
             if (iRequestTimi != null) {
                iRequestTimi.markNativeRequestInitTime();
             }
          }else {
             RequestTiming requestTimin = new RequestTiming();
             this.mRequestTiming = requestTimin;
             requestTimin.markNativeRequestInitTime();
             Object externalCont = p1.getExternalContext("requestTiming");
             if (externalCont instanceof RequestTimingMap) {
                externalCont.put(p3, this.mRequestTiming);
             }
          }
       }
       wpw commonConfig = vpw.commonConfig;
       if (commonConfig.z2 != null) {
          this.mWebView = p1;
       }
       if (p1 != null && p2) {
          if ((overrideBizI = p1.overrideBizId) == null) {
             overrideBizI = p1.bizCode;
          }
          AliNetworkAdapterNew uAliNetworkA1 = new AliNetworkAdapterNew(AliNetworkHostingService.access$100(p0), overrideBizI, p1);
          this.mAliNetwork = uAliNetworkA1;
          uAliNetworkA1.setId(p1);
          p1.setAliNetwork(this.mAliNetwork);
       }else if(p1 != null){
          uAliNetworkA = p1.getAliNetwork();
       }
       this.mAliNetwork = uAliNetworkA;
       if (this.mAliNetwork == null) {
          if (commonConfig.M1 != null && p1 != null) {
             if ((overrideBizI = p1.overrideBizId) == null) {
                overrideBizI = p1.bizCode;
             }
             AliNetworkAdapterNew uAliNetworkA2 = new AliNetworkAdapterNew(AliNetworkHostingService.access$100(p0), overrideBizI, p1);
             this.mAliNetwork = uAliNetworkA2;
             uAliNetworkA2.setId(p1);
             p1.setAliNetwork(this.mAliNetwork);
          }else {
             this.mAliNetwork = new AliNetworkAdapterNew(AliNetworkHostingService.access$100(p0));
          }
       }
       return;
    }
    public static INetworkHostingService$IDelegate access$200(AliNetworkHostingService$NetworkTransaction p0){
       IpChange $ipChange = AliNetworkHostingService$NetworkTransaction.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return p0.mDelegate;
       }
       Object[] objArray = new Object[]{p0};
       return $ipChange.ipc$dispatch("536f84bc", objArray);
    }
    public static Object ipc$super(AliNetworkHostingService$NetworkTransaction p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/uc/AliNetworkHostingService$NetworkTransaction");
    }
    private void setupUploadStream(IRequest p0){
       Request aliRequest;
       IpChange $ipChange = AliNetworkHostingService$NetworkTransaction.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("7fec8e57", objArray);
          return;
       }else if(this.mUploadStream == null){
          return;
       }else if(p0 instanceof AliRequestAdapter){
          aliRequest = p0.getAliRequest();
       }else {
          String str = "alinetwork-service";
          if (p0 instanceof MTopSSRRequest) {
             v7t.d(str, "setupUploadStream: mtop has upload data");
          }else {
             v7t.d(str, "setupUploadStream: unsupported request type "+p0);
          }
          aliRequest = null;
       }
       if (aliRequest != null) {
          aliRequest.setBodyHandler(new AliNetworkHostingService$BodyHandlerAdapter(this.this$0, this.mUploadStream));
       }
       return;
    }
    public void cancel(){
       AliNetworkHostingService$NetworkTransaction tmRequest;
       IpChange $ipChange = AliNetworkHostingService$NetworkTransaction.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("707fe601", objArray);
          return;
       }else if((tmRequest = this.mRequest) != null){
          tmRequest.cancel();
       }
       return;
    }
    public void setDelegate(INetworkHostingService$IDelegate p0){
       IpChange $ipChange = AliNetworkHostingService$NetworkTransaction.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d1971691", objArray);
          return;
       }else {
          this.mDelegate = p0;
          return;
       }
    }
    public void setExtraInfo(String p0,String p1){
       IpChange $ipChange = AliNetworkHostingService$NetworkTransaction.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("b3768cb7", objArray);
          return;
       }else if(!TextUtils.isEmpty(p0) && !TextUtils.isEmpty(p1)){
          this.mExtraInfo.put(p0, p1);
       }
       return;
    }
    public void setHeader(String p0,String p1){
       IpChange $ipChange = AliNetworkHostingService$NetworkTransaction.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("821380aa", objArray);
          return;
       }else if(!TextUtils.isEmpty(p0) && !TextUtils.isEmpty(p1)){
          if (this.mHeaders.containsKey(p0)) {
             AliNetworkHostingService$NetworkTransaction tmHeaders = this.mHeaders;
             _monitor_enter(tmHeaders);
             p1 = this.mHeaders.get(p0)+"; "+p1;
             _monitor_exit(tmHeaders);
          }
          this.mHeaders.put(p0, p1);
       }
       return;
    }
    public void setMethod(String p0){
       IpChange $ipChange = AliNetworkHostingService$NetworkTransaction.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("dc10634", objArray);
          return;
       }else {
          this.mMethod = p0;
          return;
       }
    }
    public void setRequestFlags(int p0){
       IpChange $ipChange = AliNetworkHostingService$NetworkTransaction.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0)};
          $ipChange.ipc$dispatch("c81348e2", objArray);
          return;
       }else {
          this.mRequestFlag = p0;
          return;
       }
    }
    public void setUploadStream(INetworkHostingService$IUploadStream p0){
       IpChange $ipChange = AliNetworkHostingService$NetworkTransaction.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("b1e0ee11", objArray);
          return;
       }else {
          this.mUploadStream = p0;
          return;
       }
    }
    public void start(){
       AliNetworkHostingService$NetworkTransaction tmRequestTim;
       IpChange $ipChange = AliNetworkHostingService$NetworkTransaction.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("810347e9", objArray);
          return;
       }else {
          String str = "alinetwork-service";
          if (this.mDelegate == null) {
             v7t.d(str, "start failed: delegate is null");
             return;
          }else if((tmRequestTim = this.mRequestTiming) != null){
             tmRequestTim.markNativeRequestStartTime();
          }
          AliNetworkHostingService$EventHandlerAdapter tmRequestTim1 = new AliNetworkHostingService$EventHandlerAdapter(this.this$0, this.mUrl, this.mDelegate);
          tmRequestTim1.setRequestTiming(this.mRequestTiming);
          tmRequestTim1.setWebView(this.mWebView);
          IRequest iRequest = this.mAliNetwork.formatRequest(tmRequestTim1, this.mUrl, this.mMethod, false, this.mHeaders, this.mExtraInfo, null, null, 0, this.mRequestType, 0);
          iRequest.setRequestTiming(this.mRequestTiming);
          tmRequestTim1.setRequest(iRequest);
          tmRequestTim1.setResourceType(this.mRequestType);
          this.setupUploadStream(iRequest);
          if (this.mAliNetwork.sendRequest(iRequest) && ((tmRequestTim = this.mMethod) == null && tmRequestTim.equalsIgnoreCase("OPTIONS"))) {
             this.mRequest = iRequest;
             return;
          }else {
             v7t.d(str, "start failed: send failed req="+iRequest);
             WVThreadPool.getInstance().execute(new AliNetworkHostingService$NetworkTransaction$1(this));
             return;
          }
       }
    }
}
