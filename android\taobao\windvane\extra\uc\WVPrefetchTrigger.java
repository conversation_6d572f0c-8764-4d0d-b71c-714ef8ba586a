package android.taobao.windvane.extra.uc.WVPrefetchTrigger;
import tb.t2o;
import java.util.concurrent.ConcurrentHashMap;
import java.lang.Object;
import java.lang.String;
import java.util.Map;
import anetwork.channel.Request;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Boolean;
import java.lang.Long;
import java.lang.Integer;
import anetwork.channel.entity.RequestImpl;
import android.taobao.windvane.extra.uc.UCNetworkDelegate;
import java.util.Set;
import java.util.Iterator;
import java.util.Map$Entry;
import java.lang.StringBuilder;
import tb.v7t;
import java.lang.Throwable;
import tb.zpw;
import tb.vpw;
import tb.wpw;
import java.lang.CharSequence;
import android.text.TextUtils;
import tb.yaa;
import android.taobao.windvane.extra.uc.WVPrefetchNetworkAdapter;
import tb.gtw;
import android.content.Context;
import java.lang.System;
import java.util.HashMap;
import com.taobao.android.ab.api.ABGlobal;

public class WVPrefetchTrigger	// class@000234 from classes.dex
{
    public static IpChange $ipChange;
    private static final String TAG;
    public static int connectTimeout;
    private static final Map mPrefetchNetwork;
    private static WVPrefetchTrigger mWVPrefetchTrigger;
    public static int readTimeout;
    public static int retryTimes;

    static {
       t2o.a(0x3d800169);
       WVPrefetchTrigger.mPrefetchNetwork = new ConcurrentHashMap();
       WVPrefetchTrigger.retryTimes = 1;
       WVPrefetchTrigger.connectTimeout = 0x2710;
       WVPrefetchTrigger.readTimeout = 0x2710;
    }
    private void WVPrefetchTrigger(){
       super();
    }
    private Request formatAliRequest(String p0,String p1,boolean p2,Map p3,Map p4,Map p5,Map p6,long p7,int p8,int p9,boolean p10,String p11){
       object oobject = p0;
       object oobject1 = p1;
       object oobject2 = p3;
       object oobject3 = p11;
       int i = 0;
       String str = "WVPrefetchTrigger";
       IpChange $ipChange = WVPrefetchTrigger.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[13];
          objArray[i] = this;
          objArray[1] = oobject;
          objArray[2] = oobject1;
          objArray[3] = new Boolean(p2);
          objArray[4] = oobject2;
          objArray[5] = p4;
          objArray[6] = p5;
          objArray[7] = p6;
          objArray[8] = new Long(p7);
          objArray[9] = new Integer(p8);
          objArray[10] = new Integer(p9);
          objArray[11] = new Boolean(p10);
          objArray[12] = oobject3;
          return $ipChange.ipc$dispatch("5a4b0f59", objArray);
       }else {
          try{
             RequestImpl requestImpl = new RequestImpl(p0);
             requestImpl.setFollowRedirects(i);
             requestImpl.setBizId(oobject3);
             requestImpl.setRetryTime(WVPrefetchTrigger.retryTimes);
             requestImpl.setConnectTimeout(WVPrefetchTrigger.connectTimeout);
             requestImpl.setReadTimeout(WVPrefetchTrigger.readTimeout);
             requestImpl.setMethod(p1);
             if (oobject2 != null) {
                requestImpl.addHeader("f-refer", "wv_h5");
                UCNetworkDelegate.getInstance().onSendRequest(oobject2, p0);
                Iterator iterator = p3.entrySet().iterator();
                while (iterator.hasNext()) {
                   Map$Entry uEntry = iterator.next();
                   String key = uEntry.getKey();
                   String value = uEntry.getValue();
                   requestImpl.addHeader(key, value);
                   StringBuilder str1 = "AliRequestAdapter from uc header key=".append(key);
                   v7t.a(str, str1.append(",value=").append(value).toString());
                }
             }
             return requestImpl;
          }catch(java.lang.Exception e0){
             v7t.d(str, " AliRequestAdapter formatAliRequest Exception"+e0.getMessage());
             return null;
          }
       }
    }
    public static WVPrefetchTrigger getInstance(){
       IpChange $ipChange = WVPrefetchTrigger.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          return $ipChange.ipc$dispatch("da8701af", objArray);
       }else if(WVPrefetchTrigger.mWVPrefetchTrigger == null){
          WVPrefetchTrigger wVPrefetchTr = WVPrefetchTrigger.class;
          _monitor_enter(wVPrefetchTr);
          if (WVPrefetchTrigger.mWVPrefetchTrigger == null) {
             WVPrefetchTrigger.mWVPrefetchTrigger = new WVPrefetchTrigger();
          }
          _monitor_exit(wVPrefetchTr);
       }
       return WVPrefetchTrigger.mWVPrefetchTrigger;
    }
    private void mockHeaders(String p0,Map p1){
       IpChange $ipChange = WVPrefetchTrigger.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("c0375fb8", objArray);
          return;
       }else {
          p1.put("Cookie", zpw.a(p0));
          p1.put("Accept", "*/*");
          p1.put("X-Requested-With", "com.taobao.taobao");
          p0 = "";
          vpw.b();
          wpw commonConfig = vpw.commonConfig;
          if (!TextUtils.isEmpty(commonConfig.d0)) {
             vpw.b();
             p0 = p0+commonConfig.d0;
          }
          String str = yaa.f().a();
          String str1 = yaa.f().b();
          if (!TextUtils.isEmpty(str) && !TextUtils.isEmpty(str1)) {
             p0 = p0+" AliApp\("+str+"/"+str1+"\)";
          }
          if (!TextUtils.isEmpty(yaa.f().g())) {
             p0 = p0+" TTID/"+yaa.f().g();
          }
          p1.put("User-Agent", p0+" WindVane/8.5.0");
          p1.put("Accept-Encoding", "gzip, deflate");
          p1.put("Accept-Language", "zh-CN,zh-CN;q=0.9,en-US;q=0.8");
          return;
       }
    }
    public WVPrefetchNetworkAdapter getPrefetchNetworkAdapter(String p0){
       IpChange $ipChange = WVPrefetchTrigger.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVPrefetchTrigger.mPrefetchNetwork.get(gtw.c(p0));
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("e640b03b", objArray);
    }
    public boolean hasPrefetchUrl(String p0){
       IpChange $ipChange = WVPrefetchTrigger.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVPrefetchTrigger.mPrefetchNetwork.containsKey(gtw.c(p0));
       }
       Object[] objArray = new Object[]{this,p0};
       return $ipChange.ipc$dispatch("bb020d13", objArray).booleanValue();
    }
    public void preloadMainHtml(Context p0,String p1){
       IpChange $ipChange = WVPrefetchTrigger.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("3eefdede", objArray);
          return;
       }else {
          vpw.b();
          this.preloadMainHtml(p0, p1, (long)vpw.commonConfig.b0);
          return;
       }
    }
    public void preloadMainHtml(Context p0,String p1,long p2){
       object oobject = this;
       object oobject1 = p0;
       object oobject2 = p1;
       int i = 2;
       int i1 = 0;
       IpChange $ipChange = WVPrefetchTrigger.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,oobject1,oobject2,new Long(p2)};
          $ipChange.ipc$dispatch("9f0c73a6", objArray);
          return;
       }else {
          long l = System.currentTimeMillis();
          String str = "WVPrefetchTrigger";
          v7t.a(str, "preloadMainHtml url="+oobject2);
          HashMap hashMap = new HashMap();
          oobject.mockHeaders(oobject2, hashMap);
          int i2 = (ABGlobal.isFeatureOpened(oobject1, "WVPrefetchNetType"))? 2: 0;
          WVPrefetchNetworkAdapter wVPrefetchNe = v8;
          wVPrefetchNe = new WVPrefetchNetworkAdapter(p0, i2, p2, this.formatAliRequest(p1, "GET", false, hashMap, null, null, null, 0, 4, 0, 1, "windvane"));
          WVPrefetchNetworkAdapter wVPrefetchNe1 = wVPrefetchNe;
          WVPrefetchTrigger.mPrefetchNetwork.put(gtw.c(p1), wVPrefetchNe1);
          v7t.a(str, "use time ="+(System.currentTimeMillis() - l));
          wVPrefetchNe1.sendRequest();
          return;
       }
    }
}
