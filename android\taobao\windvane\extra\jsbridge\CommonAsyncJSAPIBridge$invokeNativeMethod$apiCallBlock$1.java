package android.taobao.windvane.extra.jsbridge.CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import android.taobao.windvane.extra.jsbridge.CommonAsyncJSAPIBridge;
import java.lang.String;
import com.uc.webview.export.extension.JSInterface$JSRoute;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.xhv;
import com.android.alibaba.ip.runtime.IpChange;
import android.taobao.windvane.extra.uc.WVUCWebView;
import tb.kpw;
import android.taobao.windvane.extra.jsbridge.CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1;
import android.taobao.windvane.webview.IWVWebView;
import android.taobao.windvane.jsbridge.WVCallBackContext;

public final class CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 extends Lambda implements d1a	// class@000194 from classes.dex
{
    public final String $callToken;
    public final String $currentUrl;
    public final JSInterface$JSRoute $jsRoute;
    public final String $methodName;
    public final String $methodParam;
    public final String $nameSpace;
    public final CommonAsyncJSAPIBridge this$0;
    public static IpChange $ipChange;

    public void CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1(CommonAsyncJSAPIBridge p0,String p1,String p2,String p3,String p4,JSInterface$JSRoute p5,String p6){
       this.this$0 = p0;
       this.$nameSpace = p1;
       this.$methodName = p2;
       this.$methodParam = p3;
       this.$callToken = p4;
       this.$jsRoute = p5;
       this.$currentUrl = p6;
       super(0);
    }
    public static Object ipc$super(CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1 p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/jsbridge/CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1");
    }
    public Object invoke(){
       this.invoke();
       return xhv.INSTANCE;
    }
    public final void invoke(){
       IpChange $ipChange = CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this};
          $ipChange.ipc$dispatch("7560ccff", objArray);
          return;
       }else {
          Object jsObject = this.this$0.getWebView().getJsObject(this.$nameSpace);
          if (!jsObject instanceof kpw) {
             return;
          }
          jsObject.executeSafe(this.$methodName, this.$methodParam, new CommonAsyncJSAPIBridge$invokeNativeMethod$apiCallBlock$1$1(this, this.this$0.getWebView()));
          return;
       }
    }
}
