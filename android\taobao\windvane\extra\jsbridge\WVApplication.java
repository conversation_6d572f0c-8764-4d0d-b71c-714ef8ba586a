package android.taobao.windvane.extra.jsbridge.WVApplication;
import tb.kpw;
import tb.t2o;
import android.taobao.windvane.jsbridge.WVCallBackContext;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.Object;
import tb.nsw;
import android.os.Build$VERSION;
import android.content.Context;
import tb.itw;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import org.json.JSONObject;
import tb.vpw;
import tb.wpw;
import android.content.Intent;
import android.content.pm.ApplicationInfo;
import android.net.Uri;
import java.lang.Boolean;

public class WVApplication extends kpw	// class@0001a3 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d8000d8);
    }
    public void WVApplication(){
       super();
    }
    private void getNotificationSettings(WVCallBackContext p0,String p1){
       IpChange $ipChange = WVApplication.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("8acc54b2", objArray);
          return;
       }else {
          nsw onsw = new nsw();
          if (Build$VERSION.SDK_INT < 22) {
             onsw.b("status", "unknown");
             p0.success(onsw);
          }else if(!itw.d(this.mContext)){
             onsw.b("status", "denied");
             p0.success(onsw);
          }else {
             onsw.b("status", "authorized");
             p0.success(onsw);
          }
          return;
       }
    }
    public static Object ipc$super(WVApplication p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/jsbridge/WVApplication");
    }
    private void openSettings(WVCallBackContext p0,String p1){
       Intent intent;
       kpw tmContext;
       nsw onsw;
       kpw tmContext1;
       IpChange $ipChange = WVApplication.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("d10e0f5b", objArray);
          return;
       }else {
          String str = null;
          try{
             p1 = new JSONObject(p1).optString("type", "");
          }catch(org.json.JSONException e0){
             p0.error(new nsw("HY_PARAM_ERR"));
             p1 = e0;
          }
          String str1 = "msg";
          if ("Notification".equals(p1)) {
             vpw.b();
             str = "android.settings.APP_NOTIFICATION_SETTINGS";
             if (vpw.commonConfig.v0 != null && Build$VERSION.SDK_INT >= 26) {
                intent = new Intent();
                intent.setAction(str);
                intent.putExtra("android.provider.extra.APP_PACKAGE", this.mContext.getPackageName());
                if ((tmContext = this.mContext) != null) {
                   tmContext.startActivity(intent);
                   p0.success();
                   return;
                }
             }else {
                intent = new Intent();
                intent.setAction(str);
                intent.putExtra("app_package", this.mContext.getPackageName());
                intent.putExtra("app_uid", this.mContext.getApplicationInfo().uid);
                if ((tmContext = this.mContext) != null) {
                   tmContext.startActivity(intent);
                   p0.success();
                   return;
                }
             }
             onsw = new nsw();
             onsw.b(str1, "fail to open Notification Settings");
             p0.error(onsw);
          }else {
             intent = new Intent("android.settings.APPLICATION_DETAILS_SETTINGS");
             if ((tmContext1 = this.mContext) != null) {
                intent.setData(Uri.fromParts("package", tmContext1.getPackageName(), e0));
                this.mContext.startActivity(intent);
                p0.success();
                return;
             }else {
                onsw = new nsw();
                onsw.b(str1, "fail to open Application Settings");
                p0.error(onsw);
             }
          }
          return;
       }
    }
    public boolean execute(String p0,String p1,WVCallBackContext p2){
       IpChange $ipChange = WVApplication.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          return $ipChange.ipc$dispatch("bcd41fd1", objArray).booleanValue();
       }else if("getNotificationSettings".equals(p0)){
          this.getNotificationSettings(p2, p1);
       }else if("openSettings".equals(p0)){
          this.openSettings(p2, p1);
       }else {
          return 0;
       }
       return 1;
    }
}
