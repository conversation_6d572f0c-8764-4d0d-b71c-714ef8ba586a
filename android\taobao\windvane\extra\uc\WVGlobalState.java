package android.taobao.windvane.extra.uc.WVGlobalState;
import tb.t2o;
import java.lang.Object;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;

public class WVGlobalState	// class@00022e from classes.dex
{
    public static IpChange $ipChange;
    private static boolean sUrlHasBeenLoaded;

    static {
       t2o.a(0x3d800163);
       WVGlobalState.sUrlHasBeenLoaded = false;
    }
    private void WVGlobalState(){
       super();
    }
    public static void markLoadUrl(){
       IpChange $ipChange = WVGlobalState.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          $ipChange.ipc$dispatch("7fb0eda3", objArray);
          return;
       }else {
          WVGlobalState.sUrlHasBeenLoaded = true;
          return;
       }
    }
    public static boolean urlHasBeenLoaded(){
       IpChange $ipChange = WVGlobalState.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return WVGlobalState.sUrlHasBeenLoaded;
       }
       Object[] objArray = new Object[0];
       return $ipChange.ipc$dispatch("c5e3c2e7", objArray).booleanValue();
    }
}
