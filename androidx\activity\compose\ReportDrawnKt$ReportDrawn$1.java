package androidx.activity.compose.ReportDrawnKt$ReportDrawn$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import java.lang.Boolean;
import java.lang.Object;

public final class ReportDrawnKt$ReportDrawn$1 extends Lambda implements d1a	// class@000497 from classes.dex
{
    public static final ReportDrawnKt$ReportDrawn$1 INSTANCE;

    static {
       ReportDrawnKt$ReportDrawn$1.INSTANCE = new ReportDrawnKt$ReportDrawn$1();
    }
    public void ReportDrawnKt$ReportDrawn$1(){
       super(0);
    }
    public final Boolean invoke(){
       return Boolean.TRUE;
    }
    public Object invoke(){
       return this.invoke();
    }
}
