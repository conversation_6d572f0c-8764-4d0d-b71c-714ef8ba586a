package androidx.activity.compose.ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1;
import tb.g1a;
import kotlin.jvm.internal.Lambda;
import androidx.activity.compose.ActivityResultLauncherHolder;
import androidx.activity.result.ActivityResultRegistry;
import java.lang.String;
import androidx.activity.result.contract.ActivityResultContract;
import tb.xf40;
import java.lang.Object;
import tb.hi20;
import tb.gi20;
import tb.mb7;
import androidx.activity.result.ActivityResultCallback;
import androidx.activity.result.ActivityResultLauncher;
import androidx.activity.compose.ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1$invoke$$inlined$onDispose$1;

public final class ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1 extends Lambda implements g1a	// class@000479 from classes.dex
{
    public final ActivityResultRegistry $activityResultRegistry;
    public final ActivityResultContract $contract;
    public final xf40 $currentOnResult;
    public final String $key;
    public final ActivityResultLauncherHolder $realLauncher;

    public void ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1(ActivityResultLauncherHolder p0,ActivityResultRegistry p1,String p2,ActivityResultContract p3,xf40 p4){
       this.$realLauncher = p0;
       this.$activityResultRegistry = p1;
       this.$key = p2;
       this.$contract = p3;
       this.$currentOnResult = p4;
       super(1);
    }
    public static void a(xf40 p0,Object p1){
       ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1.invoke$lambda$0(p0, p1);
    }
    private static final void invoke$lambda$0(xf40 p0,Object p1){
       p0.getValue().invoke(p1);
    }
    public Object invoke(Object p0){
       return this.invoke(p0);
    }
    public final gi20 invoke(hi20 p0){
       this.$realLauncher.setLauncher(this.$activityResultRegistry.register(this.$key, this.$contract, new mb7(this.$currentOnResult)));
       return new ActivityResultRegistryKt$rememberLauncherForActivityResult$1$1$invoke$$inlined$onDispose$1(this.$realLauncher);
    }
}
