package androidx.activity.compose.PredictiveBackHandlerKt;
import tb.u1a;
import androidx.compose.runtime.a;
import androidx.compose.runtime.ComposerImpl;
import java.lang.Object;
import tb.xf40;
import tb.me40;
import java.lang.Class;
import androidx.compose.runtime.a$a;
import kotlin.coroutines.EmptyCoroutineContext;
import kotlin.coroutines.d;
import tb.uu4;
import androidx.compose.runtime.EffectsKt;
import tb.xa20;
import androidx.activity.compose.PredictiveBackHandlerCallback;
import tb.xhv;
import java.lang.Boolean;
import androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$2$1;
import tb.ar4;
import androidx.activity.compose.LocalOnBackPressedDispatcherOwner;
import androidx.activity.OnBackPressedDispatcherOwner;
import androidx.activity.OnBackPressedDispatcher;
import tb.v140;
import androidx.compose.ui.platform.AndroidCompositionLocals_androidKt;
import tb.oa20;
import androidx.lifecycle.LifecycleOwner;
import androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$3$1;
import tb.g1a;
import tb.b940;
import androidx.activity.compose.PredictiveBackHandlerKt$PredictiveBackHandler$4;
import androidx.compose.runtime.RecomposeScopeImpl;
import java.lang.IllegalStateException;
import java.lang.String;

public final class PredictiveBackHandlerKt	// class@000492 from classes.dex
{

    public static final void PredictiveBackHandler(boolean p0,u1a p1,a p2,int p3,int p4){
       int i;
       int i1;
       a uoa;
       Object a;
       PredictiveBackHandlerCallback predictiveBa;
       OnBackPressedDispatcherOwner current;
       b940 uob940;
       p2 = p2.B(-642000585);
       if (i = p4 & 0x01) {
          i1 = p3 | 0x06;
       }else if(!((p3 & 0x06))){
          i1 = (p2.a(p0))? 4: 2;
          i1 = i1 | p3;
       }else {
          i1 = p3;
       }
       if ((p4 & 0x02)) {
          i1 = i1 | 0x30;
       }else if(!((p3 & 0x30))){
          int i2 = (p2.y(p1))? 32: 16;
          i1 = i1 | i2;
       }
       if (((i1 & 0x13)) == 18) {
          uoa = p2;
          if (uoa.a()) {
             uoa.e();
          label_014e :
             if ((uob940 = p2.p0()) != null) {
                uob940.I(new PredictiveBackHandlerKt$PredictiveBackHandler$4(p0, p1, p3, p4));
             }
             return;
          }
       }
       if (i) {
          p0 = true;
       }
       xf40 oxf40 = me40.o(p1, p2, ((i1 >> 3) & 0x0e));
       uoa = p2;
       uoa.F(-723524056);
       uoa.F(-3687241);
       xa20 oxa20 = uoa.o();
       a.Companion.getClass();
       a = a$a.a;
       if (oxa20 == a) {
          xa20 oxa201 = new xa20(EffectsKt.k(EmptyCoroutineContext.INSTANCE, p2));
          uoa.E(oxa201);
          oxa20 = oxa201;
       }
       uoa.K();
       uu4 ouu4 = oxa20.a();
       uoa.K();
       uoa.F(-1071578902);
       if ((predictiveBa = uoa.o()) == a) {
          predictiveBa = new PredictiveBackHandlerCallback(p0, ouu4, PredictiveBackHandlerKt.PredictiveBackHandler$lambda$0(oxf40));
          uoa.E(predictiveBa);
       }
       uoa.K();
       uoa.F(-1071578713);
       Object obj = uoa.o();
       if (((uoa.y(PredictiveBackHandlerKt.PredictiveBackHandler$lambda$0(oxf40)) | uoa.y(ouu4))) || obj == a) {
          predictiveBa.setCurrentOnBack(PredictiveBackHandlerKt.PredictiveBackHandler$lambda$0(oxf40));
          predictiveBa.setOnBackScope(ouu4);
          uoa.E(xhv.INSTANCE);
       }
       uoa.K();
       Boolean uBoolean = Boolean.valueOf(p0);
       uoa.F(-1071578541);
       PredictiveBackHandlerKt$PredictiveBackHandler$2$1 predictiveBa1 = uoa.o();
       if (((uoa.y(predictiveBa) | uoa.a(p0))) || predictiveBa1 == a) {
          predictiveBa1 = new PredictiveBackHandlerKt$PredictiveBackHandler$2$1(predictiveBa, p0, null);
          uoa.E(predictiveBa1);
       }
       uoa.K();
       EffectsKt.f(uBoolean, predictiveBa1, p2, (i1 & 0x0e));
       if ((current = LocalOnBackPressedDispatcherOwner.INSTANCE.getCurrent(p2, 6)) != null) {
          OnBackPressedDispatcher onBackPresse = current.getOnBackPressedDispatcher();
          LifecycleOwner lifecycleOwn = uoa.d(AndroidCompositionLocals_androidKt.getLocalLifecycleOwner());
          uoa.F(-1071578150);
          PredictiveBackHandlerKt$PredictiveBackHandler$3$1 predictiveBa2 = uoa.o();
          if ((((uoa.y(onBackPresse) | uoa.y(lifecycleOwn)) | uoa.y(predictiveBa))) || predictiveBa2 == a) {
             predictiveBa2 = new PredictiveBackHandlerKt$PredictiveBackHandler$3$1(onBackPresse, lifecycleOwn, predictiveBa);
             uoa.E(predictiveBa2);
          }
          uoa.K();
          EffectsKt.b(lifecycleOwn, onBackPresse, predictiveBa2, p2, 0);
          goto label_014e ;
       }else {
          throw new IllegalStateException("No OnBackPressedDispatcherOwner was provided via LocalOnBackPressedDispatcherOwner");
       }
    }
    private static final u1a PredictiveBackHandler$lambda$0(xf40 p0){
       return p0.getValue();
    }
}
