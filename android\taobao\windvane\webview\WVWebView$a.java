package android.taobao.windvane.webview.WVWebView$a;
import android.view.View$OnLongClickListener;
import android.taobao.windvane.webview.WVWebView;
import java.lang.Object;
import android.view.View;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;
import java.lang.Boolean;
import android.webkit.WebView$HitTestResult;
import android.webkit.WebView;
import tb.v7t;
import java.lang.StringBuilder;
import android.taobao.windvane.view.PopupWindowController;
import android.view.View$OnClickListener;
import android.content.Context;

public class WVWebView$a implements View$OnLongClickListener	// class@000310 from classes.dex
{
    public final WVWebView a;
    public static IpChange $ipChange;

    public void WVWebView$a(WVWebView p0){
       super();
       this.a = p0;
    }
    public boolean onLongClick(View p0){
       WebView$HitTestResult hitTestResul;
       int i = 0;
       IpChange $ipChange = WVWebView$a.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("7edba102", objArray).booleanValue();
       }else {
          try{
             hitTestResul = this.a.getHitTestResult();
          }catch(java.lang.Exception e0){
             hitTestResul = null;
          }
          if (hitTestResul == null) {
             return i;
          }
          if (!WVWebView.access$000(this.a)) {
             return i;
          }
          if (v7t.h()) {
             v7t.a("WVWebView", "Long click on WebView, "+hitTestResul.getExtra());
          }
          if (hitTestResul.getType() != 8 && hitTestResul.getType() != 5) {
             return i;
          }else {
             WVWebView.access$102(this.a, hitTestResul.getExtra());
             WVWebView$a ta = this.a;
             WVWebView.access$202(this.a, new PopupWindowController(ta.context, ta, WVWebView.access$300(ta), WVWebView.access$400(this.a)));
             WVWebView.access$200(this.a).i();
             return e0;
          }
       }
    }
}
