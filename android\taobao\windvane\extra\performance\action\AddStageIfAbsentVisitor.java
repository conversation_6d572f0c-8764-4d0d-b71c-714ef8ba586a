package android.taobao.windvane.extra.performance.action.AddStageIfAbsentVisitor;
import android.taobao.windvane.extra.performance.action.AddStageVisitor;
import tb.t2o;
import java.lang.String;
import java.lang.Object;
import com.android.alibaba.ip.runtime.InstantReloadException;
import java.lang.StringBuilder;
import tb.cce;
import com.android.alibaba.ip.runtime.IpChange;

public class AddStageIfAbsentVisitor extends AddStageVisitor	// class@0001d3 from classes.dex
{
    public static IpChange $ipChange;

    static {
       t2o.a(0x3d800108);
    }
    public void AddStageIfAbsentVisitor(String p0,long p1){
       super(p0, p1);
    }
    public static Object ipc$super(AddStageIfAbsentVisitor p0,String p1,Object[] p2){
       p1.hashCode();
       throw new InstantReloadException("String switch could not find \'"+p1+"\' with hashcode "+p1.hashCode()+" in android/taobao/windvane/extra/performance/action/AddStageIfAbsentVisitor");
    }
    public void accept(cce p0){
       IpChange $ipChange = AddStageIfAbsentVisitor.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("987066e1", objArray);
          return;
       }else {
          p0.onStageIfAbsent(this.name, this.upTime);
          return;
       }
    }
}
