package android.taobao.windvane.extra.uc.UCNetworkDelegate;
import android.os.Handler$Callback;
import tb.t2o;
import java.lang.Object;
import java.util.concurrent.ConcurrentHashMap;
import android.taobao.windvane.extra.uc.WVThread;
import java.lang.String;
import android.os.Handler;
import java.util.Hashtable;
import java.lang.ref.WeakReference;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.ref.Reference;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.CharSequence;
import android.text.TextUtils;
import java.lang.Long;
import tb.gtw;
import java.util.Enumeration;
import java.util.ArrayList;
import java.lang.StringBuilder;
import tb.v7t;
import android.os.Message;
import java.lang.Boolean;
import java.lang.Integer;
import java.lang.System;
import java.util.Map;

public class UCNetworkDelegate implements Handler$Callback	// class@000222 from classes.dex
{
    private Handler mHandler;
    private ConcurrentHashMap mWebViewsInfoMap;
    public static IpChange $ipChange;
    public static final int CHANGE_WEBVIEW_URL;
    private static final int RECEIVE_RESPONSE_CODE;
    private static final int REMOVE_WEBVIEW_CODE;
    private static final int SEND_REQUEST_CODE;
    private static final String TAG;
    private static UCNetworkDelegate instance;

    static {
       t2o.a(0x3d800157);
       UCNetworkDelegate.instance = new UCNetworkDelegate();
    }
    private void UCNetworkDelegate(){
       super();
       this.mWebViewsInfoMap = new ConcurrentHashMap();
       this.mHandler = new WVThread("Windvane", this).getHandler();
    }
    private void assembleRequestData(Hashtable p0,String p1,String p2,WeakReference p3){
       WVUCWebView wVUCWebView;
       String str;
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2,p3};
          $ipChange.ipc$dispatch("6c810a37", objArray);
          return;
       }else if((wVUCWebView = p3.get()) == null){
          return;
       }else if(TextUtils.isEmpty(p1)){
          str = "";
       }else {
          str = p1;
       }
       wVUCWebView.insertH5MonitorData(p1, "url", str);
       if (TextUtils.isEmpty(p2)) {
          p2 = "";
       }
       wVUCWebView.insertH5MonitorData(p1, "referrer", p2);
       wVUCWebView.insertH5MonitorData(p1, "start", String.valueOf((Long.parseLong(p0.get("start")) - wVUCWebView.mPageStart)));
       return;
    }
    private void assembleResponseData(Hashtable p0,String p1,WeakReference p2){
       WVUCWebView wVUCWebView;
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1,p2};
          $ipChange.ipc$dispatch("317cc157", objArray);
          return;
       }else if((wVUCWebView = p2.get()) == null){
          return;
       }else {
          wVUCWebView.insertH5MonitorData(p1, "statusCode", p0.get("statusCode"));
          wVUCWebView.insertH5MonitorData(p1, "end", String.valueOf((Long.parseLong(p0.get("end")) - wVUCWebView.mPageStart)));
          return;
       }
    }
    private void dealReceiveResponse(Object p0){
       WeakReference weakReferenc;
       String str1;
       WVUCWebView wVUCWebView;
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("980334db", objArray);
          return;
       }else if(p0 instanceof Hashtable){
          String str = gtw.h(gtw.j(p0.get("url")));
          Enumeration uEnumeration = this.mWebViewsInfoMap.keys();
          while (true) {
             if (uEnumeration.hasMoreElements()) {
                weakReferenc = uEnumeration.nextElement();
                if ((str1 = this.mWebViewsInfoMap.get(weakReferenc)) != null && str1.contains(str)) {
                   this.assembleResponseData(p0, str, weakReferenc);
                   return;
                }
             }else {
                uEnumeration = this.mWebViewsInfoMap.keys();
                while (uEnumeration.hasMoreElements()) {
                   weakReferenc = uEnumeration.nextElement();
                   if ((wVUCWebView = weakReferenc.get()) == null || !wVUCWebView.containsH5MonitorData(str)) {
                   }else {
                      this.assembleResponseData(p0, str, weakReferenc);
                      break ;
                   }
                }
             }
          }
       }
       return;
    }
    private void dealRemoveWebView(Object p0){
       UCNetworkDelegate tmWebViewsIn;
       WVUCWebView wVUCWebView;
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("46e1f1cc", objArray);
          return;
       }else if(p0 instanceof WVUCWebView && (tmWebViewsIn = this.mWebViewsInfoMap) != null){
          Enumeration uEnumeration = tmWebViewsIn.keys();
          while (uEnumeration.hasMoreElements()) {
             WeakReference weakReferenc = uEnumeration.nextElement();
             if ((wVUCWebView = weakReferenc.get()) != null && p0.equals(wVUCWebView)) {
                this.mWebViewsInfoMap.remove(weakReferenc);
                break ;
             }
          }
       }
       return;
    }
    private void dealSendRequest(Object p0){
       Enumeration uEnumeration;
       WeakReference weakReferenc;
       WVUCWebView wVUCWebView;
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("fa3afc3e", objArray);
          return;
       }else if(p0 instanceof Hashtable){
          String str = gtw.h(gtw.j(p0.get("url")));
          String str1 = gtw.j(p0.get("referrer"));
          if (!TextUtils.isEmpty(str1)) {
             if (this.mWebViewsInfoMap.containsValue(str1)) {
                uEnumeration = this.mWebViewsInfoMap.keys();
                while (uEnumeration.hasMoreElements()) {
                   weakReferenc = uEnumeration.nextElement();
                   if (weakReferenc.get() != null && this.mWebViewsInfoMap.get(weakReferenc).equals(str1)) {
                      this.assembleRequestData(p0, str, str1, weakReferenc);
                      break ;
                   }
                }
             }else {
                uEnumeration = this.mWebViewsInfoMap.keys();
                while (uEnumeration.hasMoreElements()) {
                   weakReferenc = uEnumeration.nextElement();
                   if (weakReferenc.get() != null && weakReferenc.get().containsH5MonitorData(str1)) {
                      this.assembleRequestData(p0, str, str1, weakReferenc);
                      break ;
                   }
                }
             }
          }else if(this.mWebViewsInfoMap.containsValue(str)){
             uEnumeration = this.mWebViewsInfoMap.keys();
             while (uEnumeration.hasMoreElements()) {
                weakReferenc = uEnumeration.nextElement();
                if ((wVUCWebView = weakReferenc.get()) != null && this.mWebViewsInfoMap.get(weakReferenc).equals(str)) {
                   this.assembleRequestData(p0, str, str1, weakReferenc);
                   break ;
                }
             }
          }
       }
       return;
    }
    private void dealUrlChange(Object p0){
       WVUCWebView wVUCWebView1;
       int i = 1;
       int i1 = 0;
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("1c790b56", objArray);
          return;
       }else if(p0 instanceof ArrayList && (p0.get(i1) instanceof WVUCWebView && p0.get(i) instanceof String)){
          WVUCWebView wVUCWebView = p0.get(i1);
          p0 = gtw.h(gtw.j(p0.get(i)));
          Enumeration uEnumeration = this.mWebViewsInfoMap.keys();
          while (true) {
             if (uEnumeration.hasMoreElements()) {
                WeakReference weakReferenc = uEnumeration.nextElement();
                if ((wVUCWebView1 = weakReferenc.get()) == null || !wVUCWebView.equals(wVUCWebView1)) {
                   continue ;
                }else {
                   wVUCWebView1.clearH5MonitorData();
                   this.mWebViewsInfoMap.put(weakReferenc, p0);
                   return;
                }
             }else {
                this.mWebViewsInfoMap.put(new WeakReference(wVUCWebView), p0);
                break ;
             }
          }
       }
       return;
    }
    public static synchronized UCNetworkDelegate getInstance(){
       _monitor_enter(UCNetworkDelegate.class);
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[0];
          _monitor_exit(UCNetworkDelegate.class);
          return $ipChange.ipc$dispatch("a1bd8f8c", objArray);
       }else {
          _monitor_exit(UCNetworkDelegate.class);
          return UCNetworkDelegate.instance;
       }
    }
    public String getBizCodeByUrl(String p0){
       WVUCWebView wVUCWebView;
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("4c8e1c21", objArray);
       }else if(TextUtils.isEmpty(p0)){
          return "";
       }else {
          Enumeration uEnumeration = this.mWebViewsInfoMap.keys();
          while (true) {
             if (!uEnumeration.hasMoreElements()) {
                return "";
             }
             WeakReference weakReferenc = uEnumeration.nextElement();
             if ((wVUCWebView = weakReferenc.get()) != null && p0.equals(gtw.i(this.mWebViewsInfoMap.get(weakReferenc)))) {
                break ;
             }
          }
          v7t.i("UCNetworkDelegate", "Get bizCode : "+wVUCWebView.bizCode);
          return wVUCWebView.bizCode;
       }
    }
    public ConcurrentHashMap getWebViews(){
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if (!$ipChange instanceof IpChange) {
          return this.mWebViewsInfoMap;
       }
       Object[] objArray = new Object[]{this};
       return $ipChange.ipc$dispatch("706b2a8f", objArray);
    }
    public boolean handleMessage(Message p0){
       int i = 0;
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          return $ipChange.ipc$dispatch("282a8c1d", objArray).booleanValue();
       }else {
          switch (p0.what){
              case 273:
                this.dealSendRequest(p0.obj);
                break;
              case 274:
                this.dealReceiveResponse(p0.obj);
                break;
              case 275:
                this.dealRemoveWebView(p0.obj);
              case 276:
                this.dealUrlChange(p0.obj);
                break;
              default:
          }
          return i;
       }
    }
    public void onFinish(int p0,String p1){
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,new Integer(p0),p1};
          $ipChange.ipc$dispatch("365c0a94", objArray);
          return;
       }else if(this.mWebViewsInfoMap != null && p1 != null){
          Message message = this.mHandler.obtainMessage();
          message.what = 274;
          Hashtable hashtable = new Hashtable();
          hashtable.put("url", p1);
          String str = String.valueOf(p0);
          hashtable.put("statusCode", str);
          hashtable.put("end", String.valueOf(System.currentTimeMillis()));
          message.obj = hashtable;
          v7t.a("UCNetworkDelegate", "onFinish : "+p1+" statusCode: "+str);
          this.mHandler.sendMessage(message);
       }
       return;
    }
    public void onSendRequest(Map p0,String p1){
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("ea3cb0e0", objArray);
          return;
       }else if(this.mWebViewsInfoMap != null && (p0 != null && p1 != null)){
          Message message = this.mHandler.obtainMessage();
          message.what = 273;
          Hashtable hashtable = new Hashtable();
          hashtable.put("url", p1);
          String str = p0.get("Referer");
          String str1 = (TextUtils.isEmpty(str))? "": str;
          hashtable.put("referrer", str1);
          hashtable.put("start", String.valueOf(System.currentTimeMillis()));
          message.obj = hashtable;
          v7t.a("UCNetworkDelegate", "onSendRequest : "+p1+" Referer: "+str);
          this.mHandler.sendMessage(message);
       }
       return;
    }
    public void onUrlChange(WVUCWebView p0,String p1){
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0,p1};
          $ipChange.ipc$dispatch("75ddf152", objArray);
          return;
       }else {
          Message message = this.mHandler.obtainMessage();
          message.what = 276;
          ArrayList uArrayList = new ArrayList();
          uArrayList.add(p0);
          uArrayList.add(p1);
          message.obj = uArrayList;
          this.mHandler.sendMessage(message);
          return;
       }
    }
    public void removeWebview(WVUCWebView p0){
       IpChange $ipChange = UCNetworkDelegate.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("d29cde3d", objArray);
          return;
       }else {
          Message message = this.mHandler.obtainMessage();
          message.what = 275;
          message.obj = p0;
          this.mHandler.sendMessage(message);
          return;
       }
    }
}
