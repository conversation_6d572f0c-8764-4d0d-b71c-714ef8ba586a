package android.taobao.windvane.extra.uc.WVUCWebChromeClient$7;
import android.content.DialogInterface$OnCancelListener;
import android.taobao.windvane.extra.uc.WVUCWebChromeClient;
import com.uc.webview.export.JsResult;
import java.lang.Object;
import android.content.DialogInterface;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.String;

public class WVUCWebChromeClient$7 implements DialogInterface$OnCancelListener	// class@000249 from classes.dex
{
    public final WVUCWebChromeClient this$0;
    public final JsResult val$res;
    public static IpChange $ipChange;

    public void WVUCWebChromeClient$7(WVUCWebChromeClient p0,JsResult p1){
       this.this$0 = p0;
       this.val$res = p1;
       super();
    }
    public void onCancel(DialogInterface p0){
       IpChange $ipChange = WVUCWebChromeClient$7.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("f4ed3926", objArray);
          return;
       }else {
          this.val$res.cancel();
          return;
       }
    }
}
