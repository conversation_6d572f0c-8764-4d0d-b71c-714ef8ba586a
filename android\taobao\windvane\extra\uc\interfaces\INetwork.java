package android.taobao.windvane.extra.uc.interfaces.INetwork;
import android.taobao.windvane.extra.uc.interfaces.EventHandler;
import java.lang.String;
import java.util.Map;
import android.taobao.windvane.extra.uc.interfaces.IRequest;

public interface abstract INetwork	// class@000272 from classes.dex
{
    public static final int ALINETWORK = 1;
    public static final int THIRDNETWORK = 2;
    public static final int UCNETWORK;

    IRequest formatRequest(EventHandler p0,String p1,String p2,boolean p3,Map p4,Map p5,Map p6,Map p7,long p8,int p9,int p10);
    int getNetworkType();
    String getVersion();
    boolean requestURL(EventHandler p0,String p1,String p2,boolean p3,Map p4,Map p5,Map p6,Map p7,long p8,int p9,int p10);
    boolean sendRequest(IRequest p0);
}
