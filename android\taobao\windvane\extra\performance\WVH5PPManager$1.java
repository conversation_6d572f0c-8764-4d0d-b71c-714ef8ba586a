package android.taobao.windvane.extra.performance.WVH5PPManager$1;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.performance.WVH5PPManager;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;
import org.json.JSONObject;
import android.taobao.windvane.extra.performance.WVPagePerformance;
import java.lang.Throwable;
import android.taobao.windvane.webview.IWVWebView;
import android.view.View;
import tb.vpw;
import tb.wpw;

public class WVH5PPManager$1 implements ValueCallback	// class@0001cb from classes.dex
{
    public final WVH5PPManager this$0;
    public static IpChange $ipChange;

    public void WVH5PPManager$1(WVH5PPManager p0){
       this.this$0 = p0;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       int i = 1;
       IpChange $ipChange = WVH5PPManager$1.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{this,p0};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else if(TextUtils.isEmpty(p0)){
          p0 = "{}";
       }
       String str = "\"";
       if (p0.startsWith(str) && p0.endsWith(str)) {
          p0 = p0.substring(i, (p0.length() - i));
       }
       try{
          JSONObject i1 = new JSONObject(p0.replace("\\", ""));
          WVH5PPManager.access$000(this.this$0).setH5_PP_navigationStart(i1.optLong("ns"));
          WVH5PPManager.access$000(this.this$0).setH5_PP_fetchStart(i1.optLong("fs"));
          WVH5PPManager.access$000(this.this$0).setH5_PP_responseEnd(i1.optLong("re"));
          WVH5PPManager.access$000(this.this$0).setH5_PP_domContentLoadedEventStart(i1.optLong("ds"));
          WVH5PPManager.access$000(this.this$0).setH5_PP_loadEventStart(i1.optLong("ls"));
          WVH5PPManager.access$000(this.this$0).setH5_PP_loadEventEnd(i1.optLong("le"));
          WVH5PPManager.access$000(this.this$0).setH5_PP_requestStart(i1.optLong("rs"));
          WVH5PPManager.access$000(this.this$0).setH5_PP_domLoading(i1.optLong("dl"));
          WVH5PPManager.access$000(this.this$0).setH5_PP_domComplete(i1.optLong("dc"));
       }catch(org.json.JSONException e6){
          e6.printStackTrace();
       }
       if (WVH5PPManager.access$100(this.this$0) instanceof View) {
          vpw.b();
          if (vpw.commonConfig.Q0 != null) {
             WVH5PPManager$1 tthis$0 = this.this$0;
             tthis$0.uploadPPInfo(WVH5PPManager.access$100(tthis$0));
          }
       }
       return;
    }
}
