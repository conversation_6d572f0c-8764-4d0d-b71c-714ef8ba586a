package android.taobao.windvane.extra.performance.WVH5PPManager$2;
import android.webkit.ValueCallback;
import android.taobao.windvane.extra.performance.WVH5PPManager;
import android.taobao.windvane.extra.uc.WVUCWebView;
import java.lang.Object;
import java.lang.String;
import com.android.alibaba.ip.runtime.IpChange;
import java.lang.CharSequence;
import android.text.TextUtils;
import com.uc.webview.export.WebView;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSON;
import android.os.SystemClock;
import java.lang.System;
import java.lang.Double;
import tb.avt;
import java.lang.Long;
import android.view.View;
import android.taobao.windvane.extra.uc.interfaces.IRequestTiming;
import java.lang.Boolean;
import anetwork.channel.statist.StatisticData;
import tb.vpw;
import tb.wpw;
import android.taobao.windvane.extra.performance.WVH5PPManager$2$1;
import tb.esd;
import tb.hwj;
import tb.ace;
import tb.cce;
import java.lang.Integer;
import java.lang.Throwable;
import tb.v7t;

public class WVH5PPManager$2 implements ValueCallback	// class@0001cd from classes.dex
{
    public final WVH5PPManager this$0;
    public final WVUCWebView val$webview;
    public static IpChange $ipChange;

    public void WVH5PPManager$2(WVH5PPManager p0,WVUCWebView p1){
       this.this$0 = p0;
       this.val$webview = p1;
       super();
    }
    public void onReceiveValue(Object p0){
       this.onReceiveValue(p0);
    }
    public void onReceiveValue(String p0){
       JSONObject jSONObject;
       JSONObject jSONObject1;
       IRequestTiming requestTimin;
       object oobject = this;
       object oobject1 = p0;
       int i = 1;
       int i1 = 0;
       String str = "trackCost";
       String str1 = "\"";
       IpChange $ipChange = WVH5PPManager$2.$ipChange;
       if ($ipChange instanceof IpChange) {
          Object[] objArray = new Object[]{oobject,oobject1};
          $ipChange.ipc$dispatch("138ac29e", objArray);
          return;
       }else if(!TextUtils.isEmpty(p0) && !oobject.val$webview.isDestroied()){
          if (oobject1.startsWith(str1) && oobject1.endsWith(str1)) {
             oobject1 = oobject1.substring(i, (p0.length() - i));
          }
          if ((jSONObject = JSON.parseObject(oobject1.replace("\\", ""))) == null) {
             return;
          }else {
             double doubleValue = jSONObject.getDoubleValue("timeOrigin");
             if ((jSONObject1 = jSONObject.getJSONObject("longestImage")) != null) {
                String str2 = jSONObject1.getString("name");
                if (TextUtils.isEmpty(str2)) {
                   return;
                }else {
                   int intValue = jSONObject.getIntValue("imageCountBeforeSsrEnd");
                   JSONObject jSONObject2 = new JSONObject();
                   double doubleValue1 = jSONObject1.getDoubleValue("fetchStart");
                   double doubleValue2 = jSONObject1.getDoubleValue("requestStart");
                   double doubleValue3 = jSONObject1.getDoubleValue("responseStart");
                   String str3 = str;
                   double doubleValue4 = jSONObject1.getDoubleValue("responseEnd");
                   int i2 = 0;
                   if ((i2 - doubleValue1) > 0) {
                      doubleValue1 = doubleValue1 + doubleValue;
                   }
                   long l = (long)doubleValue1;
                   if ((i2 - doubleValue2) > 0) {
                      doubleValue2 = doubleValue2 + doubleValue;
                   }
                   long l1 = (long)doubleValue2;
                   if ((i2 - doubleValue3) > 0) {
                      doubleValue3 = doubleValue3 + doubleValue;
                   }
                   long l2 = (long)doubleValue3;
                   long l3 = ((i2 - doubleValue4) > 0)? (long)(doubleValue + doubleValue4): (long)doubleValue4;
                   long l4 = SystemClock.uptimeMillis() - System.currentTimeMillis();
                   jSONObject2.put("H5_imageUrl", str2);
                   int i3 = intValue;
                   jSONObject2.put("H5_duration", Double.valueOf(jSONObject1.getDoubleValue("duration")));
                   jSONObject2.put("H5_fetchStart", Long.valueOf(avt.b(l, l4)));
                   jSONObject2.put("H5_requestStart", Long.valueOf(avt.b(l1, l4)));
                   jSONObject2.put("H5_responseStart", Long.valueOf(avt.b(l2, l4)));
                   jSONObject2.put("H5_responseEnd", Long.valueOf(avt.b(l3, l4)));
                   jSONObject2.put("H5_transferSize", Double.valueOf(jSONObject1.getDoubleValue("transferSize")));
                   String str4 = str3;
                   jSONObject2.put(str4, Double.valueOf(jSONObject1.getDoubleValue(str4)));
                   if ((requestTimin = WVH5PPManager.getRequestTiming(oobject.val$webview, str2)) != null) {
                      jSONObject2.put("native_interceptStart", Long.valueOf(avt.b(requestTimin.getNativeShouldInterceptStart(), l4)));
                      jSONObject2.put("native_interceptEnd", Long.valueOf(avt.b(requestTimin.getNativeShouldInterceptEnd(), l4)));
                      jSONObject2.put("native_intercepted", Boolean.valueOf(requestTimin.getRequestIntercepted()));
                      jSONObject2.put("native_delegateStart", Long.valueOf(avt.b(requestTimin.getNativeDelegateBeforeSendRequestStart(), l4)));
                      jSONObject2.put("native_delegateEnd", Long.valueOf(avt.b(requestTimin.getNativeDelegateBeforeSendRequestEnd(), l4)));
                      jSONObject2.put("native_requestInit", Long.valueOf(avt.b(requestTimin.getNativeRequestInitTime(), l4)));
                      jSONObject2.put("native_requestStart", Long.valueOf(avt.b(requestTimin.getNativeRequestStartTime(), l4)));
                      jSONObject2.put("native_requestSend", Long.valueOf(avt.b(requestTimin.getNativeRequestSendTime(), l4)));
                      jSONObject2.put("native_responseStart", Long.valueOf(avt.b(requestTimin.getNativeRequestResponseTime(), l4)));
                      jSONObject2.put("native_firstData", Long.valueOf(avt.b(requestTimin.getNativeRequestFirstDataTime(), l4)));
                      jSONObject2.put("native_responseEnd", Long.valueOf(avt.b(requestTimin.getNativeRequestEndTime(), l4)));
                      StatisticData tNetRequestS = requestTimin.getTNetRequestStatics();
                      if (vpw.commonConfig.D3 != null && tNetRequestS != null) {
                         hwj.d(tNetRequestS, new WVH5PPManager$2$1(oobject, jSONObject2));
                      }
                   }
                   cce webViewPageM = oobject.val$webview.getWebViewContext().getWebViewPageModel();
                   webViewPageM.onPropertyIfAbsent("H5_longestImageInfo", JSON.toJSONString(jSONObject2));
                   if (vpw.commonConfig.E3 != null) {
                      webViewPageM.onPropertyIfAbsent("H5_longestImageInfoV2", jSONObject2);
                   }
                   webViewPageM.onPropertyIfAbsent("H5_beforeSsrImageCount", Integer.valueOf(i3));
                }
             }
             return;
          }
       }else {
          return;
       }
    }
}
