package androidx.activity.compose.BackHandlerKt$BackHandler$backCallback$1$1;
import androidx.activity.OnBackPressedCallback;
import tb.xf40;
import tb.d1a;
import androidx.activity.compose.BackHandlerKt;
import java.lang.Object;

public final class BackHandlerKt$BackHandler$backCallback$1$1 extends OnBackPressedCallback	// class@000480 from classes.dex
{
    public final xf40 $currentOnBack$delegate;

    public void BackHandlerKt$BackHandler$backCallback$1$1(boolean p0,xf40 p1){
       this.$currentOnBack$delegate = p1;
       super(p0);
    }
    public void handleOnBackPressed(){
       BackHandlerKt.access$BackHandler$lambda$0(this.$currentOnBack$delegate).invoke();
    }
}
