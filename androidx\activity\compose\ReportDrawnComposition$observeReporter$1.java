package androidx.activity.compose.ReportDrawnComposition$observeReporter$1;
import tb.d1a;
import kotlin.jvm.internal.Lambda;
import kotlin.jvm.internal.Ref$BooleanRef;
import java.lang.Object;
import tb.xhv;
import java.lang.Boolean;

public final class ReportDrawnComposition$observeReporter$1 extends Lambda implements d1a	// class@000494 from classes.dex
{
    public final d1a $predicate;
    public final Ref$BooleanRef $reporterPassed;

    public void ReportDrawnComposition$observeReporter$1(Ref$BooleanRef p0,d1a p1){
       this.$reporterPassed = p0;
       this.$predicate = p1;
       super(0);
    }
    public Object invoke(){
       this.invoke();
       return xhv.INSTANCE;
    }
    public final void invoke(){
       this.$reporterPassed.element = this.$predicate.invoke().booleanValue();
    }
}
